---
description: Universal Code Construction Rules. Use when AI is asked to draft or manage code construction plans.
globs: 
alwaysApply: false
---
:clipboard: 施工文档结构模板
1. 顶部施工概览 (必需)
> **🚧 [项目/模块名称]代码施工方案 🚧**
>
> **施工等级**: 🔴/🟠/🟡 [MAJOR/MODERATE/MINOR] - [施工性质描述]
> **施工紧迫性**: [时间要求] - [不完成的后果]
> **工程量**: [X]个任务，涉及[Y]个文件，预计[Z]工时

2. 核心施工概览 (5分钟快速了解)
:bullseye: 施工目标: 用数字列表，每项说明具体目标和预期效果
:world_map: 施工策略: 使用Mermaid流程图展示施工路径
:high_voltage: 实施计划: 表格形式，包含阶段/优先级/任务数/关键里程碑/风险评估
:wrapped_gift: 预期产出: :white_check_mark: 格式的成果列表，量化收益

3. 上下文施工蓝图预留空间 (必需)
## 📋 **上下文施工蓝图预留空间**

### 当前代码结构图
[预留空间 - 现状架构图]
待填充: 当前代码组织关系图


### 目标代码结构图
[预留空间 - 目标架构图]
待填充: 施工后代码组织图


### 关键文件依赖图
[预留空间 - 依赖关系图]
待填充: 文件间依赖和影响关系

4. 详细施工分析
使用 :police_car_light: 标记最高优先级任务
用 :white_check_mark:新增/:counterclockwise_arrows_button:修改/:cross_mark:删除 标记明确操作
按技术层级组织 (基础设施层/业务逻辑层/接口层等)

5. 阶段性施工清单
使用checkbox格式: - [ ] **任务名称**
每个任务包含: 文件路径、操作类型、完成标准
按执行顺序编号阶段

:artist_palette: 格式规范
Emoji使用标准
| 用途 | Emoji | 含义 |
|---|---|---|
| 紧急任务 | :police_car_light: | 最高优先级施工 |
| 施工目标 | :bullseye: | 核心目标 |
| 施工策略 | :world_map: | 整体规划 |
| 实施计划 | :high_voltage: | 执行方案 |
| 预期产出 | :wrapped_gift: | 施工收益 |
| 新建文件 | :new_button: | 创建新代码 |
| 修改代码 | :counterclockwise_arrows_button: | 更新现有代码 |
| 删除代码 | :wastebasket: | 移除废弃代码 |
| 重构优化 | :zap: | 代码重构 |
| 测试验证 | :test_tube: | 测试相关 |
| 文档更新 | :memo: | 文档维护 |
| 配置调整 | :gear: | 配置文件 |
| 依赖管理 | :package: | 依赖处理 |

优先级标识
:red_circle: P0: 极高优先级 (阻塞性任务，必须首先完成)
:orange_circle: P1: 高优先级 (核心功能，影响主要特性)
:yellow_circle: P2: 中优先级 (重要改进，提升代码质量)
:green_circle: P3: 低优先级 (优化细节，可延后处理)

风险评估标准
极高:high_voltage:: 可能导致系统不可用或数据丢失
高:fire:: 影响核心功能，需要大量测试验证
中:yellow_square:: 影响局部功能，需要仔细处理
低:green_heart:: 优化改进，风险可控

表格格式要求
| 阶段            | 优先级 | 任务数 | 关键里程碑             | 风险评估 | 预计工时 |
| --------------- | ------ | ------ | ---------------------- | -------- | -------- |
| **[阶段名称]**  | [优先级] | [X]个   | [具体里程碑描述]       | [风险级别] | [X]小时   |

:memo: 内容要求
任务描述原则
操作明确: 使用动词开头，明确说明要做什么
路径具体: 包含完整的文件路径或代码位置
标准清晰: 包含可验证的完成标准
原因说明: 解释为什么要执行这个任务
影响评估: 说明对其他模块的潜在影响

代码规范要求
命名一致性: 统一的命名规范和风格
结构清晰: 合理的文件组织和模块划分
注释规范: 关键逻辑必须包含清晰注释
错误处理: 统一的错误处理和异常管理
测试覆盖: 核心功能必须包含相应测试

质量控制标准
### 代码质量检查清单
- [ ] **语法正确**: 代码能够正常编译/运行
- [ ] **逻辑完整**: 业务逻辑实现完整
- [ ] **异常处理**: 包含适当的错误处理
- [ ] **性能考虑**: 无明显性能问题
- [ ] **安全检查**: 符合安全编码规范
- [ ] **测试验证**: 通过相关测试用例
- [ ] **文档同步**: 相关文档已更新

:counterclockwise_arrows_button: 施工流程管控
施工前检查
### 施工准备检查清单
- [ ] **环境准备**: 开发环境配置正确
- [ ] **依赖确认**: 所需依赖已安装/更新
- [ ] **备份完成**: 重要代码已备份
- [ ] **权限验证**: 具备必要的操作权限
- [ ] **资源确认**: 时间和人力资源已分配

施工中监控
### 进度统计
- **总任务数**: [X]个任务 (+[Y]个新增任务)
- **已完成**: [完成数]/[总数] ([百分比]%)
- **进行中**: [进行数]/[总数] ([百分比]%)
- **待开始**: [待开始数]/[总数] ([百分比]%)
- **遇到问题**: [问题数]个 (详见问题跟踪)

### 阶段完成状态
- [ ] 阶段一：[名称] ([完成数]/[总数]) - [状态说明]
- [ ] 阶段二：[名称] ([完成数]/[总数]) - [状态说明]
- [ ] 阶段三：[名称] ([完成数]/[总数]) - [状态说明]

施工后验收
### 验收标准检查
- [ ] **功能验证**: 所有功能按预期工作
- [ ] **性能测试**: 性能指标达到要求
- [ ] **兼容性**: 与现有系统兼容
- [ ] **安全检查**: 通过安全审查
- [ ] **文档更新**: 相关文档已同步更新
- [ ] **部署就绪**: 可以安全部署到生产环境

:shield: 风险管控
风险识别与预防
### 高风险操作识别
- 🚨 **数据库结构变更**: [具体风险和预防措施]
- 🚨 **核心算法修改**: [具体风险和预防措施]
- 🚨 **第三方依赖升级**: [具体风险和预防措施]
- 🚨 **配置文件变更**: [具体风险和预防措施]

回滚方案
### 回滚策略
| 风险场景 | 触发条件 | 回滚步骤 | 预计用时 | 责任人 |
|----------|----------|----------|----------|--------|
| [场景1] | [条件] | [步骤] | [时间] | [人员] |
| [场景2] | [条件] | [步骤] | [时间] | [人员] |

:bar_chart: 施工监控与报告
日报模板
### 施工日报 - [日期]
**今日完成**:
- [任务1]: [状态] - [说明]
- [任务2]: [状态] - [说明]

**遇到问题**:
- [问题1]: [影响] - [解决方案]
- [问题2]: [影响] - [解决方案]

**明日计划**:
- [任务1]: [预期完成时间]
- [任务2]: [预期完成时间]

**风险提醒**:
- [风险点]: [影响程度] - [应对措施]

里程碑报告
### [里程碑名称] 完成报告
**完成时间**: [日期]
**完成质量**: [质量评估]
**关键成果**: 
- ✅ [成果1]
- ✅ [成果2]
**经验总结**:
- [经验1]
- [经验2]
**后续建议**:
- [建议1]
- [建议2]

:bullseye: 成功标准
文档质量检查
 新团队成员5分钟内能理解施工目标和方案
 所有任务都有明确的执行步骤和验收标准
 风险评估覆盖所有潜在问题
 预留空间为团队协作提供充足信息
 进度跟踪机制完整有效

技术标准检查
 符合项目既定的技术规范和编码标准
 代码结构清晰，可维护性良好
 测试覆盖率达到项目要求
 性能指标满足预期目标
 安全规范得到有效执行

交付标准检查
 所有计划功能均已实现并通过验证
 相关文档已同步更新
 部署和配置文档完整
 团队知识已有效传递
 后续维护方案明确

:counterclockwise_arrows_button: 文档维护
更新原则
版本控制: 每次重大更新增加版本号和变更日志
实时同步: 施工进度和发现的问题及时更新
决策记录: 重要技术决策和变更原因详细记录
知识沉淀: 经验教训和最佳实践及时总结

交接要求
完整性: 包含所有必要的技术细节和上下文信息
可理解性: 新接手人员能快速理解和继续工作
可追溯性: 每个决策和变更都有清晰的来龙去脉
可扩展性: 为未来的功能扩展和维护留有充分空间

归档标准
### 项目归档清单
- [ ] **源代码**: 完整的代码仓库和版本历史
- [ ] **文档资料**: 设计文档、API文档、用户手册等
- [ ] **配置文件**: 部署配置、环境配置等
- [ ] **测试资料**: 测试用例、测试报告、性能基准等
- [ ] **运维资料**: 部署指南、监控配置、故障处理手册等



