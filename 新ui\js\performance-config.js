// 性能配置和优化管理器
class PerformanceConfig {
    constructor() {
        this.settings = {
            // 基础性能设置
            enableAnimations: !window.matchMedia('(prefers-reduced-motion: reduce)').matches,
            enableParticles: true,
            enableMetors: true,
            enable3DEffects: true,
            
            // 自适应设置
            adaptiveQuality: true,
            maxFPS: 60,
            targetFPS: 60,
            
            // 设备性能检测
            deviceTier: this.detectDeviceTier(),
            
            // 网络状况
            connectionSpeed: this.detectConnectionSpeed(),
            
            // 电池状态
            lowPowerMode: false
        };
        
        this.performanceObserver = null;
        this.networkObserver = null;
        this.batteryObserver = null;
        
        this.init();
    }
    
    init() {
        this.setupPerformanceObserver();
        this.setupNetworkObserver();
        this.setupBatteryObserver();
        this.setupMediaQueries();
        this.applyOptimalSettings();
    }
    
    detectDeviceTier() {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        
        if (!gl) return 'low';
        
        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
        const renderer = debugInfo ? gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : '';
        
        // CPU核心数检测
        const cores = navigator.hardwareConcurrency || 4;
        
        // 内存检测
        const memory = navigator.deviceMemory || 4;
        
        // 综合评分
        let score = 0;
        if (cores >= 8) score += 30;
        else if (cores >= 4) score += 20;
        else score += 10;
        
        if (memory >= 8) score += 30;
        else if (memory >= 4) score += 20;
        else score += 10;
        
        if (renderer.toLowerCase().includes('nvidia') || 
            renderer.toLowerCase().includes('amd') ||
            renderer.toLowerCase().includes('intel iris')) {
            score += 40;
        } else if (renderer.toLowerCase().includes('intel')) {
            score += 20;
        }
        
        if (score >= 80) return 'high';
        if (score >= 50) return 'medium';
        return 'low';
    }
    
    detectConnectionSpeed() {
        if ('connection' in navigator) {
            const conn = navigator.connection;
            const speed = conn.downlink || 0;
            
            if (speed >= 10) return 'fast';
            if (speed >= 1.5) return 'medium';
            return 'slow';
        }
        return 'unknown';
    }
    
    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            this.performanceObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (entry.entryType === 'measure' && entry.duration > 16.67) {
                        this.handlePerformanceIssue(entry);
                    }
                });
            });
            
            try {
                this.performanceObserver.observe({ entryTypes: ['measure', 'navigation'] });
            } catch (e) {
                console.warn('Performance Observer not fully supported');
            }
        }
    }
    
    setupNetworkObserver() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            
            const updateConnectionInfo = () => {
                this.settings.connectionSpeed = this.detectConnectionSpeed();
                this.applyNetworkOptimizations();
            };
            
            connection.addEventListener('change', updateConnectionInfo);
            this.networkObserver = { connection, updateConnectionInfo };
        }
    }
    
    setupBatteryObserver() {
        if ('getBattery' in navigator) {
            navigator.getBattery().then(battery => {
                const updateBatteryInfo = () => {
                    this.settings.lowPowerMode = battery.charging === false && battery.level < 0.3;
                    this.applyPowerOptimizations();
                };
                
                battery.addEventListener('chargingchange', updateBatteryInfo);
                battery.addEventListener('levelchange', updateBatteryInfo);
                
                this.batteryObserver = { battery, updateBatteryInfo };
                updateBatteryInfo();
            });
        }
    }
    
    setupMediaQueries() {
        // 检测用户偏好
        const reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
        const reducedData = window.matchMedia('(prefers-reduced-data: reduce)');
        const darkMode = window.matchMedia('(prefers-color-scheme: dark)');
        
        reducedMotion.addListener((e) => {
            this.settings.enableAnimations = !e.matches;
            this.applyAccessibilitySettings();
        });
        
        reducedData.addListener((e) => {
            if (e.matches) {
                this.applyDataSavingMode();
            }
        });
        
        if (reducedData.matches) {
            this.applyDataSavingMode();
        }
    }
    
    applyOptimalSettings() {
        const tier = this.settings.deviceTier;
        
        switch (tier) {
            case 'high':
                this.settings.targetFPS = 60;
                this.settings.particleCount = 50;
                this.settings.enable3DEffects = true;
                break;
            case 'medium':
                this.settings.targetFPS = 45;
                this.settings.particleCount = 30;
                this.settings.enable3DEffects = true;
                break;
            case 'low':
                this.settings.targetFPS = 30;
                this.settings.particleCount = 15;
                this.settings.enable3DEffects = false;
                break;
        }
        
        this.notifySettingsUpdate();
    }
    
    applyNetworkOptimizations() {
        if (this.settings.connectionSpeed === 'slow') {
            // 减少网络请求
            this.settings.enableMetors = false;
            this.settings.particleCount = Math.min(this.settings.particleCount, 20);
        }
    }
    
    applyPowerOptimizations() {
        if (this.settings.lowPowerMode) {
            this.settings.targetFPS = 30;
            this.settings.particleCount = Math.min(this.settings.particleCount, 15);
            this.settings.enableMetors = false;
            this.settings.enable3DEffects = false;
        }
    }
    
    applyAccessibilitySettings() {
        if (!this.settings.enableAnimations) {
            this.settings.enableParticles = false;
            this.settings.enableMetors = false;
            this.settings.enable3DEffects = false;
        }
    }
    
    applyDataSavingMode() {
        this.settings.enableMetors = false;
        this.settings.particleCount = Math.min(this.settings.particleCount, 10);
        this.settings.enable3DEffects = false;
    }
    
    handlePerformanceIssue(entry) {
        console.warn(`Performance issue detected: ${entry.name} took ${entry.duration}ms`);
        
        // 自动降级性能设置
        if (this.settings.adaptiveQuality) {
            if (this.settings.particleCount > 10) {
                this.settings.particleCount = Math.max(10, this.settings.particleCount - 5);
            }
            
            if (this.settings.targetFPS > 30) {
                this.settings.targetFPS = Math.max(30, this.settings.targetFPS - 5);
            }
            
            this.notifySettingsUpdate();
        }
    }
    
    notifySettingsUpdate() {
        // 发送自定义事件通知设置更新
        const event = new CustomEvent('performanceSettingsUpdate', {
            detail: { ...this.settings }
        });
        window.dispatchEvent(event);
    }
    
    getSettings() {
        return { ...this.settings };
    }
    
    updateSettings(newSettings) {
        Object.assign(this.settings, newSettings);
        this.notifySettingsUpdate();
    }
    
    destroy() {
        if (this.performanceObserver) {
            this.performanceObserver.disconnect();
        }
        
        if (this.networkObserver) {
            this.networkObserver.connection.removeEventListener('change', this.networkObserver.updateConnectionInfo);
        }
        
        if (this.batteryObserver) {
            this.batteryObserver.battery.removeEventListener('chargingchange', this.batteryObserver.updateBatteryInfo);
            this.batteryObserver.battery.removeEventListener('levelchange', this.batteryObserver.updateBatteryInfo);
        }
    }
}

// 导出
if (typeof window !== 'undefined') {
    window.PerformanceConfig = PerformanceConfig;
}
