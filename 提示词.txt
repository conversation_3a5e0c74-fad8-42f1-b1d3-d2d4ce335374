你是⼀位资深WEB UI设计师，现需根据以下产品需求创建UI⽅案，最终⽣成HTML完整代码：

## 产品需求：
应用类型：Web应用 (多模态模拟面试评测智能体)

### 核⼼模块：

#### 一、用户认证与管理模块
功能描述：负责用户账户的创建、登录、密码管理以及首次使用的个性化引导，确保用户顺畅进入系统并根据偏好进行初步设置。
详细规格：
    ##### 1. 注册页面 (`/register`)
    *   **目的**: 新用户创建账户。
    *   **核心元素**:
        *   系统Logo和Slogan区域。
        *   标题："加入我们，开启智能面试提升之旅！"
        *   表单字段:
            *   邮箱 (兼作用户名，带格式验证)。
            *   昵称 (公开显示名，如社群)。
            *   密码 (带强度提示和确认密码字段)。
            *   (可选) 邀请码。
        *   "同意《用户协议》和《隐私政策》" 复选框 (链接到相应独立页面 `/terms-of-service` 和 `/privacy-policy`)。
        *   "注册" 按钮 (在同意协议后激活)。
        *   链接："已有账户？立即登录"。
        *   (可选) 第三方账号快速注册入口 (如微信、QQ，若集成)。
        *   (新增) **"游客/快速体验"入口**: 允许未注册用户体验部分核心功能。
    *   **交互**:
        *   实时输入校验 (邮箱格式、密码强度、昵称是否合规)。
        *   注册成功后，提示验证邮件已发送 (若开启邮箱验证)，或直接跳转至登录页/个性化引导页。

    ##### 2. 登录页面 (`/login`)
    *   **目的**: 用户登录系统。
    *   **核心元素**:
        *   系统Logo。
        *   标题："欢迎回来！"
        *   Tab切换：密码登录 / 验证码登录 / (App端提示)扫码登录。
        *   表单字段: 账号（手机号/邮箱/昵称）、密码/验证码。
        *   "记住我" 复选框。
        *   "登录" 按钮。
        *   链接："忘记密码？"、"还没有账户？免费注册"。
        *   (可选) 第三方登录入口。
    *   **交互**:
        *   登录成功后跳转至仪表盘或上次未完成的页面。
        *   错误提示 (如用户名或密码错误、账户锁定)。

    ##### 3. 忘记密码/重置密码页面 (`/forgot-password`, `/reset-password`)
    *   **目的**: 用户找回或重置密码。
    *   **核心元素**:
        *   `/forgot-password`: 输入注册手机号/邮箱 -> "发送验证码/重置链接" 按钮。
        *   `/reset-password`: 输入验证码，设置新密码，确认新密码。
    *   **交互**: 成功后提示操作成功，引导用户进行下一步（如查收邮件或直接登录）。

    ##### 4. （首次登录或可选）个性化引导与设置页面 (`/onboarding`)
    *   **目的**: 收集用户初始偏好，引导熟悉核心功能，突出系统价值。
    *   **核心元素** (分步式，界面友好，可跳过，后续可在帮助中心查看):
        *   **欢迎语与价值主张**: 简明介绍系统核心价值。
        *   **步骤1: 您的面试目标**
            *   "您目前主要关注的求职领域是？" (多选标签，如：后端开发、前端开发、数据科学、产品管理等，可自定义输入，AI根据用户学校专业初步推荐)。
            *   "您的目标岗位级别大致是？" (单选：实习生、初级、中级、高级)。
            *   "近期是否有明确的目标公司或岗位？" (可选文本输入：公司名称；岗位JD链接或文本粘贴区域)。
            *   "您当前的求职阶段是？" (单选：如"大一/大二探索期"、"大三/研一准备期"、"大四/研二冲刺期"、"已毕业求职期" - 不同阶段引导内容和推荐功能差异化)。
        *   **步骤2: 主要求职痛点自评**
            *   多选常见痛点 (如"缺乏面试经验"、"简历优化难"、"行业知识不足"、"表达能力弱"、"心理紧张")。
            *   (新增) 痛点选择后可展示一个简易的痛点雷达图，并可与同类学生平均水平匿名对比。
        *   **步骤3: 个性化体验设置**
            *   "AI教练沟通风格偏好：" (单选卡片：鼓励型、严谨分析型、启发提问型、幽默型、Socratic提问型 - 可预览风格特点)。
            *   "面试前放松引导：" (开关：默认开启，可选择放松引导类型)。
            *   "面试中轻度情绪提示：" (开关：默认关闭，可设置强度、类型、反馈方式如文字/震动(App))。
            *   "是否愿意接收社群精选内容？" (开关)。
            *   "是否愿意匿名参与'面试伙伴'社群，与他人交流学习？" (开关：默认开启，并简述匿名保护机制)。
        *   **步骤4: 核心功能一览与操作提示**
            *   简短图文或动画介绍：模拟面试、AI教练、职业发展导航、专项练习、匿名社群的核心功能入口和价值。 (可跳过)
        *   **(可选) 引导上传第一份简历**: "上传您的简历，让AI更好地了解您，为您提供更精准的面试辅导。"
        *   "完成设置，开始探索！" 按钮。
    *   **交互**: 用户选择实时保存。完成后跳转至仪表盘。

#### 二、仪表盘/首页模块
功能描述：作为用户进入系统的核心入口，提供关键信息概览、常用功能快速访问和个性化内容推荐，帮助用户快速开始或继续其面试提升之旅。体现"千人千面的AI求职规划师"理念。
详细规格：
    ##### 仪表盘/首页 (`/dashboard`)
    *   **目的**: 用户进入系统的核心入口，提供高度个性化的关键信息概览、常用功能快速访问和智能推荐。
    *   **核心元素**:
        *   **顶部导航栏**:
            *   系统Logo。
            *   全局搜索框 (可搜索面试场景、历史记录、自定义题目、社群帖子、学习资源等)。
            *   核心功能快捷入口 (图标按钮：模拟面试、AI教练、职业发展导航)。
            *   通知中心图标 (铃铛，有未读消息时显示红点，内容包括AI教练新消息、导师新反馈、学习计划提醒、社群互动等)。
            *   用户头像下拉菜单 (指向：个人中心、我的偏好设置、帮助与反馈、退出登录)。
        *   **主区域布局** (采用卡片式、模块化布局，内容根据用户的求职阶段、目标岗位、近期活动、薄弱环节动态展示最相关的内容和行动建议，用户可自定义部分顺序或显隐):
            *   **欢迎与快速开始/行动建议区域 (高度个性化)**:
                *   "Hi, [用户昵称]！" + AI生成的当日/本周核心行动建议 (如："今天来挑战一场[推荐场景]面试吧！" 或 "针对您上次的薄弱点，AI教练建议您先完成[专项练习X]")。
                *   "开始新的模拟面试" (主操作按钮，非常醒目)。
                *   (若有上次未完成的面试或未查看的反馈) "您上次的[场景名称]面试反馈已生成，点击查看" 或 "继续上次未完成的面试"。
                *   (若有面试表现预测) "AI预测您在[目标面试]中可能在[方面]遇到挑战，建议提前准备[相关练习/知识点]" (非侵入式提示)。
            *   **我的职业发展导航摘要卡片 (高度个性化)**:
                *   标题："我的成长导航" (链接至完整导航页 `/career-navigation/dashboard`)。
                *   显示当前核心目标 (如"目标：高级Java工程师")。
                *   AI生成的1-2条当前最关键的技能提升建议、推荐学习资源/专项练习 (如 "建议学习[课程A]以提升您的[技能X]")。
                *   简易进度条展示已规划学习路径的完成度或下一个小目标的进度。
            *   **近期活动与提醒卡片**:
                *   "最近面试表现趋势" (迷你折线图展示近3-5次面试总分，可点击进入面试历史)。
                *   "AI教练有新的跟进建议" (若AI教练有主动推送或用户上次对话未结束，点击进入对话)。
                *   "您关注的[社群标签/话题]有X条新动态" (点击进入社群)。
                *   "待办事项提醒": (如未完成的面试、新反馈通知、导师新反馈、学习计划今日任务)。
            *   **核心功能入口卡片组 (根据用户阶段和使用频率动态排序或突出显示)**:
                *   **模拟面试**: 图标 + "进行全真模拟" -> 面试场景选择中心 (`/interviews/explore`)。
                *   **AI面试教练**: 图标 + "与教练深度复盘" -> AI教练交互页面 (`/coach/`)。
                *   **职业发展与求职辅助**: 图标 + "规划我的成长路径" -> 职业发展导航首页 (`/career-navigation/dashboard`)。
                *   **专项技能练习**: 图标 + "针对性提升短板" -> 专项练习中心 (`/practice/items`)。
                *   **我的面试历史**: 图标 + "回顾所有面试记录" -> 面试历史列表页 (`/profile/interview-history`)。
                *   **我的自定义题库**: 图标 + "管理我的专属题目" -> 自定义题库管理页 (`/profile/custom-questions`)。
                *   **"面试伙伴"社群**: 图标 + "加入社群匿名交流" -> 社群首页 (`/community/home`)。
            *   **游戏化与成就概览卡片**:
                *   显示当前等级、经验值进度条、连续活跃天数、虚拟货币余额。
                *   最近获得的1-2个成就徽章。
                *   "每日任务"快捷入口 (如 "今日任务：完成1场行为面试")。
                *   "查看我的成就与激励" 链接至 `/gamification-hub`。
            *   **(可选) 积极心理与面试韧性中心入口卡片**: 图标 + "学习放松与应对技巧" -> `/wellbeing`。
    *   **交互**:
        *   各卡片内容高度动态更新，提供千人千面的个性化信息和行动引导。
        *   所有链接和按钮清晰指向相应功能模块。
        *   仪表盘布局支持响应式，适应不同屏幕尺寸。

#### 三、模拟面试模块
功能描述：提供从面试场景选择、面试准备、进行到分析的完整流程，用户可以选择不同面试模式，进行高度拟真和沉浸式的音视频面试，系统将采集多模态数据并进行智能分析。
详细规格：
    ##### 1. 面试场景选择中心 (`/interviews/explore`)
    *   **目的**: 用户浏览、筛选、搜索和选择各种类型的模拟面试场景，或发起AI定制面试。
    *   **核心元素**:
        *   **顶部Banner/推荐区**: 展示最新场景、热门场景、"今日推荐" (基于用户画像、历史数据、目标岗位、近期学习与浏览记录)、"限时挑战场景"。
        *   **模式选择Tabs/Cards**:
            *   **A. 标准场景面试**:
                *   场景列表 (卡片式设计，包含场景名称、目标岗位/领域、难度星级、预计时长、参与人数、用户好评率、标签云、"官方认证"/"企业合作"标识)。点击卡片进入场景详情页。
                *   高级筛选器 (多维度组合筛选：技术领域、岗位类型、行业（含新兴行业）、公司类型、难度（含炼狱模式）、面试语言、场景类型（技术/行为、群面、压力、英文、无领导小组、案例、辩论式、即兴演讲）、高校特色场景（保研、考研复试、学生干部、奖学金答辩、学术报告预演）、"排除已面试"选项，支持保存常用筛选条件)。
                *   关键词搜索框 (支持场景名、岗位名、技能点搜索、支持语音搜索)。
            *   **B. 自定义题库面试**:
                *   入口，引导用户选择个人已创建的自定义题库或小组共享题库，或现场勾选题目组合进行面试。
            *   **C. AI生成专项面试**:
                *   引导式表单：
                    *   输入目标岗位名称/关键词 (AI联想补全)。
                    *   选择/输入期望考察的核心技能点 (可从技能库选择、AI根据岗位推荐技能)。
                    *   (可选) 上传/粘贴目标公司JD文本。
                    *   (可选) 选择AI面试官风格、难度、面试官性格特点 (如注重细节、结果导向)。
                    *   (可选) 定制多轮追问逻辑或角色扮演需求。
                *   AI生成题目列表预览、可微调（替换、增删题目、调整题目顺序和预计回答时长）、预估时长。
                *   "保存为我的场景"选项。
            *   **D. 简历/JD专项分析面试**:
                *   引导用户选择已上传的简历或即时上传/粘贴简历内容。
                *   上传/粘贴岗位JD文本 (支持直接输入招聘网站JD链接)。
                *   AI进行匹配度分析（可视化展示匹配亮点与潜在差距、给出提升匹配度的行动建议，JD真实性/质量初步评估和优化建议）。
                *   AI生成针对性的面试题目列表预览与调整。
                *   "开始这场定制面试"按钮。
            *   **E. (新增) "挑战名企真题"专区**:
                *   收录部分合作企业或公开的经典面试题目/流程，用户可选择挑战。
                *   可查看往期高分匿名作答（需授权）作为参考。
            *   **F. (新增) "高校特色面试专区"**:
                *   保研、考研复试、学生干部、奖学金答辩等场景入口。
            *   **G. (新增) "面试官视角"体验入口**:
                *   用户扮演面试官，对AI模拟的候选人进行提问和评价，可选不同风格AI面试者。
            *   **H. (新增) 其他特色场景入口**: "跨文化交流"、"道德困境与决策"等。
    *   **交互**:
        *   用户可以方便地找到或创建适合自己的面试。AI推荐和生成功能提供个性化体验。

    ##### 2. 场景详情页面 (`/interviews/scenario/{scenario_id}`)
    *   **目的**: 用户在选择标准场景后，查看该场景的详细信息并决定是否开始。
    *   **核心元素**:
        *   场景标题、创建者/来源、更新时间、版本号。
        *   详细描述、目标人群、考察能力维度详解 (能力维度可点击查看详细释义和考察标准)。
        *   难度、预计时长、语言。
        *   典型问题示例与解析、建议准备方向、推荐学习资源链接。
        *   用户评价与讨论区 (可匿名、官方答疑区)。
        *   "开始面试准备" / "收藏场景" / "分享场景" / "加入我的学习计划" 按钮。
    *   **交互**: 用户充分了解场景信息后进入面试准备流程。

    ##### 3. 面试准备页面 (`/interview/prepare`) (承接场景选择或AI生成后的设置与检测)
    *   **目的**: 用户完成面试前的最后设置和设备检测。
    *   **核心元素**:
        *   标题："准备开始 "[场景/面试名称]""
        *   **面试设置**:
            *   "选择AI面试官风格" (下拉菜单，可选：标准、友善型、压力型、行业专家型等，有文字描述其特点，部分场景可能根据选择的"面试官性格模拟器"进阶标签预设)。
            *   "选择AI面试官虚拟形象" (若支持，提供可选2D/3D形象，或关闭形象仅语音，或提示可上传自定义形象)。
            *   "开启面试前放松引导" (复选框，根据用户偏好默认勾选/不勾选，可选择放松引导类型如正念呼吸、积极心理暗示、1分钟小游戏)。
            *   (若场景需要或用户选择) "开启屏幕共享功能" (授权提示)。
            *   (若技术支持且用户选择) "开启高级行为特征分析" (如视线追踪、微表情，需明确授权，告知数据用途和隐私保护，提供详细数据处理说明链接)。
        *   **设备检测与授权**:
            *   摄像头预览区域 (可选择摄像头设备、虚拟背景设置)。
            *   麦克风音量实时指示条 (可选择麦克风设备、噪音抑制开关)。
            *   网络状况检测 (显示延迟、带宽评估、抖动、丢包率，弱网提示)。
            *   背景环境检测 (如光线充足度、背景复杂度、杂音等级评估与改进建议，一键优化建议如调整摄像头角度)。
            *   明确的音视频录制、(若开启)屏幕共享、(若开启)高级行为特征分析授权请求提示。
        *   "一切就绪，开始面试！" 按钮 (有3秒倒计时准备，可设置倒计时时长或跳过，在所有必要选择、授权和设备检测通过后激活)。
    *   **交互**:
        *   引导式流程，每一步清晰明了。
        *   设备检测失败时提供故障排除提示。

    ##### 4. (可选) 面试前放松引导页面/弹窗
    *   **目的**: 在正式面试开始前帮助用户调整心态。
    *   **核心元素**:
        *   引导动画/视频/音频 (如正念呼吸练习、舒缓音乐背景、积极心理暗示语录滚动、快速眼动放松引导、1分钟小游戏)。
        *   可跳过。
    *   **交互**: 完成或跳过后进入模拟面试进行中界面。

    ##### 5. 模拟面试进行中界面 (`/interview/live/{interview_id}`)
    *   **目的**: 模拟真实面试过程，采集用户多模态数据，提供分支剧情式、沉浸式、高度拟真的互动体验。
    *   **核心元素**:
        *   **主视频区域**: 清晰展示用户摄像头画面 (可拖动、缩放、画中画模式)。
        *   **虚拟面试官区域/题目显示区域**:
            *   (可选) AI虚拟形象 (根据用户选择或场景设定，可有简单表情和动作配合)。
            *   当前面试题目文本 (清晰展示，支持调整字体大小)。
            *   AI追问或澄清性问题动态显示 (问题会根据用户回答实时调整，体现分支剧情)。
            *   (若为技术面试或行业工具实操模拟) **在线代码编辑器/模拟软件操作区**:
                *   代码输入区、运行按钮、输出结果区、语言选择。
                *   或简化的Figma、SQL查询界面等，要求用户完成特定任务。
        *   **计时器与进度**:
            *   当前题目已用时 / (可选)建议回答时长。
            *   总面试已用时 / 预计总时长。
            *   面试进度条 (第N题/共M题，或动态进度提示)。
        *   **状态与辅助提示区**:
            *   录制中状态、麦克风音量指示、网络状况实时指示。
            *   当前面试模式提示 (如压力模式、压力递增模式)。
            *   **(可选，非侵入式情绪/行为提示)**: 若用户开启，微动画、颜色变化或预设关键词短句（如"深呼吸"、"眼神交流"）进行温和提示，或特定关键词触发的鼓励语。用户可设置面试结束后统一查看此类提示。
        *   **交互控制区**:
            *   "回答完毕，下一题" / "提交" 按钮。
            *   "重复问题" / "请AI面试官说慢一点" / "给我一点时间思考" 按钮 (AI会有相应反馈)。
            *   "跳过此题" 按钮 (会被记录)。
            *   "标记此题" 按钮 (用于后续重点复盘)。
            *   "卡壳求助" / "请求提示" 按钮 (显示剩余次数，可选择提示类型：关键词、思路方向、相关知识点，会被记录)。
            *   (若启用屏幕共享) "开始/停止共享屏幕"按钮、选择共享窗口/应用。
            *   (若为无领导小组) 角色分配显示、个人发言计时器、小组总计时器。
            *   "暂停面试"按钮 (有次数限制，记录暂停时长)。
            *   "结束本次面试" 按钮 (有二次确认)。
        *   **(新增) 实时字幕（可选）**: 用户语音转文字实时显示，辅助表达和回顾。(支持编辑实时字幕中的错别字，仅用于个人记录，不影响AI评分原始数据)。
        *   **(新增) 临时笔记区（可选，不参与评分）**: 用户可快速记录关键词或思路。(笔记可关联到当前题目，面试后统一查看)。
    *   **交互**:
        *   界面简洁，聚焦于用户和题目，最大程度减少干扰。
        *   流畅的题目切换和状态反馈，AI的反应和追问体现智能和动态性。
        *   结束后跳转至"分析中"过渡页，然后是反馈报告。来电/通知有合理暂停机制。

    ##### 6. 面试分析中过渡页面 (`/interview/processing/{interview_id}`)
    *   **目的**: 告知用户系统正在处理数据，提供等待预期。
    *   **核心元素**:
        *   动态加载动画 (如进度条、雷达扫描动画)。
        *   提示文字："正在对您的面试表现进行多维度智能分析，请稍候..." 或 "AI正在努力为您生成深度反馈报告..."
        *   显示分析步骤摘要 (如：语音识别中... 语义理解中... 行为特征分析中（若开启）... 报告生成中...)。
        *   (可选) 根据本次面试类型推荐相关阅读文章或专项练习。
    *   **交互**: 分析完成后自动跳转到反馈报告界面。

#### 四、面试反馈模块
功能描述：向用户全面、清晰、可交互地展示AI对面试表现的分析结果，包括多维度能力评估、行为分析、情绪波动及个性化改进建议，并支持音视频回放与AI标注，最终生成可行动的复盘笔记。
详细规格：
    ##### 1. 面试反馈报告界面 (`/feedback/{interview_id}`)
    *   **目的**: 向用户全面、清晰、可交互地展示面试表现的AI分析结果和改进建议。
    *   **核心元素**:
        *   **报告头部**:
            *   面试标题 (如 "[场景名称] - [日期] 的模拟面试反馈")。
            *   **总体得分** (百分制或等级制，显著突出) 与 **AI生成的一句话核心评语/亮点总结**。
            *   **本次面试难度与用户表现匹配度评估** (如：难度适中，表现优秀/仍有提升空间)。
            *   "与AI教练深入探讨此报告" (主行动按钮，携带报告上下文跳转至AI教练模块)。
            *   "下载PDF版报告" (可选择包含/排除某些模块，如详细行为分析)、"分享报告" (可设有效期、密码、查看权限如仅对方可见AI评语不含原始视频)、"再进行一次同类面试"、"生成复盘笔记初稿" 按钮。
        *   **导航/目录 (页面较长时)**: 快速跳转到报告各部分 (可收起/展开)。
        *   **多维度能力评估 (可交互雷达图或柱状图)**:
            *   展示用户在各项核心能力（如语言表达、逻辑思维、问题解决、岗位匹配度、情绪稳定性、学习能力、求职动机、行为仪态）及特定指标（如说服力、批判性思维）上的得分。
            *   可切换对比对象（如"我的平均水平"、"目标岗位要求"、"我的历史最佳"、"同校/同专业平均水平（匿名）"）。鼠标悬浮或点击各项指标可显示简要定义和得分说明。
        *   **详细指标解读与行为分析 (可折叠/Tab切换的区域)**:
            *   对每个评测指标：
                *   **指标名称与得分**。
                *   **AI生成的详细文字评语**: 解释得分依据，引用用户回答原文进行佐证并高亮关键句。
                *   **针对错误回答或不足之处，给出标准答案参考或更优表达方式建议**。
                *   **"亮点时刻"与"提升关键点"片段列表**: 行为描述 (如"回答流利"、"出现卡顿"、"眼神交流佳/回避"、"表达模糊/清晰"、"情绪紧张/放松表现"、"使用填充词过多"、"回答逻辑跳跃") + 时间戳 + (可选)用户对应文本片段。点击可联动下方播放器。
        *   **交互式音视频回放与分析**:
            *   内嵌音视频播放器，带时间轴。时间轴上标记AI识别的关键事件点 (不同颜色/图标区分正面/负面/中性事件、用户主动标记的点)。
            *   播放速度控制、字幕开关、全屏播放、逐句精听/跟读模式。
            *   **(新增) AI自动标注的关键行为片段**: 高亮显示时间点，点击可跳转。
            *   **(新增) 语音语调分析可视化**: 如语速变化曲线、音量变化、停顿分布、音高变化（反映情绪），与播放器同步。
            *   **(新增) 关键词提取与分析**: 用户回答中出现的高频词、与JD相关的关键词、积极/消极词汇、行业术语使用准确性。
            *   **(新增) 行为标注层**: 可在视频上叠加显示AI对特定行为（如手势、眼神、微表情若开启）的初步解读或提示。
            *   **(新增) 屏幕共享内容回顾 (若有)**: 可回放屏幕共享的录像，并结合AI分析操作流程的效率和准确性。
        *   **(可选)情绪波动与压力应对分析**:
            *   情绪变化曲线图 (积极/消极情绪随时间变化，基于语音、面部表情初步判断)。
            *   关键情绪节点解读，情绪恢复能力评估。
        *   **(可选)简历/JD匹配度分析结果**:
            *   若面试基于此生成，逐条分析JD要求与用户在面试中展现的相关经验/技能的匹配情况。对未充分展示的匹配点给出具体建议。
        *   **"模拟面试官视角点评"**: 从面试官角度出发，给出对候选人的整体感受和录用意愿的初步模拟判断（强调非真实录用结果），并指出可能让面试官产生疑虑的方面。
        *   **"本次面试亮点复盘"**: 总结用户在本次面试中的突出表现和闪光点，给予积极反馈，并分析这些亮点如何迁移到其他面试中。
        *   **个性化改进建议与行动计划**:
            *   结构化列出"待改进项"，AI根据重要性和用户短板程度进行优先级排序，并预估改进所需时间与难度。
            *   针对每个待提升点，提供具体、可操作的改进方法、练习建议。
            *   **智能链接到系统内的专项练习模块、相关学习资源、社群中类似问题的讨论帖、AI教练的特定辅导主题** (可一键加入学习计划)。
        *   **"面试复盘笔记"自动生成初稿**: 基于面试内容和AI分析，生成结构化的复盘笔记框架，用户可在此基础上修改和补充，并导出。
        *   **(新增) AI评测透明度与局限性说明区域**: 简要说明AI评测的原理、数据依据、当前技术的局限性，引导用户正确看待评测结果。
        *   **(新增) 用户反馈入口**: "您对本次报告满意吗？" / "我有疑问或建议" (可针对报告特定部分提问)。
    *   **交互**:
        *   报告内容丰富但结构清晰，易于导航和理解。
        *   核心亮点是可交互的音视频片段回放与AI标注，让用户直观感知自身表现。
        *   所有建议和链接都应是可操作的，引导用户进行下一步提升。

    ##### 2. 面试历史列表页面 (`/profile/interview-history`) (隶属于个人中心)
    *   **目的**: 用户查看、筛选和管理自己所有的模拟面试记录，并进行趋势分析。
    *   **核心元素**:
        *   标题："我的面试历史"。
        *   列表/卡片展示历次面试记录 (场景名称、面试日期、总得分、主要标签、面试时长、面试官风格)。
        *   筛选功能 (按场景类型、时间范围、得分区间、面试官风格)。
        *   "趋势分析"入口: 选择多次面试记录后，可查看能力项得分变化趋势图、与目标岗位差距变化趋势。
        *   操作：点击查看完整反馈报告、(可选)批量导出报告摘要。
    *   **交互**: 用户可以方便地追踪自己的进步和反复出现的问题点。

#### 五、AI教练模块
功能描述：提供用户与AI面试教练的多轮、上下文感知对话交互功能。用户可以针对面试反馈进行提问，AI教练将提供个性化的指导、回答策略建议、知识点讲解、求职技能辅导，并支持模拟追问、文件讨论和目标设定。
详细规格：
    ##### 1. AI教练交互界面 (`/coach/{session_id_or_new}`)
    *   **目的**: 用户与AI教练进行对话，深度复盘面试，获取个性化指导、策略、知识和情感支持。
    *   **核心元素**:
        *   **标准聊天界面布局**:
            *   **左侧面板 (PC端)**: 对话会话列表 (按时间倒序，可标星、重命名、打标签，可搜索)。
            *   **右侧主聊天区域**:
                *   顶部显示当前AI教练的"人设"/风格/当前辅导主题 (如"鼓励型教练小爱"、"简历优化助手"、"压力面试突破教练"、"英文面试口语教练")。 (可选) 更换教练风格/模式的按钮。
                *   消息气泡形式展示用户和AI教练的发言 (区分左右，带头像和时间戳)。
                *   AI教练的回复支持Markdown格式 (列表、加粗、链接、代码块高亮与一键复制)。AI生成的内容中可包含可点击的系统内链接 (如指向某专项练习、反馈报告某部分、学习资源)。
                *   支持引用回复功能：用户或AI可针对某条具体消息进行回复。
                *   AI生成的计划、清单等内容，可以卡片形式展示，支持一键采纳或修改。
            *   **底部输入区域**:
                *   多行文本输入框 (支持Shift+Enter换行、输入时AI智能联想常用提问)。
                *   "发送"按钮 (或Enter发送)。
                *   (可选) 语音输入按钮 (语音输入后可编辑文字)。
                *   附件上传按钮 (支持简历、Cover Letter、JD文档、图片格式、行业研究报告片段等)。
                *   "引用反馈报告中的内容"按钮: 快速引用当前正在讨论的面试报告中的片段。
                *   "快速提问"模板按钮: 预设一些常用提问 (如"帮我分析这个问题的回答思路"、"我该如何改进这个缺点？"、"帮我分析一下这个JD"、"模拟一次关于XX的追问")。
        *   **上下文感知与智能提示/快捷操作区**:
            *   当从特定反馈报告跳转过来时，AI教练的首条消息应主动提及该报告或用户可能关心的问题点。
            *   AI根据当前对话内容，动态推荐相关问题或操作（如"能详细解释一下XX点吗？"、"帮我制定一个针对XX的练习计划"、"将这个建议加入我的学习计划"）。
            *   快捷指令按钮 (如"总结对话"、"保存摘要"、"清空当前记忆，开始新话题"、"设定短期辅导目标")。
        *   **功能按钮/菜单 (聊天框附近)**:
            *   查看/关联当前讨论的面试报告。
            *   对AI的回复进行评价（赞/踩、提供更详细的反馈标签如"回答不准确"、"很有帮助"、"过于啰嗦"），帮助模型优化。
            *   导出当前对话记录 (Markdown或PDF)。
    *   **交互**:
        *   流畅的多轮对话体验，AI回复支持流式输出。
        *   用户可以方便地回顾对话历史，AI能理解上下文并进行深入讨论。
        *   AI的建议具有高度个性化和可操作性，能引导用户深度思考、制定计划、进行模拟练习（如特定追问、角色扮演）。支持用户随时暂停，向AI教练请教应对策略后再继续模拟。
        *   支持就上传文件（简历、JD）进行讨论和获取反馈。
        *   AI可协助设定辅导目标（如"一周内优化自我介绍"）并追踪进度。
        *   提供特定对话模式如"模拟群面策略讨论"、"情绪疏导与积极心理建设"。

    ##### 2. AI教练会话历史页面 (`/profile/coach-sessions`) (隶属于个人中心)
    *   **目的**: 用户回顾和管理与AI教练的历史对话。
    *   **核心元素**:
        *   标题："我的AI教练会话"。
        *   列表展示历史对话会话 (包含会话标题/主题/重命名、最后更新时间、AI生成的会话摘要、会话标签)。
        *   搜索会话记录 (支持按关键词、标签、时间范围搜索)。
        *   操作：点击进入特定会话回顾与继续、删除会话。
    *   **交互**: 方便用户查找和继续之前的辅导内容。

#### 六、专项技能练习模块
功能描述：提供针对特定面试技能点的微型、聚焦练习场景，用户可以选择不同练习项目，进行多样化的针对性训练（如知识点问答、STAR故事构建、即兴演讲、逻辑挑战、英文口语、行为面试作答、看图说话、电梯演讲、术语辨析、复杂概念解释等），并获得AI的即时、聚焦反馈、具体得分、改进点、优秀范例，以及游戏化激励。支持错题本、练习闯关和每日一练。
详细规格：
    ##### 1. 练习中心/选择页面 (`/practice/items`)
    *   **目的**: 用户发现、筛选和选择合适的专项技能进行练习。
    *   **核心元素**:
        *   标题："专项技能提升营" 或 "面试技能加油站"。
        *   **顶部"每日一练"卡片**: 系统每天推送一个简短的专项练习 (用户可自定义"每日一练"的技能方向和难度)。
        *   **技能分类筛选**: Tab或侧边栏导航 (如：沟通表达类、逻辑思维类、STAR法则应用、技术概念阐述、行为面试题应对、压力面试技巧、非语言沟通、数据敏感性、创新思维等，可按用户薄弱技能智能排序)。支持按练习形式（文本、语音、交互选择）筛选。
        *   **练习列表/卡片**:
            *   每个练习以卡片形式展示：练习名称、图标、简短描述、所属技能分类、难度级别 (初/中/高)、预计用时、用户平均得分/完成率、"推荐"/"热门"标识。
            *   支持关键词搜索练习。
        *   **"AI为你推荐"区域**: 根据用户最近的面试反馈或职业发展导航中的短板，动态推荐2-3个最相关的练习。
        *   **快捷入口**: "我的练习计划" (若与职业发展模块联动)、"已收藏练习"、"我的错题本"。
        *   **(可选) "练习排行榜"**: 按练习完成数量、平均分等，可匿名。
        *   **(可选) "练习闯关模式/系列练习"入口**: 将相关的微练习组织成进阶式的练习包。
    *   **交互**: 用户可以快速找到适合自己的练习，AI推荐和排行榜增加参与度。

    ##### 2. 单个专项练习进行中界面 (`/practice/item/{item_id}/do`)
    *   **目的**: 用户完成具体的专项练习任务，支持多种交互形式。
    *   **核心元素**:
        *   界面布局根据练习类型动态调整，保持简洁和专注。
        *   **通用元素**:
            *   清晰的练习标题和任务指引/问题描述/考察目标说明。
            *   用户输入/交互区域:
                *   文本输入框 (如STAR故事的各部分，AI可能提供实时辅助提示)。
                *   语音录制控件与波形显示 (如即兴演讲、英文口语跟读)。
                *   选择题选项 (单选/多选/不定项，如知识点快问快答)。
                *   简单交互选择 (如拖拽排序、连线匹配，如逻辑思维挑战)。
                *   代码编辑器 (若为编程类练习，如专业术语应用)。
            *   计时器 (若练习限时)。
            *   "提交答案" / "完成练习" / "查看提示" (若练习支持) / "跳过此题" (若为系列练习) 按钮。
        *   **示例特定元素**:
            *   STAR故事构建器: S, T, A, R四个独立文本框，AI对构建的故事进行实时反馈和优化建议。
            *   即兴演讲/表达练习: AI对演讲内容、流畅度、逻辑性进行评价。
            *   逻辑思维挑战: 提供多种解题思路参考。
            *   英文口语跟读与发音评测: 针对面试常用词汇和句型。
            *   行为面试高频问题专项作答: 提供不同回答角度的AI范例。
    *   **交互**: 练习过程引导清晰，交互方式符合练习目标，支持用户高效完成任务。

    ##### 3. 专项练习即时反馈界面 (`/practice/record/{record_id}/feedback`)
    *   **目的**: 用户查看练习结果和AI的针对性反馈，并进行后续操作。
    *   **核心元素**:
        *   练习提交后立即显示。
        *   **结构化反馈**:
            *   总体评价 (如"优秀"、"良好"、"有待提升"、具体得分/星级)。
            *   针对练习目标的具体评分/AI文字评语 (聚焦单一技能点，指出优点、改进点、优秀表现点)。
            *   (若适用) **优秀回答示例/AI范例/参考答案对比**。
            *   AI改进建议和技巧提示。
        *   **游戏化元素**: 显示本次练习获得的经验值、成就点、徽章碎片等。
        *   **行动按钮**: "再来一次"、"挑战下一关" (若为系列练习)、"查看同类其他练习"、"将问题加入我的题库"、"返回练习中心"、"收藏此练习"、"加入错题本"。
    *   **交互**: 反馈及时、具体、可操作，帮助用户快速迭代并引导后续学习。

    ##### 4. 我的专项练习记录页面 (`/profile/practice-records`) (隶属于个人中心)
    *   **目的**: 用户回顾和管理自己的专项练习历史和表现。
    *   **核心元素**:
        *   标题："我的专项练习记录"。
        *   列表/日历视图展示已完成的专项练习。
        *   每条记录显示：练习名称、完成时间、得分/评价、所用时长。
        *   筛选和排序功能 (按技能标签、得分高低、练习日期等)。
        *   点击可查看详细反馈。
        *   练习统计：总练习次数、平均分、各技能练习分布图等。
    *   **交互**: 方便用户追踪练习进度和效果。

#### 七、职业发展与求职辅助模块
功能描述：基于用户的面试表现、职业目标、技能画像及行业趋势，提供智能化的职业发展规划、技能提升路径、精选学习资源推荐、行业洞察、简历优化建议、求职资讯日历、模拟Offer决策及校友网络对接等功能，赋能用户进行长期职业成长和高效求职。
详细规格：
    ##### 1. 职业发展导航首页/仪表盘 (`/career-navigation/dashboard`)
    *   **目的**: 用户设定、查看和管理自己的职业发展蓝图，获取个性化发展建议和资源，作为"我的成长导航"驾驶舱。
    *   **核心元素**:
        *   标题："我的职业发展蓝图" 或 "我的成长导航"。
        *   **我的职业目标设定区域**:
            *   清晰显示用户当前设定的短期/长期目标 (目标岗位、行业、公司类型、期望发展城市)。
            *   支持从热门职业库选择，或AI根据用户简历/兴趣/专业/性格测试结果推荐。
            *   目标可分解为阶段性小目标，并设置提醒。
            *   "修改目标" 或 "添加新的发展目标" 链接。
        *   **综合能力概览 (个性化，基于多维度数据)**:
            *   与当前首要目标的技能匹配度总览 (如百分比、评级)。引用具体案例说明差距。
            *   我的核心优势技能标签云。
            *   待提升的关键技能TOP3 (AI识别，并说明与目标的差距，提供与同校/同专业/同目标岗位人群的匿名技能对比)。
            *   技能提升进度概览。
            *   (新增) **"智能画像对比与定位"区域**: 用户可选择对标画像（如"目标公司同岗位平均水平"、"已成功入职学长学姐画像（匿名聚合）"），系统从能力、经验、准备度等多维度进行对比分析图表。
        *   **AI推荐学习路径/计划卡片列表**:
            *   每个计划以卡片形式展示 (如 "产品经理能力提升计划"、"沟通与表达能力强化月")。
            *   卡片上显示计划名称、核心技能标签、预计完成时间、当前进度。
            *   路径包含推荐的学习资源、系统内的专项练习、建议参与的面试场景、建议参加的校内外活动或实习类型、建议关注的行业专家或资讯。
            *   点击卡片进入计划详情页。学习路径支持用户自定义调整和添加个人任务。
        *   **快捷入口**: "AI生成新发展建议"、"探索技能图谱"、"查看行业洞察"、"优化我的简历"、"查看求职日历"、"校园招聘专区"。
    *   **交互**: 信息架构清晰，用户能方便地设定目标、查看规划、获取资源。推荐内容可互动反馈，实现动态优化。

    ##### 2. 学习计划详情页 (`/career-navigation/plan/{plan_id}`)
    *   **目的**: 用户查看具体的学习计划步骤和推荐资源，并追踪进度。
    *   **核心元素**:
        *   计划名称和总体目标，关联的职业目标。
        *   分阶段/模块化学习步骤:
            *   每个步骤：学习主题、核心知识点、预计用时、重要性/优先级标记。
            *   **推荐资源列表 (卡片)**: 针对每个步骤，精准推荐2-4个学习资源 (在线课程链接、行业报告、技术博客、专业书籍推荐、校友/导师推荐资源、开源项目推荐、行业会议/研讨会信息)。
                *   资源卡片：标题、类型图标（视频/文章/课程）、来源、预计学习时长、推荐理由/用户评价。
                *   操作： "开始学习/查看资源" (跳转)、"标记为已学习"、"收藏"、"不感兴趣"、"评价资源"。
            *   **推荐系统内专项练习链接/面试场景链接**。
        *   学习进度可视化 (如甘特图或步骤完成勾选框)。
        *   用户个人笔记区 (针对此学习计划)。
        *   "将此计划加入我的学习日历" / "寻找学习搭子(可选，匿名匹配，组建线上学习小组)" 按钮。
        *   计划完成后的成就徽章或奖励预览。
    *   **交互**: 用户可以清晰地了解学习路径，方便地获取资源并管理学习进度。

    ##### 3. (可选) 交互式技能图谱探索页 (`/career-navigation/skill-map`)
    *   **目的**: 用户通过可视化图谱探索行业技能和发展路径。
    *   **核心元素**:
        *   以可视化、可交互的图谱形式展示行业技能结构。
        *   高亮用户已掌握技能、目标岗位所需技能、AI推荐的学习路径节点、以及用户在图谱中自行标注的目标技能。
        *   用户可点击图谱节点查看技能详情：技能定义、重要性、相关岗位、推荐学习资源、前置/后置技能、与此技能相关的热门职位列表。
    *   **交互**: 探索式学习，帮助用户理解技能关联和职业发展方向。

    ##### 4. 行业洞察与岗位解读页面 (`/career-navigation/insights`)
    *   **目的**: 用户获取行业最新动态、人才需求趋势及岗位详细信息。
    *   **核心元素**:
        *   标题："行业洞察与岗位解读"。
        *   行业/岗位筛选器。
        *   资讯卡片列表 (AI聚合分析的行业动态、人才需求、薪酬范围参考、新兴职业介绍与前景分析；标题、摘要、来源、发布日期、阅读量/点赞数)。
        *   岗位详情页 (点击资讯或搜索进入)：常见岗位的职责描述、发展前景、所需核心技能解读、典型职业发展路径案例、该岗位典型一天的工作内容描述。
    *   **交互**: 用户可以便捷地了解行业和岗位信息，辅助职业决策。

    ##### 5. 求职资讯与活动日历页面 (`/career-navigation/events`)
    *   **目的**: 用户发现和管理相关的求职资讯和活动。
    *   **核心元素**:
        *   标题："求职资讯与活动日历"。
        *   日历视图/列表视图展示活动 (整合高校就业指导中心信息、合作企业招聘宣讲会、在线求职讲座、大型招聘会、行业峰会、技能竞赛等)。
        *   活动筛选 (类型、地点、主办方、是否线上)。
        *   活动详情弹窗 (时间、地点、内容、报名链接、"添加到我的日历")。
        *   用户可订阅感兴趣的资讯类别和活动提醒。
    *   **交互**: 帮助用户一站式获取求职活动信息并进行管理。

    ##### 6. 简历智能优化建议页面 (`/career-navigation/resume-optimizer`)
    *   **目的**: 用户上传简历，获取AI提供的初步优化建议。
    *   **核心元素**:
        *   标题: "简历智能优化"。
        *   简历上传区域 (或选择已在"我的简历库"中的简历)。
        *   (可选) 目标岗位JD粘贴区域，用于对比分析。
        *   AI分析结果展示区：
            *   格式规范性建议。
            *   关键词匹配度分析 (对比目标JD)。
            *   内容完整性检查。
            *   表达专业性与动词使用建议。
            *   STAR原则应用情况评估。
            *   针对不同目标岗位生成不同版本的优化建议。
        *   "导出优化建议" 或 "将建议发送给AI教练讨论" 按钮。
    *   **交互**: 用户快速获得简历的初步反馈，引导进一步优化。

    ##### 7. 模拟Offer决策页面 (`/career-navigation/offer-comparator`)
    *   **目的**: 辅助用户在获得多个Offer时进行对比决策。
    *   **核心元素**:
        *   标题："模拟Offer决策助手"。
        *   引导用户输入多个（模拟或真实）Offer的关键信息 (公司、职位、薪资构成、福利、地点、已知优缺点、发展前景等)。
        *   用户可设定个人偏好 (如薪资优先、发展优先、WLB优先)。
        *   AI进行结构化对比，并从用户预设的偏好角度给出分析。
        *   提供可视化对比图表 (如雷达图、条形图)。
    *   **交互**: 帮助用户更理性地分析和选择Offer。

    ##### 8. 校友网络对接引导页 (`/career-navigation/alumni-connect`) (需学校合作与校友授权)
    *   **目的**: 为学生提供联系已毕业校友咨询行业或公司情况的平台。
    *   **核心元素**:
        *   标题："校友网络"。
        *   说明规则和隐私保护政策。
        *   提供搜索/筛选校友的入口 (基于行业、公司、毕业年份等，需校友授权显示信息)。
        *   站内信或预约沟通功能 (需双方同意)。
    *   **交互**: 在规则内促进校友间的经验分享和信息传递。

    ##### 9. (可选) 校园招聘专区 (`/career-navigation/campus-recruitment`) (需合作)
    *   **目的**: 智能匹配校园招聘岗位，提供内推通道。
    *   **核心元素**:
        *   标题: "校园招聘"。
        *   岗位列表 (根据用户简历、技能画像、求职意向精准匹配的校招岗位)。
        *   岗位详情页。
        *   (若企业开放) "一键内推"或"优先筛选申请"按钮。
    *   **交互**: 简化校招信息获取和申请流程。

#### 八、"面试伙伴"匿名社群模块
功能描述：提供一个安全的匿名环境，用户可以使用系统生成的匿名代号（可自定义风格头像）在社群发帖、评论、提问、投票、参与悬赏和打卡小组。AI辅助内容摘要、关键词提取、情绪识别和风险过滤，促进互助学习和经验分享。支持创建和参与模拟面试小组及技能分享Workshop。
详细规格：
    ##### 1. 社群首页/帖子列表 (`/community/home` or `/community/posts`)
    *   **目的**: 用户浏览、筛选、搜索社群帖子，发现感兴趣的内容和互动。
    *   **核心元素**:
        *   **顶部Banner/公告区**: 社群规则、热门活动、"社群名人堂"入口。
        *   **左侧板块/话题导航**: 如"面试经验分享"、"行业交流"、"岗位解惑"、"简历互助"、"每日打卡"、"模拟面试小组招募"、"Offer选择求助"、"学习资源共享"、"高校专区"、"技能分享Workshop"。 (用户可自定义关注的板块)。
        *   **Tab筛选**: 最新回复、最新发布、热门、精华、我关注的标签/用户、"待解答悬赏" (按悬赏金额排序)。
        *   "发布新帖" 按钮 (显眼，浮动或固定位置)。
        *   **帖子列表 (卡片式或列表式)**:
            *   帖子标题、AI生成摘要/核心观点。
            *   匿名作者代号 (可显示其等级/徽章/"认证辅导员"等特殊身份标识，使用风格化匿名头像)、发布时间、所属板块/标签。
            *   评论数、点赞数、"有价值"标记数、悬赏金额 (若有)。
            *   帖子类型图标 (如"经验"、"提问"、"求助"、"分享"、"投票"、"招募"、"Workshop")。
            *   点击帖子标题或摘要进入帖子详情页。
    *   **交互**: 界面简洁友好，易于浏览和参与。匿名机制贯穿始终。

    ##### 2. 帖子详情页 (`/community/post/{post_id}`)
    *   **目的**: 用户查看帖子完整内容并参与互动（评论、点赞、感谢、采纳答案）。
    *   **核心元素**:
        *   完整帖子内容 (AI处理和排版优化，支持Markdown、代码高亮、图片/视频嵌入)。
        *   匿名作者信息栏 (代号、头像、等级、特殊身份标识)。
        *   互动按钮: 点赞、收藏、分享、"有价值"标记、"感谢作者" (可附带少量虚拟币)。
        *   **(若为提问帖且有悬赏) 悬赏信息展示和状态**。
        *   **(若为投票帖) 投票选项和结果展示**。
        *   **评论列表**:
            *   匿名评论者信息、评论内容 (AI处理后，支持嵌套回复、支持对评论点赞)、时间、点赞数。
            *   (若为提问帖) 提问者可"采纳为最佳答案" (可采纳多个答案并分配悬赏)。
        *   **评论输入框**: 支持富文本、@他人（匿名代号）、插入表情包。 (AI进行初步审核和内容处理)。
    *   **交互**: 鼓励有价值的互动和高质量内容的产生。

    ##### 3. 发布新帖页面 (`/community/new-post`)
    *   **目的**: 用户创建并发布新的社群帖子、提问、投票等。
    *   **核心元素**:
        *   选择帖子类型 (普通帖、提问帖、投票帖、小组招募帖、Workshop发起帖等)。
        *   选择所属板块。
        *   标题输入框。
        *   内容输入框 (支持Markdown的富文本编辑器，提供常用格式按钮、支持从本地或AI教练处导入内容)。
        *   标签选择/输入区域 (AI推荐标签)。
        *   匿名发帖选项与提示 (强调社群规范，系统生成或用户自选匿名代号和头像风格)。
        *   (若为提问帖) 设置悬赏金额和有效期。
        *   (若为投票帖) 设置投票选项和截止日期。
        *   "发布"按钮 (发布前AI进行内容审核、摘要、关键词提取、情绪倾向初步识别和风险内容预警与过滤)。
    *   **交互**: 引导用户发布合规且有价值的内容。

    ##### 4. 我的社群动态 (`/profile/community-activity`) (隶属于个人中心)
    *   **目的**: 用户追踪和管理自己在社群的活动和收获。
    *   **核心元素**:
        *   标题："我的社群动态"。
        *   我发布的帖子列表 (及状态)。
        *   我的评论/回复列表。
        *   我收藏的帖子/关注的用户列表。
        *   我收到的赞/回答被采纳的通知。
        *   我获得的感谢/悬赏记录。
    *   **交互**: 方便用户回顾个人社群贡献和互动。

    ##### 5. 模拟面试小组招募/管理页面 (`/community/groups`)
    *   **目的**: 用户创建或加入模拟面试练习小组，进行协同练习。
    *   **核心元素**:
        *   标题："模拟面试小组"。
        *   创建/搜索面试练习小组 (可按目标岗位、学校、练习频率筛选)。
        *   小组信息页：目标岗位、练习频率、成员列表（匿名代号）、小组简介和规则。
        *   小组内部讨论区。
        *   小组共享文件区（如共享面经、自定义题目）。
        *   小组练习日历和预约功能。
    *   **交互**: 促进用户间的协作学习和实战演练。

    ##### 6. 学习打卡小组页面 (`/community/study-groups`)
    *   **目的**: 用户创建或加入学习打卡小组，互相监督，共同进步。
    *   **核心元素**:
        *   标题："学习打卡小组"。
        *   创建/加入学习打卡小组 (可按学习目标、打卡周期筛选)。
        *   小组信息页：学习目标、打卡周期、规则、成员列表。
        *   成员打卡记录展示墙、互相鼓励和评论区。
    *   **交互**: 增强学习的持续性和社群的凝聚力。

    ##### 7. (可选) 技能分享Workshop页面 (`/community/workshops`)
    *   **目的**: 用户发起或参与由其他用户组织的技能分享会。
    *   **核心元素**:
        *   标题: "技能分享工坊"。
        *   Workshop列表 (主题、发起人匿名代号、时间、参与方式、费用（若有，虚拟货币）)。
        *   Workshop详情页 (详细介绍、议程、发起人背景、报名入口)。
        *   发起新Workshop表单 (主题、描述、时间、形式、费用等)。
    *   **交互**: 促进知识共享和深度交流。

#### 九、个人中心与设置模块
功能描述：用户管理个人账户信息（头像、昵称、密码、第三方绑定）、简历、技能标签、学生认证、导师关联；查看各类面试、AI教练、专项练习、职业发展、社群活动、游戏化成就等记录；进行系统偏好设置（AI教练风格、面试设置、通知、隐私）；访问帮助与反馈、关于我们等支持信息。
详细规格：
    ##### 个人中心与设置界面 (`/profile` 或 `/settings`)
    *   **目的**: 用户管理个人信息、查看各类记录、进行系统偏好设置，作为各项个性化数据的汇总入口。
    *   **核心元素** (通常采用侧边栏导航 + 右侧内容区布局):
        *   **侧边栏导航**:
            *   **仪表盘/首页**: 链接至 `/dashboard`。
            *   **账户信息 (`/profile/info`)**:
                *   头像上传/修改、昵称修改。
                *   基础信息：性别、年龄、学校、专业、年级、学历 (学校/专业自动联想与标准化)。
                *   求职意向：目标行业、岗位、城市、期望薪资范围、实习/全职偏好 (AI根据表现和市场趋势推荐调整)。
                *   技能标签：用户自行填写或AI提取用户确认，技能熟练度自评与AI评估对比。
                *   学生身份认证入口与状态显示 (可选上传学生证/学信网截图，认证后享特定功能，状态与有效期管理)。
            *   **我的简历库 (`/profile/resumes`)**:
                *   上传/管理多份简历（隐私简历，可设置对AI开放程度）。
                *   简历解析状态、预览、AI智能命名与标签化管理、版本管理与智能命名建议。
                *   设置默认简历。
                *   简历对比功能。
            *   **账户安全 (`/settings/security`)**:
                *   修改密码、绑定/解绑手机号/邮箱、可选二次验证、异地登录提醒与设备管理。
            *   **偏好设置 (`/settings/preferences`)**:
                *   AI教练风格选择 (预览不同风格特点)。
                *   面试相关设置: 面试前放松引导开关及类型选择、面试中情绪提示（强度、类型、反馈方式如语音/文字/震动(App)、是否结束后统一查看）、默认面试时长偏好。
                *   通知设置: 精细化设置各类通知的接收方式（站内信、邮件、App推送、微信/QQ服务号）。
                *   隐私设置: 个人信息展示范围、匿名数据使用授权、面试记录分享权限管理（可设分享给特定用户组如学习小组、与导师/学校共享数据的精细化设置）。
            *   **我的面试历史**: 链接至 `/profile/interview-history`。
            *   **我的AI教练会话**: 链接至 `/profile/coach-sessions`。
            *   **我的专项练习记录**: 链接至 `/profile/practice-records` (含错题本入口)。
            *   **我的自定义题库**: 链接至 `/profile/custom-questions` (含共享题库管理)。
            *   **我的职业发展中心 (`/profile/career-plan`)**:
                *   汇总用户的职业目标、所有已生成/自定义的学习计划、整体学习进度。
                *   简历智能优化建议入口 (链接至 `/career-navigation/resume-optimizer`)。
                *   "我的技能档案": 详细记录用户各项技能的来源（面试、练习、简历提取等）、评估结果和相关证明。
            *   **我的社群动态**: 链接至 `/profile/community-activity`。
            *   **我的游戏化与激励**: 链接至 `/gamification-hub`。
            *   **我的导师/生涯顾问 (`/profile/mentors`)**:
                *   申请关联导师的入口（输入导师认证ID或邮箱）。
                *   已关联导师列表及状态。
                *   分享设置：选择性分享面试报告、成长数据给导师。
                *   导师反馈集中查看与回复区。
            *   **支持与信息**:
                *   帮助与支持中心: 链接至 `/help`。
                *   新手引导中心: 链接至 `/welcome-guide`。
                *   用户反馈与建议: 链接至 `/feedback` or `/suggestions`。
                *   关于我们: 链接至 `/about`。
                *   版本更新日志: 链接至 `/release-notes`。
                *   服务协议: 链接至 `/terms-of-service`。
                *   隐私政策: 链接至 `/privacy-policy`。
                *   AI伦理与数据透明度: 链接至 `/ai-ethics`。
            *   **退出登录**。
        *   **右侧内容区**: 根据左侧选择显示对应模块的内容。每个子页面都有自己的标题和核心元素。
    *   **交互**:
        *   导航清晰，各项设置和信息查找方便。
        *   用户对其数据和偏好有充分的控制权。
        *   各个子页面功能丰富，交互流畅。

#### 十、积极心理与面试韧性模块 (新增)
功能描述：提供多元化的面试前放松与准备工具（正念冥想、积极暗示、场景预演、减压游戏、准备清单），面试中非侵入式积极赋能提示，面试后情绪分析与积极重塑引导，以及情绪日记、成功案例分享、抗压挑战和感恩成就记录等功能，全面提升用户面试心理素质。
详细规格：
    ##### 1. 积极心理与面试韧性中心 (`/wellbeing` or `/mental-fitness`)
    *   **目的**: 用户获取和使用各类心理调适工具与资源，提升面试韧性。
    *   **核心元素**:
        *   标题："积极心理与面试韧性中心"。
        *   **放松与准备工具库 (卡片式或列表式)**:
            *   **正念冥想引导**: 音频/视频播放列表 (可筛选时长、主题如"缓解紧张"、"增强自信"、"考前专注力提升"，支持用户上传自定义冥想音乐)。
            *   **积极心理暗示语录库**: 用户可收藏、自定义、AI根据用户近期表现生成个性化暗示语，支持轮播展示。
            *   **快速场景预演工具**: AI引导用户快速想象面试成功画面，建立积极心态。
            *   **面试清单检查与提醒模板**: 用户可自定义清单内容（如物理环境、设备、个人状态）。
            *   **"减压小游戏"专区入口**: 提供一些科学设计的快速减压小游戏。
        *   **情绪日记功能**:
            *   简单的日记编辑与情绪标签选择 (支持语音输入日记)。
            *   (可选) AI对日记内容的简单情感分析与鼓励性回应。
            *   情绪变化趋势图（基于日记，可关联到特定面试事件）。
        *   **学习与分享资源区**:
            *   关于面试心理调适、压力管理的科普文章、短视频、专家访谈。
            *   认知行为疗法（CBT）的简单技巧介绍与练习。
            *   "面试成功案例分享"与"学长学姐经验谈"匿名分享墙 (支持视频、音频，可按行业/岗位筛选)。
        *   **"抗压能力小挑战"专区**:
            *   一系列小型互动练习或情景模拟列表，模拟面试中可能遇到的意外情况或压力提问，挑战后提供AI点评和应对策略建议。
        *   **"感恩日记"与"成就记录"板块**:
            *   引导用户记录积极事件和已达成的小成就，培养积极心态。
    *   **交互**: 提供一个宁静、正向的界面氛围。各项工具易于访问和使用。

    ##### 2. 面试前最终准备检查页面/弹窗 (在`/interview/prepare`点击"开始面试"后，正式进入`/interview/live`前)
    *   **目的**: 确保用户在面试开始前的最后一刻状态最佳。
    *   **核心元素**:
        *   标题："最后确认，轻松上阵！"
        *   清单式检查：摄像头/麦克风正常？网络稳定？背景整洁？手机静音？喝水准备？着装得体？面试资料备齐？ (用户可勾选确认)。
        *   快速放松引导入口（如1分钟呼吸练习、积极自我对话提示卡片）。
        *   "我准备好了，开始面试！"按钮。
    *   **交互**: 简洁明了，给用户积极的心理暗示。

#### 十一、游戏化与激励模块 (新增)
功能描述：通过经验值(XP)、虚拟货币、等级、称号、徽章、连续活跃、排行榜、每日/每周任务、虚拟道具商城、成长树/宠物、学习契约和互助悬赏等机制，全面激励用户积极参与各项活动（模拟面试、专项练习、学习、社群互动、完善资料、签到等），提升用户粘性和成就感。
详细规格：
    ##### 1. 我的游戏化成就与激励中心 (`/gamification-hub` or `/profile/achievements_rewards`)
    *   **目的**: 用户集中查看和管理自己的游戏化数据、成就、任务和相关活动。
    *   **核心元素**:
        *   标题："我的成就殿堂" 或 "游戏化中心"。
        *   **概览区**:
            *   当前等级与称号 (如 "面试新星 Lv.5")、经验值XP进度条 (显示距下一级所需XP)。
            *   虚拟货币余额 (如 "面霸币: 1200")。
            *   连续活跃天数及阶段性奖励提示。
            *   (若有合作) 学分/第二课堂认证信息展示。
        *   **徽章墙**:
            *   动态展示已解锁徽章 (图标精美、名称、描述、获得时间、稀有度)。
            *   可筛选和排序徽章，可设置为头像挂件。
        *   **任务中心 (Tab切换或独立区域)**:
            *   "每日任务"列表 (如"完成一场特定类型面试"、"在社群回答3个问题"、"学习一个新技能点")，显示任务进度、奖励，提供"去完成"和"领取奖励"按钮。
            *   "每周挑战"列表 (同上，但任务更复杂，奖励更丰厚)。
            *   已完成/已过期任务列表。
        *   **排行榜 (可选，需用户授权)**:
            *   可切换不同榜单 (如XP总榜、徽章收集榜、特定活动榜)。
            *   显示自己的排名和前后用户（匿名），我的历史最高排名。
            *   (若有赛季制) 当前赛季信息和结束倒计时，赛季奖励预览。
        *   **"我的学习契约"管理区**:
            *   创建新的学习契约 (设定目标、完成期限、投入虚拟货币"押金")。
            *   查看进行中/已完成/已失败的契约列表及状态。
            *   (新增) 创建/加入团队版学习契约。
        *   **快捷入口**: "虚拟道具商城"、"成长树/宠物养成" (若启用)。
    *   **交互**: 界面充满成就感和激励元素，各项数据清晰展示，操作便捷。

    ##### 2. 虚拟道具商城页面 (`/store`)
    *   **目的**: 用户使用通过各种行为获得的虚拟货币兑换增值服务或个性化物品。
    *   **核心元素**:
        *   标题："道具商城"。
        *   **商品分类**: 如"面试辅助" (额外提示次数、AI教练简历精修服务)、"个性装扮" (头像框、主题皮肤)、"特权服务" (解锁特定面试场景)、"学习加速" (补签卡、经验加速卡、排行榜积分加成卡)。
        *   **商品卡片/列表**:
            *   道具图标、名称、价格（虚拟货币）、功能描述/效果预览、剩余库存/限购次数、用户评价。
            *   "购买"按钮。
        *   **用户虚拟货币余额显示**。
        *   **"我的背包"入口**: 查看已拥有但未使用的道具。
        *   (可选) 购物车、订单记录。
        *   (可选) 限时优惠、兑换码输入区域。
    *   **交互**: 浏览和购买体验流畅，道具价值明确。

    ##### 3. (可选) 求职副本挑战列表页面 (`/gamification/challenges`)
    *   **目的**: 用户选择并参与系列化的求职任务挑战。
    *   **核心元素**:
        *   标题: "求职副本挑战"。
        *   副本列表 (卡片形式): 副本名称 (如 "字节跳动产品岗一面通关")、主题、难度等级、预计耗时、奖励预览。
        *   副本详情页: 详细介绍、包含的任务步骤 (学习任务、技能练习、模拟面试等)、Boss战描述。
        *   "开始挑战" 或 "组队挑战" 按钮。
    *   **交互**: 引导用户进行有目标、有剧情的系列化提升。

    ##### 4. (可选) 成长树/宠物养成界面 (嵌入到`/gamification-hub`或作为弹窗/独立小模块)
    *   **目的**: 提供趣味化的视觉反馈，增加用户持续动力。
    *   **核心元素**:
        *   可爱的成长树或宠物虚拟形象。
        *   用户的学习行为 (如完成面试、练习) 可以"浇水"、"喂食"，促进形象成长。
        *   成长到特定阶段可解锁特殊奖励或外观变化。
    *   **交互**: 简单、轻松、正反馈强。

#### 十二、支持与信息模块 (新增)
功能描述：提供系统帮助文档、图文/视频教程、常见问题解答(FAQ)、智能客服、新手引导、版本更新日志、用户反馈渠道，以及隐私政策、用户协议、AI伦理声明和关于我们等重要信息。
详细规格：
    ##### 1. 帮助与支持中心 (`/help` or `/support`)
    *   **目的**: 用户获取帮助信息，解决使用中遇到的问题。
    *   **核心元素**:
        *   标题："帮助与支持中心"。
        *   **搜索框**: 搜索帮助文档和FAQ。
        *   **FAQ分类浏览**: 如账号问题、面试功能、报告解读、社群规则、付费与订阅（若有）、AI评测说明等。每个FAQ条目点击可展开答案。
        *   **热门问题列表**。
        *   **视频教程专区**: 按功能模块分类的简短视频教程。
        *   **智能客服/帮助精灵入口**: 悬浮窗或独立页面，用户可输入问题，AI尝试解答或引导至相关帮助文档/FAQ，(可选)支持转人工客服。
        *   帮助文档支持版本控制和用户反馈。
    *   **交互**: 信息查找便捷，解答清晰。

    ##### 2. 新手引导中心页面 (`/welcome-guide`)
    *   **目的**: 帮助新用户快速了解和上手系统的核心功能和价值。
    *   **核心元素**:
        *   标题："欢迎来到[产品名]！快速上手指南"。
        *   分步骤的功能介绍和操作演示 (图文或短视频形式，如如何开始一场模拟面试、如何使用AI教练、如何设置职业目标等)。
        *   常见使用场景引导 (如"我想提升面试表达能力" -> 引导至专项练习；"我对某行业不了解" -> 引导至行业洞察)。
        *   核心价值快速体验入口。
    *   **交互**: 引导清晰，易于理解，降低新用户使用门槛。

    ##### 3. 用户反馈与建议页面 (`/feedback` or `/suggestions`)
    *   **目的**: 用户提交对产品的问题反馈、功能建议或体验改进意见。
    *   **核心元素**:
        *   标题："反馈与建议"。
        *   反馈表单：
            *   问题类型选择 (如Bug报告、功能建议、体验问题、内容错误等)。
            *   详细描述输入框。
            *   (可选) 截图/文件上传。
            *   联系方式 (可选，若用户希望得到回复)。
        *   "提交反馈"按钮。
        *   (可选) 公开的建议看板：展示其他用户提交的建议（经审核），其他用户可投票顶起重要建议。
    *   **交互**: 提交方便，鼓励用户参与产品改进。

    ##### 4. 版本更新日志页面 (`/release-notes`)
    *   **目的**:告知用户产品的最新更新、功能改进和问题修复。
    *   **核心元素**:
        *   标题："版本更新日志"。
        *   按版本号/日期倒序列出更新条目。
        *   每个版本包含：新功能介绍、功能改进说明、已修复问题列表。
        *   对重要更新提供视频演示或图文解读链接。
        *   (可选) 用户可对新功能投票或提建议的快速入口。
    *   **交互**: 信息透明，帮助用户了解产品迭代。

    ##### 5. 关于我们页面 (`/about`)
    *   **目的**: 展示产品理念、团队、联系方式及合作伙伴等信息。
    *   **核心元素**:
        *   标题："关于我们"。
        *   产品理念与愿景。
        *   (可选) 团队介绍。
        *   联系方式 (客服邮箱、电话等)。
        *   (可选) 合作伙伴Logo墙。
        *   (可选) 产品荣誉与媒体报道。
        *   (可选) 产品发展路线图（Roadmap）的简要展示。
    *   **交互**: 简洁明了，传递品牌信息。

    ##### 6. 隐私政策页面 (`/privacy-policy`)
    *   **目的**: 详细说明系统如何收集、使用、存储和保护用户数据。
    *   **核心元素**:
        *   标准法律文本格式。
        *   提供易读版摘要，突出核心要点。
    *   **交互**: 文本清晰可读，易于访问。

    ##### 7. 用户协议页面 (`/terms-of-service`)
    *   **目的**: 明确用户在使用产品时的权利与义务。
    *   **核心元素**:
        *   标准法律文本格式。
        *   关键条款高亮或提供通俗解释。
    *   **交互**: 文本清晰可读，易于访问。

    ##### 8. AI伦理与数据透明度页面 (`/ai-ethics`)
    *   **目的**: 解释AI评测原理、数据匿名化处理、用户数据控制权、AI的局限性以及如何处理AI偏见。
    *   **核心元素**:
        *   以通俗易懂的语言解释相关概念。
        *   图文结合，增强可理解性。
        *   强调用户数据安全与隐私保护承诺。
    *   **交互**: 内容专业且易懂，提升用户信任感。

## 技术规格：
*   单个HTML文件，包含所有上述功能页面的预览。每个页面预览以画板形式横向排列，每行最多6个画板，可以有多行。
*   画板尺⼨：1440x900像素（带1px描边模拟浏览器窗口）；设计应考虑响应式，确保在常见桌面分辨率和主流平板设备上有良好体验。移动端体验可作为次级目标单独优化。
*   必须包含：
    *   ⽮量图标系统（使⽤SVG `<symbol>` 定义，或通过CSS类名引用SVG图标）。
    *   动态折线图（例如在仪表盘的"最近面试表现趋势"或反馈报告的情绪曲线中使用，可考虑使用`stroke-dasharray`动画或JS图表库实现）。
    *   卡⽚悬浮效果（例如，鼠标悬停在功能入口卡片、练习卡片、帖子卡片上时，通过`box-shadow`变化和轻微上浮`transform: translateY`实现）。
    *   图⽚占位使⽤`<image>`标签或`<img>`标签，外链Unsplash等版权友好图库的图片（例如 `https://source.unsplash.com/random/WIDTHxHEIGHT?query=KEYWORD`）。

## 新视觉⻛格：
*   **玻璃拟态背景 (Glassmorphism)**：
    *   在特定区域（如卡片背景、侧边栏、模态框）使⽤半透明磨砂玻璃质感。
    *   背景内容通过`backdrop-filter: blur(Xpx);`实现模糊效果。
    *   搭配柔和的内阴影、外阴影以及边缘高光，营造层次感和精致感。
*   **低饱和配⾊**：
    *   主⾊调采⽤温暖的⽶⽩⾊ (例如 `#F8F5F2` 或 `#FAF7F5`) 或非常浅的灰色作为整体背景。
    *   搭配深⾊⽂字（如深灰 `#333333` 或 `#4A4A4A`）保证阅读性。
    *   辅以明亮且不过于刺眼的橙⾊ (例如 `#FF7A59` 或 `#FFA07A`) 或蓝色 (例如 `#4A90E2`) 作为点缀⾊和强调色（用于按钮、图标、链接、重要提示等）。
    *   整体配⾊方案追求高级感、宁静感与科技感，避免使用过多高饱和度色彩。
*   **极简字体排版**：
    *   主要使⽤⼤字号的现代⽆衬线字体（如 思源⿊体 (Source Han Sans CN), Roboto, Poppins, Inter）。
    *   信息层次通过字体⼤⼩ (例如，标题 `24px-36px`，正文 `14px-16px`，辅助文字 `12px`)、粗细 (`font-weight`: `300` Light, `400` Regular, `500` Medium, `600` SemiBold, `700` Bold) 与⾊彩（主信息深色，次要信息中灰色，点缀色用于链接或特定强调）来明确区分。
    *   保持足够的行高 (`line-height`: `1.5` 到 `1.8`) 提升阅读舒适度。
*   **表单优化**：
    *   表单输⼊框、下拉选择框等控件去除传统边框线。
    *   仅保留流畅的圆⻆背景 (例如 `border-radius: 8px-12px`)，背景色可为浅灰色或米白色，获得焦点时可有轻微描边或阴影变化。
    *   标签文字放置在输入框上方或作为占位符（获得焦点时上移）。
    *   减少视觉噪⾳，提升整体界⾯的简洁与精致感。
*   **交互动效**：
    *   按钮与可交互卡⽚加⼊"呼吸感"动效：鼠标悬停 (hover) 或激活 (active) 时，可有细微的阴影变化、透明度变化或背景色渐变。
    *   同时赋予轻微悬浮感：hover时元素微微上浮 (`transform: translateY(-2px); box-shadow: ...;`) 并伴有平滑过渡 (`transition: all 0.3s ease-out;`)。
    *   页面切换或主要内容加载时，可考虑使用优雅的淡入淡出或骨架屏效果。
    *   动效应保持克制，以提升用户体验和高级感为目的，避免过度花哨。

请基于上述优化后的提示词，设计出符合要求的Web端高品质UI方案，并最终⽣成完整的HTML代码。





