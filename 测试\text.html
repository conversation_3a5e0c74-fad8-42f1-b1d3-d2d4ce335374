<!DOCTYPE html>
<html>
<head>
    <title>学校社团管理系统设计</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #444;
            margin-top: 20px;
        }
        .answer {
            margin-left: 20px;
            margin-bottom: 30px;
        }
        .code {
            font-family: Consolas, monospace;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        .note {
            font-style: italic;
            color: #666;
        }
        .entity {
            font-weight: bold;
        }
        .relation {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <h1>学校社团管理系统设计</h1>

    <h2>(1) E-R图设计</h2>
    <div class="answer">
        <p class="note">注：由于HTML限制，无法直接绘制E-R图，以下是E-R图的文字描述：</p>
        
        <p><span class="entity">实体：</span></p>
        <ul>
            <li><strong>学生</strong>(<u>学号</u>, 姓名, 年级, 专业)</li>
            <li><strong>社团</strong>(<u>社团ID</u>, 社团名称, 成立年份)</li>
            <li><strong>活动</strong>(<u>活动ID</u>, 活动名称, 举办日期, 地点, 预算, 实际支出)</li>
            <li><strong>指导老师</strong>(<u>教工号</u>, 姓名, 职称, 所属院系)</li>
        </ul>
        
        <p><span class="entity">联系：</span></p>
        <ul>
            <li><strong>加入</strong>：学生与社团之间的M:N联系，具有属性"加入时间"</li>
            <li><strong>指导</strong>：指导老师与社团之间的1:N联系</li>
            <li><strong>举办</strong>：社团与活动之间的1:N联系</li>
        </ul>
    </div>

    <h2>(2) 关系模型转换</h2>
    <div class="answer">
        <p>根据E-R图转换成关系模型如下：</p>
        
        <div class="code">
            1. 学生(<u>学号</u>, 姓名, 年级, 专业)<br>
            2. 指导老师(<u>教工号</u>, 姓名, 职称, 所属院系)<br>
            3. 社团(<u>社团ID</u>, 社团名称, 成立年份, 教工号)<br>
               外码：教工号 → 指导老师(教工号)<br>
            4. 活动(<u>活动ID</u>, 活动名称, 举办日期, 地点, 预算, 实际支出, 社团ID)<br>
               外码：社团ID → 社团(社团ID)<br>
            5. 加入(<u>学号, 社团ID</u>, 加入时间)<br>
               外码：学号 → 学生(学号)<br>
               外码：社团ID → 社团(社团ID)
        </div>
        
        <p class="note">说明：</p>
        <ul>
            <li>1:N的"指导"联系已合并到社团关系中，教工号作为社团的外码</li>
            <li>1:N的"举办"联系已合并到活动关系中，社团ID作为活动的外码</li>
            <li>M:N的"加入"联系转换为单独的关系表，其主码为学号和社团ID的组合</li>
        </ul>
    </div>
</body>
</html>