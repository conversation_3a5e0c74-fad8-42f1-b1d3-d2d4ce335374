body {
    margin: 0;
    font-family: '<PERSON>', '<PERSON>o', 'Source <PERSON> CN', sans-serif; /* Updated to use part1_styles font stack */
    background-color: #FAF7F5; /* Updated to match part1_styles --color-bg-primary */
    color: #333;
    min-height: 200vh; /* For scrolling to see fixed header */
    padding-top: 100px; /* Space for the dynamic island */
    transition: background-color 0.3s ease;
}

#dynamic-island-container {
    position: fixed;
    top: 15px; /* Slight offset from top */
    left: 50%;
    transform: translateX(-50%); 
    display: flex;
    align-items: center;
    justify-content: center; /* Center islands initially, JS might reposition */
    padding: 5px;
    background-color: rgba(30, 30, 30, 0.75); /* 更深色的背景，提高对比度 */
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-radius: 30px; /* Pill shape for the container itself */
    z-index: 1000;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.25); /* 更柔和的阴影 */
    border: 1px solid rgba(255, 255, 255, 0.1); /* 更细微的边框 */
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1); /* 更平滑的过渡效果 */
    gap: 10px; /* 增加间距，提高清晰度 */
    will-change: transform; /* 性能优化：提示浏览器这个元素会有变化 */
}

.island {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    min-width: 42px; /* 稍微增加触摸区域 */
    height: 42px;
    background-color: rgba(0, 0, 0, 0.2); /* Darker islands */
    border-radius: 21px; /* 保持圆形 */
    cursor: pointer;
    transition: all 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* 更自然的缓动 */
    color: white;
    overflow: hidden; /* Important for expanding search etc. */
}

.island:hover {
    background-color: rgba(0, 0, 0, 0.3);
    transform: scale(1.05) translateY(-2px); /* 添加轻微上浮效果 */
    box-shadow: 0 0 15px 2px rgba(0, 122, 255, 0.5); /* Blue glow */
}

.island .icon {
    width: 22px;
    height: 22px;
    fill: currentColor;
    transition: transform 0.3s ease, opacity 0.2s ease; /* 添加透明度过渡 */
}

.island:hover .icon {
    opacity: 0.9; /* 悬停时轻微降低图标不透明度 */
}

.island:active .icon {
    transform: scale(0.85); /* 更明显的点击反馈 */
}

/* Logo specific */
.logo-island {
    background-color: rgba(255, 136, 0, 0.7); /* 更明亮的橙色 */
}
.logo-island:hover {
    box-shadow: 0 0 18px 3px rgba(255, 165, 0, 0.7); /* 更明显的光晕 */
}
.logo-svg {
    animation: breath 4s infinite ease-in-out; /* 更慢的动画周期 */
}
@keyframes breath {
    0%, 100% { opacity: 0.8; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.08); } /* 更明显的缩放 */
}

/* Search Island */
.search-island {
    padding: 10px;
    transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

#search-input {
    background: transparent;
    border: none;
    outline: none;
    color: white;
    font-size: 14px;
    width: 0;
    padding: 0;
    margin-left: 0;
    opacity: 0;
    transition: width 0.4s cubic-bezier(0.16, 1, 0.3, 1), 
                opacity 0.3s 0.1s ease, 
                padding 0.4s cubic-bezier(0.16, 1, 0.3, 1), 
                margin-left 0.4s cubic-bezier(0.16, 1, 0.3, 1);
}

.search-trigger-icon {
    transition: opacity 0.3s ease, transform 0.3s ease; /* 添加变换过渡 */
}

.search-island.active {
    /* width: auto; /* Allow it to expand based on input */
    min-width: 240px; /* 增加展开宽度 */
    background-color: rgba(0, 0, 0, 0.35); /* 稍微加深背景 */
    justify-content: flex-start;
}

.search-island.active #search-input {
    width: 180px; /* 增加宽度 */
    opacity: 1;
    padding: 0 5px;
    margin-left: 8px;
}

.search-island.active .search-trigger-icon {
    opacity: 0.7;
    transform: scale(0.9); /* 添加轻微缩放效果 */
}


/* Notification Island */
.notification-dot {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background-color: #FF5722; /* 更鲜明的橙色 */
    border-radius: 50%;
    border: 1px solid rgba(0,0,0,0.5);
    display: none; /* Hidden by default */
    animation: pulse 1.5s infinite ease-in-out;
    box-shadow: 0 0 5px rgba(255, 87, 34, 0.5); /* 添加光晕 */
}

.notification-dot.visible {
    display: block;
}

@keyframes pulse {
    0% { transform: scale(0.8); opacity: 0.7; }
    50% { transform: scale(1.2); opacity: 1; box-shadow: 0 0 8px rgba(255, 87, 34, 0.8); } /* 更明显的脉动 */
    100% { transform: scale(0.8); opacity: 0.7; }
}

/* User Island & Menu */
.user-island {
    padding: 5px; /* Smaller padding to fit avatar nicely */
    overflow: visible; /* 确保菜单可见 */
}

.avatar-img {
    width: 32px; /* 稍微增大头像 */
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    display: block;
    border: 2px solid rgba(255, 255, 255, 0.2); /* 添加边框 */
    transition: border-color 0.3s ease; /* 边框过渡效果 */
}

.user-island:hover .avatar-img {
    border-color: rgba(255, 255, 255, 0.5); /* 悬停时边框更明显 */
}

.user-menu {
    position: absolute;
    top: calc(100% + 10px); /* Below the island */
    right: 0;
    background-color: rgba(35, 35, 35, 0.85); /* 更深色 */
    backdrop-filter: blur(15px) saturate(150%);
    -webkit-backdrop-filter: blur(15px) saturate(150%);
    border-radius: 12px; /* 增大圆角 */
    padding: 10px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3); /* 更明显的阴影 */
    width: 200px; /* 增加宽度 */
    z-index: 1001;
    display: flex;
    flex-direction: column;
    gap: 5px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px) scale(0.95);
    transform-origin: top right;
    transition: opacity 0.3s ease, 
                visibility 0.3s ease, 
                transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1); /* 更有弹性的效果 */
}

.user-island.menu-active .user-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.user-menu a {
    text-decoration: none;
    color: white;
    padding: 10px 12px; /* 增加垂直padding */
    border-radius: 8px; /* 增大圆角 */
    font-size: 14px;
    transition: background-color 0.2s ease, transform 0.2s ease; /* 添加变换过渡 */
    display: flex;
    align-items: center;
}

.user-menu a:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateX(3px); /* 添加轻微位移效果 */
}

.user-menu svg {
    margin-right: 8px; /* 增加图标与文字间距 */
}

.user-menu hr {
    border: none;
    height: 1px;
    background-color: rgba(255, 255, 255, 0.1);
    margin: 5px 0;
}

/* Tooltips for nav items */
[data-tooltip]::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: calc(100% + 10px);
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(30, 30, 30, 0.9);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    pointer-events: none;
    z-index: 1002;
    transform: translateX(-50%) translateY(5px);
}

[data-tooltip]:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
}

/* Content area for demo */
.content {
    padding: 20px;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    opacity: 0;
    transform: translateY(10px);
    animation: fade-in 0.5s forwards ease-out;
}

@keyframes fade-in {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.content h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.content p, .content li {
    line-height: 1.6;
    margin-bottom: 1rem;
    font-size: 1rem;
    color: #4A4A4A;
}

.content ul {
    padding-left: 2rem;
    margin-bottom: 1.5rem;
}

.content button {
    background-color: #4A90E2;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.content button:hover {
    background-color: #3A7BD5;
}

.content button:active {
    transform: scale(0.98);
}


/* Contextual changes */
body.context-interview #dynamic-island-container {
    background-color: rgba(25, 25, 25, 0.85);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.3);
}

/* Reset styles for all interactive islands before applying context-specific ones */
#dynamic-island-container .island:not(#logo-island):not(#user-island):not(#notification-island):not(#search-island) {
    opacity: 1;
    visibility: visible;
    width: auto;
    margin: 0 2px;
    transform: translateY(0);
}

/* General dimmed state for islands in specific contexts */
body.context-interview .island:not(#logo-island):not(#user-island):not(#notification-island):not(#nav-interview):not(#search-island),
body.context-coach .island:not(#logo-island):not(#user-island):not(#notification-island):not(#nav-coach):not(#search-island)
{
    opacity: 0.5;
    transform: translateY(0);
}

/* Specific highlighted islands */
body.context-interview #nav-interview,
body.context-coach #nav-coach,
body.context-career #nav-career,
body.context-community #nav-community {
    background-color: rgba(255, 255, 255, 0.15);
    transform: scale(1.1);
    box-shadow: 0 0 15px 2px rgba(255, 255, 255, 0.3);
}

body.context-dashboard #dynamic-island-container {
    /* Optional: different style for dashboard's island container */
}

body.context-dashboard #nav-coach {
    opacity: 1;
    transform: scale(1.1);
    box-shadow: 0 0 20px 5px rgba(80, 200, 120, 0.7); /* Greenish glow for AI Coach */
}

/* Context-specific styles for dynamic island */
/* Example: Highlight active nav island based on body class */

/* General style for non-active islands in task modes */
body.context-interview #dynamic-island-container,
body.context-settings #dynamic-island-container,
body.context-ai-coach #dynamic-island-container,
body.context-skill-practice #dynamic-island-container {
    /* background-color: rgba(230, 230, 230, 0.6); Greyish tone when in a specific context */
}

#dynamic-island-container .island:not(#logo-island):not(#user-island):not(#notification-island):not(#search-island) {
    /* Default appearance for context-switchable islands */
    /* opacity: 0.7; */
    /* filter: grayscale(50%); */
}

/* Dim non-active islands in specific contexts */
body.context-interview .island:not(#logo-island):not(#user-island):not(#notification-island):not(#nav-interview):not(#search-island),
body.context-settings .island:not(#logo-island):not(#user-island):not(#notification-island):not(#nav-settings):not(#search-island),
body.context-ai-coach .island:not(#logo-island):not(#user-island):not(#notification-island):not(#nav-coach):not(#search-island),
body.context-skill-practice .island:not(#logo-island):not(#user-island):not(#notification-island):not(#nav-skills):not(#search-island)
{
    opacity: 0.5;
    transform: scale(0.9);
}

/* Highlight active island based on context */
body.context-interview #nav-interview,
body.context-settings #nav-settings, /* Assuming #nav-settings exists or will be created */
body.context-ai-coach #nav-coach,
body.context-skill-practice #nav-skills,
body.context-dashboard #nav-dashboard {
    opacity: 1;
    transform: scale(1.1);
    box-shadow: 0 0 15px 3px var(--color-accent-blue, rgba(0, 122, 255, 0.7)); /* Use CSS var with fallback */
    background-color: rgba(0, 100, 200, 0.4);
}

/* Styles from original inline style of dynamic_island_ui.html */
.content {
    padding-top: 80px; /* Adjust based on island height */
    padding-left: 20px;
    padding-right: 20px;
    text-align: center;
    /* Note: max-width and margin: auto from previous .content rule might be overridden or conflict.
       This will be resolved if layouts look incorrect. */
    contain: content; /* 内容包含，优化渲染性能 */
}

.context-display {
    display: none;
}

/* 响应式优化 */
@media (max-width: 768px) {
    #dynamic-island-container {
        width: 95%;
        justify-content: space-between;
        padding: 5px 10px;
    }
    
    .island {
        min-width: 36px;
        height: 36px;
        padding: 8px;
    }
    
    .island .icon {
        width: 20px;
        height: 20px;
    }
    
    .search-island.active {
        min-width: 160px;
    }
    
    .search-island.active #search-input {
        width: 100px;
    }
    
    .user-menu {
        width: 180px;
        right: -10px; /* 调整位置 */
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    body:not(.light-mode) {
        background-color: #121212;
        color: #e0e0e0;
    }
    
    body:not(.light-mode) #dynamic-island-container {
        background-color: rgba(50, 50, 50, 0.85);
        border-color: rgba(255, 255, 255, 0.05);
    }
}

/* 添加自定义滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
} 