<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级UI功能展示 - Dynamic Island</title>
    
    <!-- 样式表 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/enhanced-styles.css">
    <link rel="stylesheet" href="css/artboard-styles.css">
    <link rel="stylesheet" href="css/3d-effects.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 20px;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 50px;
            color: white;
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 30px;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 30px;
            color: white;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .demo-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }
        
        .demo-card:hover::before {
            opacity: 1;
        }
        
        .demo-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .demo-card h3 {
            font-size: 1.5rem;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .demo-card p {
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            border: none;
            border-radius: 12px;
            color: white;
            cursor: pointer;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }
        
        .particle-text-demo {
            text-align: center;
            margin: 50px 0;
            position: relative;
            height: 200px;
        }
        
        .flip-demo {
            margin: 20px 0;
        }
        
        .stagger-demo {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
        }
        
        .stagger-item {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .gesture-demo {
            min-height: 200px;
            background: rgba(255, 255, 255, 0.05);
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            position: relative;
        }
        
        .gesture-feedback {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .control-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            color: white;
        }
        
        .control-group {
            margin: 20px 0;
        }
        
        .control-group label {
            display: block;
            margin-bottom: 10px;
            font-weight: 500;
        }
        
        .control-group input[type="range"] {
            width: 100%;
            margin: 10px 0;
        }
        
        .stats-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            color: white;
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: scale(1.05);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stat-label {
            opacity: 0.8;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header" data-scroll-animation="fadeInUp">
            <h1 class="text-3d">🚀 高级UI功能展示</h1>
            <p>体验下一代用户界面交互和动画效果</p>
            <div class="particle-text-demo" id="particle-text-container"></div>
        </header>
        
        <div class="demo-grid">
            <!-- 3D效果演示 -->
            <div class="demo-card card-3d transform-3d" data-scroll-animation="fadeInLeft">
                <h3>🎯 3D转换效果</h3>
                <p>悬停查看3D卡片效果，支持深度感知和立体视觉。</p>
                
                <div class="flip-demo">
                    <div class="flip-card" style="width: 200px; height: 120px; margin: 0 auto;">
                        <div class="flip-card-inner">
                            <div class="flip-card-front">
                                <p>悬停翻转</p>
                            </div>
                            <div class="flip-card-back">
                                <p>背面内容</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <button class="demo-button btn-3d" onclick="demo3DEffects()">测试3D按钮</button>
                <button class="demo-button glassmorphism-btn" onclick="demoGlassmorphism()">玻璃形态</button>
            </div>
            
            <!-- 粒子系统演示 -->
            <div class="demo-card" data-scroll-animation="fadeInUp">
                <h3>✨ 粒子系统</h3>
                <p>交互式粒子背景，支持鼠标交互和动态连接。</p>
                
                <div id="particle-demo" style="height: 150px; position: relative; border: 1px solid rgba(255,255,255,0.3); border-radius: 8px; margin: 15px 0;">
                </div>
                
                <button class="demo-button" onclick="resetParticles()">重置粒子</button>
                <button class="demo-button" onclick="toggleParticleInteraction()">切换交互</button>
            </div>
            
            <!-- 手势识别演示 -->
            <div class="demo-card" data-scroll-animation="fadeInRight">
                <h3>👆 手势识别</h3>
                <p>支持滑动、长按、缩放和旋转手势识别。</p>
                
                <div class="gesture-demo" id="gesture-demo-area">
                    <div class="gesture-feedback" id="gesture-feedback"></div>
                    <p>在此区域尝试各种手势操作</p>
                </div>
                
                <div class="stagger-demo">
                    <div class="stagger-item"></div>
                    <div class="stagger-item"></div>
                    <div class="stagger-item"></div>
                    <div class="stagger-item"></div>
                </div>
                
                <button class="demo-button" onclick="demoStaggerAnimation()">交错动画</button>
            </div>
            
            <!-- 高级动画演示 -->
            <div class="demo-card floating" data-scroll-animation="scaleIn">
                <h3>🎬 高级动画</h3>
                <p>弹簧物理、缓动函数和关键帧动画系统。</p>
                
                <div style="display: flex; gap: 10px; margin: 15px 0; justify-content: center;">
                    <div class="demo-element" style="width: 50px; height: 50px; background: linear-gradient(45deg, #ff6b6b, #ee5a52); border-radius: 50%; transition: all 0.3s ease;"></div>
                    <div class="demo-element" style="width: 50px; height: 50px; background: linear-gradient(45deg, #4ecdc4, #44a08d); border-radius: 8px; transition: all 0.3s ease;"></div>
                    <div class="demo-element" style="width: 50px; height: 50px; background: linear-gradient(45deg, #45b7d1, #96c93d); border-radius: 25px; transition: all 0.3s ease;"></div>
                </div>
                
                <button class="demo-button" onclick="demoSpringAnimation()">弹簧动画</button>
                <button class="demo-button" onclick="demoKeyframeAnimation()">关键帧</button>
            </div>
            
            <!-- 视觉反馈演示 -->
            <div class="demo-card breathing" data-scroll-animation="rotateIn">
                <h3>💫 视觉反馈</h3>
                <p>涟漪效果、脉冲动画和智能触摸反馈。</p>
                
                <div style="display: flex; gap: 15px; margin: 20px 0; justify-content: center; flex-wrap: wrap;">
                    <button class="demo-button" onclick="createRipple(event)">涟漪效果</button>
                    <button class="demo-button" onclick="createGlow(event)">发光效果</button>
                    <button class="demo-button" onclick="createShake(this)">震动效果</button>
                    <button class="demo-button" onclick="createBounce(this)">弹跳效果</button>
                </div>
            </div>
            
            <!-- 性能监控演示 -->
            <div class="demo-card magnetic-hover" data-scroll-animation="fadeInLeft">
                <h3>📊 性能监控</h3>
                <p>实时监控动画性能和用户交互数据。</p>
                
                <div class="stats-display">
                    <div class="stat-card">
                        <div class="stat-number" id="fps-counter">60</div>
                        <div class="stat-label">FPS</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="interaction-count">0</div>
                        <div class="stat-label">交互次数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="particle-count">50</div>
                        <div class="stat-label">粒子数量</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 控制面板 -->
        <div class="control-panel">
            <h3>🎛️ 实时控制面板</h3>
            
            <div class="control-group">
                <label for="animation-speed">动画速度</label>
                <input type="range" id="animation-speed" min="0.1" max="2" step="0.1" value="1" onchange="updateAnimationSpeed(this.value)">
            </div>
            
            <div class="control-group">
                <label for="particle-count-slider">粒子数量</label>
                <input type="range" id="particle-count-slider" min="10" max="100" step="5" value="50" onchange="updateParticleCount(this.value)">
            </div>
            
            <div class="control-group">
                <label>
                    <input type="checkbox" id="enable-3d" checked onchange="toggle3DEffects(this.checked)"> 启用3D效果
                </label>
            </div>
            
            <div class="control-group">
                <label>
                    <input type="checkbox" id="enable-particles" checked onchange="toggleParticles(this.checked)"> 启用粒子效果
                </label>
            </div>
            
            <div class="control-group">
                <label>
                    <input type="checkbox" id="performance-mode" onchange="togglePerformanceMode(this.checked)"> 性能模式
                </label>
            </div>
        </div>
    </div>
    
    <!-- 脚本文件 -->
    <script src="js/particle-system.js"></script>
    <script src="js/advanced-gestures.js"></script>
    <script src="js/animation-controller.js"></script>
    
    <script>
        // 全局变量
        let particleSystem = null;
        let gestureHandler = null;
        let animationController = null;
        let feedbackSystem = null;
        let interactionCount = 0;
        let currentParticleCount = 50;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSystems();
            startPerformanceMonitoring();
            setupEventListeners();
        });
        
        function initializeSystems() {
            // 初始化粒子系统
            if (window.ParticleSystem) {
                particleSystem = new ParticleSystem('particle-demo', {
                    particleCount: currentParticleCount,
                    connectionDistance: 100,
                    particleSpeed: 0.5,
                    interactive: true,
                    color: '#ffffff'
                });
            }
            
            // 初始化手势系统
            if (window.AdvancedGestureHandler) {
                gestureHandler = new AdvancedGestureHandler();
                setupGestureEvents();
            }
            
            // 初始化动画控制器
            if (window.AdvancedAnimationController) {
                animationController = new AdvancedAnimationController();
            }
            
            // 初始化视觉反馈系统
            if (window.VisualFeedbackSystem) {
                feedbackSystem = new VisualFeedbackSystem();
                
                // 为所有按钮添加反馈
                document.querySelectorAll('.demo-button').forEach(btn => {
                    feedbackSystem.addTouchFeedback(btn, 'ripple');
                });
            }
            
            // 初始化粒子文字
            if (window.ParticleText) {
                new ParticleText('DYNAMIC ISLAND', 
                    document.getElementById('particle-text-container'), {
                    fontSize: 40,
                    particleCount: 80,
                    color: '#ffffff',
                    interactive: true
                });
            }
        }
        
        function setupGestureEvents() {
            const gestureArea = document.getElementById('gesture-demo-area');
            const feedback = document.getElementById('gesture-feedback');
            
            gestureHandler.on('swipe', (data) => {
                showGestureFeedback(`滑动: ${data.direction}`);
                interactionCount++;
                updateStats();
            });
            
            gestureHandler.on('longPress', (data) => {
                showGestureFeedback('长按检测');
                interactionCount++;
                updateStats();
            });
            
            gestureHandler.on('pinch', (data) => {
                showGestureFeedback(`缩放: ${data.scale.toFixed(2)}`);
                interactionCount++;
                updateStats();
            });
            
            gestureHandler.on('rotate', (data) => {
                showGestureFeedback(`旋转: ${data.rotation.toFixed(1)}°`);
                interactionCount++;
                updateStats();
            });
        }
        
        function showGestureFeedback(message) {
            const feedback = document.getElementById('gesture-feedback');
            feedback.textContent = message;
            feedback.style.opacity = '1';
            
            setTimeout(() => {
                feedback.style.opacity = '0';
            }, 2000);
        }
        
        // 演示函数
        function demo3DEffects() {
            const elements = document.querySelectorAll('.demo-element');
            elements.forEach((el, index) => {
                setTimeout(() => {
                    el.style.transform = 'translateZ(20px) rotateY(180deg) scale(1.2)';
                    setTimeout(() => {
                        el.style.transform = 'translateZ(0) rotateY(0deg) scale(1)';
                    }, 1000);
                }, index * 200);
            });
            interactionCount++;
            updateStats();
        }
        
        function demoGlassmorphism() {
            const card = event.target.closest('.demo-card');
            card.style.background = 'rgba(255, 255, 255, 0.2)';
            card.style.backdropFilter = 'blur(20px)';
            
            setTimeout(() => {
                card.style.background = 'rgba(255, 255, 255, 0.1)';
                card.style.backdropFilter = 'blur(10px)';
            }, 2000);
            
            interactionCount++;
            updateStats();
        }
        
        function demoSpringAnimation() {
            const elements = document.querySelectorAll('.demo-element');
            
            if (animationController) {
                elements.forEach((element, index) => {
                    animationController.spring(element, {
                        from: { transform: 'scale(1) rotate(0deg)' },
                        to: { transform: 'scale(1.5) rotate(360deg)' },
                        stiffness: 150,
                        damping: 8,
                        onComplete: () => {
                            animationController.spring(element, {
                                from: { transform: 'scale(1.5) rotate(360deg)' },
                                to: { transform: 'scale(1) rotate(0deg)' },
                                stiffness: 200,
                                damping: 10
                            });
                        }
                    });
                });
            }
            
            interactionCount++;
            updateStats();
        }
        
        function demoKeyframeAnimation() {
            const elements = document.querySelectorAll('.demo-element');
            
            if (animationController) {
                elements.forEach((element, index) => {
                    animationController.keyframes(element, {
                        '0%': { transform: 'translateY(0) scale(1)', opacity: '1' },
                        '25%': { transform: 'translateY(-20px) scale(1.1)', opacity: '0.8' },
                        '50%': { transform: 'translateY(-30px) scale(1.2)', opacity: '0.6' },
                        '75%': { transform: 'translateY(-20px) scale(1.1)', opacity: '0.8' },
                        '100%': { transform: 'translateY(0) scale(1)', opacity: '1' }
                    }, {
                        duration: 1000,
                        delay: index * 200
                    });
                });
            }
            
            interactionCount++;
            updateStats();
        }
        
        function demoStaggerAnimation() {
            const items = document.querySelectorAll('.stagger-item');
            
            if (animationController) {
                animationController.stagger(items, {
                    from: { transform: 'scale(1) rotate(0deg)', background: 'linear-gradient(135deg, #667eea, #764ba2)' },
                    to: { transform: 'scale(1.2) rotate(180deg)', background: 'linear-gradient(135deg, #ff6b6b, #ee5a52)' },
                    duration: 500
                }, 100).then(() => {
                    // 返回原状态
                    setTimeout(() => {
                        animationController.stagger(items, {
                            from: { transform: 'scale(1.2) rotate(180deg)', background: 'linear-gradient(135deg, #ff6b6b, #ee5a52)' },
                            to: { transform: 'scale(1) rotate(0deg)', background: 'linear-gradient(135deg, #667eea, #764ba2)' },
                            duration: 500
                        }, 100);
                    }, 500);
                });
            }
            
            interactionCount++;
            updateStats();
        }
        
        // 视觉反馈演示
        function createRipple(event) {
            if (feedbackSystem) {
                feedbackSystem.createRipple(event.clientX, event.clientY, 150, 'rgba(255, 255, 255, 0.5)');
            }
            interactionCount++;
            updateStats();
        }
        
        function createGlow(event) {
            if (feedbackSystem) {
                feedbackSystem.createGlow(event.clientX, event.clientY, 100);
            }
            interactionCount++;
            updateStats();
        }
        
        function createShake(element) {
            if (feedbackSystem) {
                feedbackSystem.shake(element);
            }
            interactionCount++;
            updateStats();
        }
        
        function createBounce(element) {
            if (feedbackSystem) {
                feedbackSystem.bounce(element);
            }
            interactionCount++;
            updateStats();
        }
        
        // 控制面板函数
        function updateAnimationSpeed(speed) {
            document.documentElement.style.setProperty('--animation-speed', speed);
            document.querySelectorAll('*').forEach(el => {
                if (el.style.animationDuration) {
                    const duration = parseFloat(el.style.animationDuration);
                    el.style.animationDuration = (duration / speed) + 's';
                }
            });
        }
        
        function updateParticleCount(count) {
            currentParticleCount = parseInt(count);
            document.getElementById('particle-count').textContent = count;
            
            if (particleSystem) {
                particleSystem.updateOptions({ particleCount: currentParticleCount });
            }
        }
        
        function toggle3DEffects(enabled) {
            if (enabled) {
                document.body.classList.remove('disable-3d');
            } else {
                document.body.classList.add('disable-3d');
            }
        }
        
        function toggleParticles(enabled) {
            if (particleSystem) {
                if (enabled) {
                    particleSystem.options.particleCount = currentParticleCount;
                } else {
                    particleSystem.options.particleCount = 0;
                }
                particleSystem.updateOptions(particleSystem.options);
            }
        }
        
        function togglePerformanceMode(enabled) {
            if (enabled) {
                document.body.classList.add('performance-mode');
                updateParticleCount(20);
                document.getElementById('particle-count-slider').value = 20;
            } else {
                document.body.classList.remove('performance-mode');
                updateParticleCount(50);
                document.getElementById('particle-count-slider').value = 50;
            }
        }
        
        function resetParticles() {
            if (particleSystem) {
                particleSystem.destroy();
                particleSystem = new ParticleSystem('particle-demo', {
                    particleCount: currentParticleCount,
                    connectionDistance: 100,
                    particleSpeed: 0.5,
                    interactive: true,
                    color: '#ffffff'
                });
            }
            interactionCount++;
            updateStats();
        }
        
        function toggleParticleInteraction() {
            if (particleSystem) {
                particleSystem.options.interactive = !particleSystem.options.interactive;
                particleSystem.updateOptions(particleSystem.options);
            }
            interactionCount++;
            updateStats();
        }
        
        // 性能监控
        function startPerformanceMonitoring() {
            let lastTime = performance.now();
            let frameCount = 0;
            
            function updateFPS() {
                frameCount++;
                const currentTime = performance.now();
                const deltaTime = currentTime - lastTime;
                
                if (deltaTime >= 1000) {
                    const fps = Math.round((frameCount * 1000) / deltaTime);
                    document.getElementById('fps-counter').textContent = fps;
                    frameCount = 0;
                    lastTime = currentTime;
                }
                
                requestAnimationFrame(updateFPS);
            }
            
            updateFPS();
        }
        
        function updateStats() {
            document.getElementById('interaction-count').textContent = interactionCount;
        }
        
        function setupEventListeners() {
            // 监听所有交互事件
            document.addEventListener('click', () => {
                interactionCount++;
                updateStats();
            });
            
            // 键盘快捷键
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case '1':
                            e.preventDefault();
                            demo3DEffects();
                            break;
                        case '2':
                            e.preventDefault();
                            demoSpringAnimation();
                            break;
                        case '3':
                            e.preventDefault();
                            demoStaggerAnimation();
                            break;
                        case 'r':
                            e.preventDefault();
                            resetParticles();
                            break;
                    }
                }
            });
        }
        
        // 添加样式来禁用3D效果
        const style = document.createElement('style');
        style.textContent = `
            .disable-3d .card-3d,
            .disable-3d .transform-3d,
            .disable-3d .btn-3d {
                transform: none !important;
            }
            
            .performance-mode .floating,
            .performance-mode .breathing {
                animation: none !important;
            }
            
            .performance-mode .card-3d:hover {
                transform: translateY(-5px) !important;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
