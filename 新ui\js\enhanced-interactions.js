// Enhanced Dynamic Island Interactions with Performance Optimizations
class EnhancedDynamicIsland {
    // Singleton pattern
    static instance = null;
    
    constructor() {
        // Implement singleton pattern
        if (EnhancedDynamicIsland.instance) {
            return EnhancedDynamicIsland.instance;
        }
        
        // Initialize properties
        this.islands = [];
        this.searchExpanded = false;
        this.userMenuOpen = false;
        this.currentTheme = this.getPreferredTheme();
        this.animationFrameId = null;
        this.resizeTimeoutId = null;
        this.searchTimeoutId = null;
        this.performanceConfig = null;
        
        // Bind methods to preserve context
        this.handleResize = this.debounce(this.handleResize.bind(this), 250);
        this.handleScroll = this.throttle(this.handleScroll.bind(this), 16);
        this.handleKeyboardNavigation = this.handleKeyboardNavigation.bind(this);
        
        // Initialize
        this.init();
        
        // Set singleton instance
        EnhancedDynamicIsland.instance = this;
    }
    
    static getInstance() {
        if (!EnhancedDynamicIsland.instance) {
            new EnhancedDynamicIsland();
        }
        return EnhancedDynamicIsland.instance;
    }

    async init() {
        try {
            // Wait for performance config to load
            if (window.PerformanceConfig) {
                this.performanceConfig = window.PerformanceConfig.getInstance();
                await this.performanceConfig.initialize();
            }
            
            this.setupElements();
            this.bindEvents();
            this.initAnimations();
            this.initThemeSystem();
            this.initAccessibility();
            this.setupPerformanceOptimizations();
            
            console.log('EnhancedDynamicIsland initialized successfully');
        } catch (error) {
            console.error('Failed to initialize EnhancedDynamicIsland:', error);
        }
    }

    setupElements() {
        // Cache DOM elements with error handling
        try {
            this.container = document.getElementById('dynamic-island-container');
            this.searchIsland = document.getElementById('search-island');
            this.searchInput = document.getElementById('search-input');
            this.userIsland = document.getElementById('user-island');
            this.notificationIsland = document.getElementById('notification-island');
            
            // Get all navigation islands
            this.navIslands = document.querySelectorAll('.nav-island');
            
            // Validate critical elements
            if (!this.container) {
                throw new Error('Dynamic island container not found');
            }
        } catch (error) {
            console.error('Error setting up elements:', error);
        }
    }
    
    setupPerformanceOptimizations() {
        // Apply performance-based optimizations
        if (this.performanceConfig) {
            const settings = this.performanceConfig.getOptimizedSettings();
            
            // Disable complex animations on low-end devices
            if (settings.animations.complexity === 'minimal') {
                document.body.classList.add('minimal-animations');
            }
            
            // Reduce effects based on performance
            if (settings.effects.particles === false) {
                document.body.classList.add('no-particles');
            }
        }
    }
    
    getPreferredTheme() {
        // Get theme from localStorage or system preference
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            return savedTheme;
        }
        
        return window.matchMedia?.('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }    bindEvents() {
        // Search functionality with error handling
        this.searchIsland?.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleSearch();
        });

        // Search input events with debouncing
        this.searchInput?.addEventListener('input', this.debounce((e) => {
            this.handleSearch(e.target.value);
        }, 300));

        this.searchInput?.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.collapseSearch();
            }
        });

        // User menu with touch support
        this.userIsland?.addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleUserMenu();
        });

        // Navigation islands with enhanced interaction
        this.navIslands.forEach(island => {
            // Add click handler
            island.addEventListener('click', (e) => {
                this.handleNavigation(island.id);
                this.addRippleEffect(e, island);
            });
            
            // Add hover effects for desktop
            if (!this.isTouchDevice()) {
                this.addHoverEffects(island);
            }
        });

        // Notification island
        this.notificationIsland?.addEventListener('click', () => {
            this.showNotifications();
        });

        // Global event listeners with passive optimization
        document.addEventListener('click', this.handleGlobalClick.bind(this));
        document.addEventListener('keydown', this.handleKeyboardNavigation, { passive: false });
        
        // Optimized scroll and resize handlers
        window.addEventListener('scroll', this.handleScroll, { passive: true });
        window.addEventListener('resize', this.handleResize, { passive: true });
        
        // Visibility change optimization
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAnimations();
            } else {
                this.resumeAnimations();
            }
        });
    }
    
    handleGlobalClick() {
        this.collapseSearch();
        this.closeUserMenu();
    }
    
    addHoverEffects(island) {
        island.addEventListener('mouseenter', () => {
            if (!this.performanceConfig?.shouldReduceMotion()) {
                island.style.transform = 'scale(1.05)';
                island.style.transition = 'transform 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
            }
        });
        
        island.addEventListener('mouseleave', () => {
            island.style.transform = '';
        });
    }
    
    isTouchDevice() {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }
    
    pauseAnimations() {
        document.body.classList.add('animations-paused');
    }
    
    resumeAnimations() {
        document.body.classList.remove('animations-paused');
    }    // Enhanced search functionality
    toggleSearch() {
        if (this.searchExpanded) {
            this.collapseSearch();
        } else {
            this.expandSearch();
        }
    }

    expandSearch() {
        if (this.searchExpanded) return;
        
        this.searchExpanded = true;
        this.searchIsland?.classList.add('active');
        
        // Use requestAnimationFrame for smooth animations
        this.animationFrameId = requestAnimationFrame(() => {
            this.searchInput?.focus();
            this.animateSearchExpansion();
        });
    }

    collapseSearch() {
        if (!this.searchExpanded) return;
        
        this.searchExpanded = false;
        this.searchIsland?.classList.remove('active');
        
        if (this.searchInput) {
            this.searchInput.value = '';
            this.searchInput.blur();
        }
        
        this.clearSearchResults();
        
        // Cancel any pending animation
        if (this.animationFrameId) {
            cancelAnimationFrame(this.animationFrameId);
            this.animationFrameId = null;
        }
    }

    animateSearchExpansion() {
        if (!this.searchIsland || this.performanceConfig?.shouldReduceMotion()) {
            return;
        }
        
        this.searchIsland.style.animation = 'searchPulse 0.3s ease-out';
        setTimeout(() => {
            if (this.searchIsland) {
                this.searchIsland.style.animation = '';
            }
        }, 300);
    }

    handleSearch(query) {
        // Clear previous timeout
        if (this.searchTimeoutId) {
            clearTimeout(this.searchTimeoutId);
        }
        
        if (query.length < 2) {
            this.clearSearchResults();
            return;
        }

        // Debounced search execution
        this.searchTimeoutId = setTimeout(() => {
            this.performSearch(query);
        }, 300);
    }

    async performSearch(query) {
        try {
            console.log('Searching for:', query);
            
            // Add search feedback animation
            this.addSearchFeedback();
            
            // Here you would integrate with actual search API
            // const results = await this.searchAPI(query);
            
        } catch (error) {
            console.error('Search failed:', error);
            this.showSearchError();
        }
    }

    addSearchFeedback() {
        const searchIcon = this.searchIsland?.querySelector('.icon');
        if (searchIcon && !this.performanceConfig?.shouldReduceMotion()) {
            searchIcon.style.animation = 'spin 0.5s ease-in-out';
            setTimeout(() => {
                if (searchIcon) {
                    searchIcon.style.animation = '';
                }
            }, 500);
        }
    }
    
    showSearchError() {
        // Show user-friendly error message
        console.log('Search temporarily unavailable');
    }

    clearSearchResults() {
        // Clear search results display
        // Implementation depends on search results structure
    }    // Enhanced user menu functionality
    toggleUserMenu() {
        if (this.userMenuOpen) {
            this.closeUserMenu();
        } else {
            this.openUserMenu();
        }
    }

    openUserMenu() {
        if (this.userMenuOpen) return;
        
        this.userMenuOpen = true;
        const menu = this.userIsland?.querySelector('.user-menu');
        
        if (menu) {
            menu.style.display = 'block';
            // Use requestAnimationFrame for smoother animation
            requestAnimationFrame(() => {
                menu.classList.add('slide-up');
            });
        }
    }

    closeUserMenu() {
        if (!this.userMenuOpen) return;
        
        this.userMenuOpen = false;
        const menu = this.userIsland?.querySelector('.user-menu');
        
        if (menu) {
            menu.classList.remove('slide-up');
            setTimeout(() => {
                menu.style.display = 'none';
            }, 200);
        }
    }

    // Enhanced navigation handling
    handleNavigation(islandId) {
        try {
            // Remove all active states efficiently
            this.navIslands.forEach(island => {
                island.classList.remove('active');
            });

            // Add active state to clicked island
            const activeIsland = document.getElementById(islandId);
            if (activeIsland) {
                activeIsland.classList.add('active');
                this.animateNavigation(activeIsland);
            }

            // Trigger page switch
            this.switchPage(islandId);
        } catch (error) {
            console.error('Navigation failed:', error);
        }
    }

    animateNavigation(island) {
        if (!island || this.performanceConfig?.shouldReduceMotion()) {
            return;
        }
        
        island.style.animation = 'navPulse 0.4s ease-out';
        setTimeout(() => {
            if (island) {
                island.style.animation = '';
            }
        }, 400);
    }

    switchPage(islandId) {
        console.log('Switching to page:', islandId);
        
        // Page transition animation with performance consideration
        if (!this.performanceConfig?.shouldReduceMotion()) {
            this.animatePageTransition();
        }
        
        // Here you would implement actual page switching logic
        // this.pageManager?.switchTo(islandId);
    }

    animatePageTransition() {
        if (this.performanceConfig?.shouldReduceMotion()) {
            return;
        }
        
        document.body.style.animation = 'pageTransition 0.5s ease-out';
        setTimeout(() => {
            document.body.style.animation = '';
        }, 500);
    }    // Enhanced ripple effect with performance optimization
    addRippleEffect(event, element) {
        if (this.performanceConfig?.shouldReduceMotion()) {
            return;
        }
        
        try {
            const ripple = document.createElement('span');
            const rect = element.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = event.clientX - rect.left - size / 2;
            const y = event.clientY - rect.top - size / 2;

            ripple.className = 'ripple-effect';
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
                z-index: 1000;
            `;

            // Ensure element positioning
            const computedStyle = window.getComputedStyle(element);
            if (computedStyle.position === 'static') {
                element.style.position = 'relative';
            }
            element.style.overflow = 'hidden';
            
            element.appendChild(ripple);

            // Clean up ripple element
            setTimeout(() => {
                if (ripple.parentNode) {
                    ripple.parentNode.removeChild(ripple);
                }
            }, 600);
        } catch (error) {
            console.error('Ripple effect failed:', error);
        }
    }

    // Enhanced notification system
    showNotifications() {
        try {
            // Remove notification dot with animation
            const dot = this.notificationIsland?.querySelector('.notification-dot');
            if (dot) {
                dot.style.animation = 'fadeOut 0.3s ease-out forwards';
                setTimeout(() => {
                    if (dot.parentNode) {
                        dot.parentNode.removeChild(dot);
                    }
                }, 300);
            }

            // Show notification panel
            this.createNotificationPanel();
        } catch (error) {
            console.error('Failed to show notifications:', error);
        }
    }

    createNotificationPanel() {
        try {
            // Remove existing panel
            const existingPanel = document.querySelector('.notification-panel');
            if (existingPanel) {
                existingPanel.remove();
            }
            
            // Create notification panel
            const panel = document.createElement('div');
            panel.className = 'notification-panel slide-up';
            panel.innerHTML = this.getNotificationHTML();

            document.body.appendChild(panel);

            // Bind close event
            const closeBtn = panel.querySelector('.close-btn');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    this.closeNotificationPanel(panel);
                });
            }

            // Auto close after 5 seconds
            setTimeout(() => {
                if (panel.parentNode) {
                    this.closeNotificationPanel(panel);
                }
            }, 5000);
        } catch (error) {
            console.error('Failed to create notification panel:', error);
        }
    }
    
    getNotificationHTML() {
        return `
            <div class="notification-header">
                <h3>通知</h3>
                <button class="close-btn" aria-label="关闭通知">&times;</button>
            </div>
            <div class="notification-list">
                <div class="notification-item">
                    <div class="notification-content">
                        <h4>新面试邀请</h4>
                        <p>您有一个新的模拟面试邀请</p>
                        <span class="notification-time">2分钟前</span>
                    </div>
                </div>
                <div class="notification-item">
                    <div class="notification-content">
                        <h4>技能评估完成</h4>
                        <p>您的JavaScript技能评估已完成</p>
                        <span class="notification-time">1小时前</span>
                    </div>
                </div>
            </div>
        `;
    }

    closeNotificationPanel(panel) {
        if (!panel) return;
        
        panel.classList.add('fade-out');
        setTimeout(() => {
            if (panel.parentNode) {
                panel.parentNode.removeChild(panel);
            }
        }, 300);
    }    // Enhanced theme system
    initThemeSystem() {
        try {
            this.applyTheme(this.currentTheme);

            // Listen for system theme changes
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            const handleSystemThemeChange = (e) => {
                if (!localStorage.getItem('theme')) {
                    this.currentTheme = e.matches ? 'dark' : 'light';
                    this.applyTheme(this.currentTheme);
                }
            };
            
            // Use modern addEventListener if available
            if (mediaQuery.addEventListener) {
                mediaQuery.addEventListener('change', handleSystemThemeChange);
            } else {
                // Fallback for older browsers
                mediaQuery.addListener(handleSystemThemeChange);
            }
        } catch (error) {
            console.error('Theme system initialization failed:', error);
        }
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(this.currentTheme);
        localStorage.setItem('theme', this.currentTheme);
    }

    applyTheme(theme) {
        try {
            document.documentElement.setAttribute('data-theme', theme);
            
            // Add theme transition animation if not reduced motion
            if (!this.performanceConfig?.shouldReduceMotion()) {
                document.body.classList.add('theme-transition');
                setTimeout(() => {
                    document.body.classList.remove('theme-transition');
                }, 300);
            }
        } catch (error) {
            console.error('Failed to apply theme:', error);
        }
    }

    // Enhanced keyboard navigation
    handleKeyboardNavigation(event) {
        const { key, altKey, ctrlKey, metaKey } = event;
        
        try {
            // Alt + number keys for quick navigation
            if (altKey && !isNaN(key) && key >= 1 && key <= 9) {
                event.preventDefault();
                const index = parseInt(key) - 1;
                const islands = Array.from(this.navIslands);
                if (islands[index]) {
                    this.handleNavigation(islands[index].id);
                }
                return;
            }

            // Ctrl/Cmd + K to open search
            if ((ctrlKey || metaKey) && key === 'k') {
                event.preventDefault();
                this.expandSearch();
                return;
            }

            // ESC to close panels
            if (key === 'Escape') {
                this.collapseSearch();
                this.closeUserMenu();
                return;
            }
            
            // Arrow key navigation
            if (['ArrowLeft', 'ArrowRight'].includes(key)) {
                this.handleArrowNavigation(key);
            }
        } catch (error) {
            console.error('Keyboard navigation failed:', error);
        }
    }
    
    handleArrowNavigation(key) {
        const activeIsland = document.querySelector('.nav-island.active');
        if (!activeIsland) return;
        
        const islands = Array.from(this.navIslands);
        const currentIndex = islands.indexOf(activeIsland);
        
        if (currentIndex === -1) return;
        
        let nextIndex;
        if (key === 'ArrowLeft') {
            nextIndex = currentIndex > 0 ? currentIndex - 1 : islands.length - 1;
        } else {
            nextIndex = currentIndex < islands.length - 1 ? currentIndex + 1 : 0;
        }
        
        const nextIsland = islands[nextIndex];
        if (nextIsland) {
            this.handleNavigation(nextIsland.id);
        }
    }    // Optimized scroll handling
    handleScroll() {
        const scrollY = window.scrollY;
        
        // Dynamic island scroll effect
        if (this.container) {
            if (scrollY > 50) {
                this.container.classList.add('scrolled');
            } else {
                this.container.classList.remove('scrolled');
            }

            // Parallax effect with performance consideration
            if (!this.performanceConfig?.shouldReduceMotion()) {
                const translateY = Math.min(scrollY * 0.1, 10);
                this.container.style.transform = `translateX(-50%) translateY(${translateY}px)`;
            }
        }
    }

    // Optimized resize handling
    handleResize() {
        const width = window.innerWidth;
        
        // Auto-collapse search on mobile
        if (width < 768 && this.searchExpanded) {
            this.collapseSearch();
        }
        
        // Update performance settings based on viewport
        if (this.performanceConfig) {
            this.performanceConfig.updateViewportSettings(width, window.innerHeight);
        }
    }

    // Enhanced animation initialization
    initAnimations() {
        // Add CSS animations only if not using reduced motion
        if (this.performanceConfig?.shouldReduceMotion()) {
            document.body.classList.add('reduced-motion');
            return;
        }
        
        const style = document.createElement('style');
        style.id = 'enhanced-dynamic-island-animations';
        style.textContent = this.getAnimationCSS();
        
        // Remove existing styles to prevent duplicates
        const existingStyle = document.getElementById('enhanced-dynamic-island-animations');
        if (existingStyle) {
            existingStyle.remove();
        }
        
        document.head.appendChild(style);
    }
    
    getAnimationCSS() {
        return `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
            
            @keyframes searchPulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
            
            @keyframes navPulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.1); }
                100% { transform: scale(1); }
            }
            
            @keyframes pageTransition {
                0% { opacity: 1; }
                50% { opacity: 0.9; }
                100% { opacity: 1; }
            }
            
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            
            @keyframes fadeOut {
                to {
                    opacity: 0;
                    transform: scale(0);
                }
            }
            
            @keyframes slide-up {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .notification-panel {
                position: fixed;
                top: 80px;
                right: 20px;
                width: min(320px, calc(100vw - 40px));
                background: var(--bg-glass, rgba(255, 255, 255, 0.95));
                backdrop-filter: blur(20px);
                border-radius: var(--radius-xl, 16px);
                box-shadow: var(--shadow-lg, 0 25px 50px -12px rgba(0, 0, 0, 0.25));
                border: 1px solid rgba(255, 255, 255, 0.2);
                z-index: var(--z-modal, 1000);
                overflow: hidden;
                animation: slide-up 0.3s ease-out;
            }
            
            .notification-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: var(--space-4, 16px);
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }
            
            .notification-header h3 {
                margin: 0;
                color: var(--text-primary, #1f2937);
                font-size: 16px;
                font-weight: 600;
            }
            
            .close-btn {
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
                color: var(--text-secondary, #6b7280);
                padding: 0;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                transition: var(--transition-fast, 0.15s ease);
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .close-btn:hover {
                background: rgba(255, 255, 255, 0.1);
                color: var(--text-primary, #1f2937);
            }
            
            .notification-list {
                max-height: 300px;
                overflow-y: auto;
            }
            
            .notification-item {
                padding: var(--space-4, 16px);
                border-bottom: 1px solid rgba(255, 255, 255, 0.05);
                transition: var(--transition-fast, 0.15s ease);
            }
            
            .notification-item:hover {
                background: rgba(255, 255, 255, 0.05);
            }
            
            .notification-item:last-child {
                border-bottom: none;
            }
            
            .notification-content h4 {
                margin: 0 0 var(--space-1, 4px) 0;
                color: var(--text-primary, #1f2937);
                font-size: 14px;
                font-weight: 500;
            }
            
            .notification-content p {
                margin: 0 0 var(--space-2, 8px) 0;
                color: var(--text-secondary, #6b7280);
                font-size: 13px;
                line-height: 1.4;
            }
            
            .notification-time {
                font-size: 12px;
                color: var(--text-tertiary, #9ca3af);
            }
            
            .fade-out {
                animation: fadeOut 0.3s ease-out forwards;
            }
            
            .scrolled {
                background: rgba(255, 255, 255, 0.95) !important;
                backdrop-filter: blur(30px) !important;
                box-shadow: var(--shadow-xl, 0 25px 50px -12px rgba(0, 0, 0, 0.25)) !important;
            }
            
            .reduced-motion *,
            .reduced-motion *::before,
            .reduced-motion *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
            
            .animations-paused * {
                animation-play-state: paused !important;
            }
            
            .theme-transition {
                transition: background-color 0.3s ease, color 0.3s ease;
            }
            
            .minimal-animations .notification-panel {
                animation: none;
            }
            
            .minimal-animations .ripple-effect {
                display: none;
            }
        `;
    }    // Enhanced accessibility features
    initAccessibility() {
        try {
            // Add ARIA labels and roles to all islands
            this.navIslands.forEach((island, index) => {
                if (!island.getAttribute('role')) {
                    island.setAttribute('role', 'button');
                }
                if (!island.getAttribute('tabindex')) {
                    island.setAttribute('tabindex', '0');
                }
                if (!island.getAttribute('aria-label')) {
                    const label = island.dataset.tooltip || 
                                 island.textContent?.trim() || 
                                 `导航按钮 ${index + 1}`;
                    island.setAttribute('aria-label', label);
                }
                
                // Add keyboard interaction
                island.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        island.click();
                    }
                });
            });

            // Add screen reader instructions
            this.addScreenReaderInstructions();
            
            // Setup focus management
            this.setupFocusManagement();
        } catch (error) {
            console.error('Accessibility initialization failed:', error);
        }
    }
    
    addScreenReaderInstructions() {
        const instructions = document.createElement('div');
        instructions.className = 'sr-only';
        instructions.setAttribute('aria-live', 'polite');
        instructions.innerHTML = `
            <p>动态岛导航系统已加载。使用Tab键导航，Enter或空格键激活。</p>
            <p>键盘快捷键：Alt + 数字键快速导航，Ctrl + K 打开搜索，ESC 关闭面板</p>
        `;
        document.body.appendChild(instructions);
    }
    
    setupFocusManagement() {
        // Trap focus within notification panel when open
        document.addEventListener('keydown', (e) => {
            const notificationPanel = document.querySelector('.notification-panel');
            if (notificationPanel && e.key === 'Tab') {
                this.trapFocus(e, notificationPanel);
            }
        });
    }
    
    trapFocus(event, container) {
        const focusableElements = container.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];
        
        if (event.shiftKey && document.activeElement === firstElement) {
            event.preventDefault();
            lastElement.focus();
        } else if (!event.shiftKey && document.activeElement === lastElement) {
            event.preventDefault();
            firstElement.focus();
        }
    }

    // Performance optimization utilities
    debounce(func, wait, immediate = false) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func(...args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func(...args);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    // Memory management and cleanup
    destroy() {
        try {
            // Cancel any pending animations
            if (this.animationFrameId) {
                cancelAnimationFrame(this.animationFrameId);
            }
            
            // Clear timeouts
            if (this.resizeTimeoutId) {
                clearTimeout(this.resizeTimeoutId);
            }
            if (this.searchTimeoutId) {
                clearTimeout(this.searchTimeoutId);
            }
            
            // Remove event listeners
            window.removeEventListener('scroll', this.handleScroll);
            window.removeEventListener('resize', this.handleResize);
            document.removeEventListener('keydown', this.handleKeyboardNavigation);
            
            // Clear singleton instance
            EnhancedDynamicIsland.instance = null;
            
            console.log('EnhancedDynamicIsland destroyed successfully');
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
    }
}

// Service Worker registration for offline support
class ServiceWorkerManager {
    static async register() {
        if (!('serviceWorker' in navigator)) {
            console.log('Service Worker not supported');
            return;
        }
        
        try {
            const registration = await navigator.serviceWorker.register('/sw.js', {
                scope: '/'
            });
            
            console.log('SW registered successfully:', registration);
            
            // Handle updates
            registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                if (newWorker) {
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            // New content available
                            console.log('New content available, please refresh');
                        }
                    });
                }
            });
            
        } catch (error) {
            console.log('SW registration failed:', error);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // Initialize the dynamic island system
        const dynamicIsland = EnhancedDynamicIsland.getInstance();
        
        // Register service worker
        await ServiceWorkerManager.register();
        
        // Expose to global scope for debugging
        if (process.env.NODE_ENV === 'development' || window.location.hostname === 'localhost') {
            window.dynamicIsland = dynamicIsland;
        }
        
        console.log('Enhanced Dynamic Island system initialized');
    } catch (error) {
        console.error('Failed to initialize Enhanced Dynamic Island system:', error);
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { EnhancedDynamicIsland, ServiceWorkerManager };
}

// AMD support
if (typeof define === 'function' && define.amd) {
    define(['enhanced-dynamic-island'], () => ({ EnhancedDynamicIsland, ServiceWorkerManager }));
}
