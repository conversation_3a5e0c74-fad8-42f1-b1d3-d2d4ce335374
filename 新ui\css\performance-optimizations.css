/* 性能优化样式 */

/* GPU加速优化 */
.gpu-accelerated {
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* 动画性能优化 */
.optimized-animation {
    animation-fill-mode: both;
    animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 减少重绘的过渡效果 */
.smooth-transition {
    transition: transform 0.3s ease, opacity 0.3s ease;
    will-change: transform, opacity;
}

.smooth-transition:not(:hover) {
    will-change: auto;
}

/* 性能模式样式 */
.performance-mode * {
    animation-duration: 0.2s !important;
    transition-duration: 0.2s !important;
}

.performance-mode .floating,
.performance-mode .breathing,
.performance-mode .complex-animation {
    animation: none !important;
}

.performance-mode .particle-canvas {
    display: none !important;
}

.performance-mode .meteor-canvas {
    display: none !important;
}

/* 低端设备优化 */
.reduced-animations * {
    animation: none !important;
    transition: none !important;
}

.reduced-animations .transform-3d {
    transform: none !important;
}

.reduced-animations .card-3d:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
}

/* 页面隐藏时的优化 */
.page-hidden * {
    animation-play-state: paused !important;
}

.page-hidden .particle-canvas,
.page-hidden .meteor-canvas {
    opacity: 0;
    pointer-events: none;
}

/* 电池节省模式 */
.power-save-mode {
    --transition-normal: all 0.1s ease;
    --transition-slow: all 0.15s ease;
    --transition-bounce: all 0.15s ease;
}

.power-save-mode * {
    animation: none !important;
}

.power-save-mode .particle-canvas,
.power-save-mode .meteor-canvas,
.power-save-mode .complex-effect {
    display: none !important;
}

/* 关键性能指标优化 */
.critical-path {
    contain: layout style paint;
}

/* 滚动性能优化 */
.scroll-optimized {
    overflow-anchor: none;
    scroll-behavior: auto;
}

/* 图像加载优化 */
.lazy-load {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lazy-load.loaded {
    opacity: 1;
}

/* 内容可见性优化 */
.off-screen {
    content-visibility: auto;
    contain-intrinsic-size: 200px;
}

/* 字体加载优化 */
@font-face {
    font-family: 'Inter-optimized';
    src: url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    font-display: swap;
}

/* 渐进式增强 */
@supports not (backdrop-filter: blur(10px)) {
    .glassmorphism-btn {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: none;
    }
}

@supports not (transform-style: preserve-3d) {
    .transform-3d {
        transform: none !important;
    }
    
    .card-3d:hover {
        transform: translateY(-5px) scale(1.02) !important;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .hover-effect:hover {
        transform: none;
    }
    
    .touch-optimized {
        min-height: 44px;
        min-width: 44px;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .glassmorphism-btn {
        background: #000;
        color: #fff;
        border: 2px solid #fff;
    }
    
    .card-3d {
        border: 2px solid currentColor;
    }
}

/* 深色模式性能优化 */
@media (prefers-color-scheme: dark) {
    .auto-dark {
        background: #1a1a1a;
        color: #ffffff;
    }
    
    .auto-dark .card {
        background: #2d2d2d;
        border-color: #404040;
    }
}

/* 网络状况优化 */
@media (prefers-reduced-data) {
    .data-heavy {
        display: none;
    }
    
    .particle-canvas,
    .meteor-canvas {
        display: none;
    }
    
    .high-res-bg {
        background-image: none;
        background-color: #6366f1;
    }
}

/* 内存使用优化 */
.memory-efficient {
    contain: strict;
    content-visibility: auto;
}

/* 离屏渲染优化 */
.offscreen-rendered {
    opacity: 0;
    visibility: hidden;
    position: absolute;
    left: -9999px;
}

.offscreen-rendered.visible {
    opacity: 1;
    visibility: visible;
    position: relative;
    left: auto;
}

/* 动画队列管理 */
.animation-queue {
    animation-delay: calc(var(--queue-index, 0) * 0.1s);
}

/* 资源预加载优化 */
.preload-hint::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 1px;
    height: 1px;
    opacity: 0;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg"></svg>');
}

/* 关键渲染路径优化 */
.above-fold {
    contain: layout;
    will-change: auto;
}

.below-fold {
    content-visibility: auto;
    contain-intrinsic-size: 300px;
}

/* 交互反馈优化 */
.fast-feedback {
    transition: transform 0.1s ease;
}

.fast-feedback:active {
    transform: scale(0.98);
}

/* 滚动锁定优化 */
.scroll-locked {
    overflow: hidden;
    height: 100vh;
    touch-action: none;
}

/* 虚拟滚动优化 */
.virtual-scroll {
    overflow-y: auto;
    height: 100%;
    contain: strict;
}

.virtual-scroll-item {
    contain: layout style paint;
    will-change: transform;
}

/* 响应式图片优化 */
.responsive-image {
    width: 100%;
    height: auto;
    aspect-ratio: 16/9;
    object-fit: cover;
    loading: lazy;
    decoding: async;
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .parallax-element {
        transform: none !important;
    }
}

/* 设备特定优化 */
@media (max-width: 767px) {
    .mobile-optimized {
        transform: none !important;
        animation: none !important;
    }
    
    .desktop-only {
        display: none !important;
    }
}

@media (min-width: 768px) {
    .mobile-only {
        display: none !important;
    }
}

/* 内存泄漏防护 */
.cleanup-on-destroy {
    transition: opacity 0.3s ease;
}

.cleanup-on-destroy.destroying {
    opacity: 0;
    pointer-events: none;
    will-change: auto;
}

/* 渲染层合成优化 */
.composite-layer {
    transform: translateZ(0);
    isolation: isolate;
}

/* 布局抖动防护 */
.layout-stable {
    contain: layout;
    min-height: 200px; /* 根据内容调整 */
}

/* CPU密集型任务优化 */
.cpu-intensive {
    contain: strict;
    will-change: contents;
}

/* 网络延迟优化 */
.network-aware {
    transition: background-color 0.3s ease;
}

.network-aware.loading {
    background-color: rgba(255, 255, 255, 0.5);
    pointer-events: none;
}

.network-aware.error {
    background-color: rgba(255, 0, 0, 0.1);
}

/* 实用工具类 */
.will-change-transform { will-change: transform; }
.will-change-opacity { will-change: opacity; }
.will-change-auto { will-change: auto; }

.contain-layout { contain: layout; }
.contain-style { contain: style; }
.contain-paint { contain: paint; }
.contain-size { contain: size; }
.contain-strict { contain: strict; }

.gpu-layer { transform: translateZ(0); }
.hide-backface { backface-visibility: hidden; }

/* 调试性能类 */
.debug-performance {
    outline: 2px solid red;
    background: rgba(255, 0, 0, 0.1) !important;
}

.debug-performance::before {
    content: 'PERF DEBUG';
    position: absolute;
    top: 0;
    left: 0;
    background: red;
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    z-index: 9999;
}

/* 性能监控样式 */
.performance-monitor {
    position: fixed;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-family: monospace;
    font-size: 12px;
    z-index: 10000;
    pointer-events: none;
}

.performance-monitor.hidden {
    display: none;
}

/* 条件加载 */
@media (max-resolution: 150dpi) {
    .high-dpi-only {
        display: none;
    }
}

@media (min-resolution: 150dpi) {
    .low-dpi-only {
        display: none;
    }
}
