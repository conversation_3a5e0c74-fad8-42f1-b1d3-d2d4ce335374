<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="AI面试助手演示 - 展示增强的UI、用户体验和动画效果">
    <title>AI面试助手 - 增强演示版</title>
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" as="style">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- 样式表 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/enhanced-styles.css">
    <link rel="stylesheet" href="css/artboard-styles.css">
    
    <!-- PWA支持 -->
    <meta name="theme-color" content="#6366f1">
    <link rel="manifest" href="manifest.json">
    
    <!-- 图标 -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
</head>
<body class="theme-transition">
    <!-- 动态岛导航系统 -->
    <nav id="dynamic-island-container" role="navigation" aria-label="主导航">
        <!-- Logo岛 -->
        <div class="island logo-island bounce-in" id="logo-island" role="button" tabindex="0" aria-label="返回首页">
            <svg class="icon logo-svg" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L3.09 8.26L12 22l8.91-13.74L12 2z"/>
            </svg>
        </div>

        <!-- 仪表盘岛 -->
        <div class="island nav-island fade-in active" id="nav-dashboard" data-tooltip="仪表盘" role="button" tabindex="0" aria-label="仪表盘">
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
            </svg>
        </div>

        <!-- 模拟面试岛 -->
        <div class="island nav-island fade-in" id="nav-interview" data-tooltip="模拟面试" role="button" tabindex="0" aria-label="模拟面试" style="animation-delay: 0.1s">
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
            </svg>
        </div>

        <!-- AI教练岛 -->
        <div class="island nav-island fade-in" id="nav-coach" data-tooltip="AI智能教练" role="button" tabindex="0" aria-label="AI智能教练" style="animation-delay: 0.2s">
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
        </div>

        <!-- 搜索岛 -->
        <div class="island search-island fade-in" id="search-island" role="search" aria-label="全局搜索" style="animation-delay: 0.3s">
            <svg class="icon search-trigger-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
            </svg>
            <input type="search" id="search-input" placeholder="搜索功能、内容..." autocomplete="off" aria-label="搜索输入框">
            
            <!-- 搜索结果下拉 -->
            <div class="search-results" id="search-results">
                <div class="search-result-item">
                    <div class="search-result-title">模拟面试</div>
                    <div class="search-result-description">开始一场真实的模拟面试体验</div>
                </div>
                <div class="search-result-item">
                    <div class="search-result-title">AI教练指导</div>
                    <div class="search-result-description">获得个性化的面试指导建议</div>
                </div>
                <div class="search-result-item">
                    <div class="search-result-title">面试技巧</div>
                    <div class="search-result-description">学习经典面试问题和回答技巧</div>
                </div>
            </div>
        </div>

        <!-- 通知岛 -->
        <div class="island notification-island fade-in" id="notification-island" data-tooltip="通知中心" role="button" tabindex="0" aria-label="通知中心" style="animation-delay: 0.4s">
             <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                 <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.63-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2zm-2 1H8v-6c0-2.48 1.51-4.5 4-4.5s4 2.02 4 4.5v6z"/>
             </svg>
            <div class="notification-dot" aria-hidden="true"></div>
        </div>

        <!-- 用户岛 -->
        <div class="island user-island fade-in" id="user-island" role="button" tabindex="0" aria-label="用户菜单" style="animation-delay: 0.5s">
            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=32&h=32&q=80" alt="用户头像" class="avatar-img" loading="lazy">
            <div class="user-menu" role="menu" aria-hidden="true">
                <a href="#" role="menuitem" tabindex="-1">
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                    个人中心
                </a>
                <a href="#" role="menuitem" tabindex="-1">
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                    </svg>
                    设置
                </a>
                <a href="#" role="menuitem" tabindex="-1" id="theme-toggle">
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M20 8.69V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12 20 8.69zM12 18c-3.31 0-6-2.69-6-6s2.69-6 6-6 6 2.69 6 6-2.69 6-6 6zm0-10c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4z"/>
                    </svg>
                    主题切换
                    <span class="keyboard-shortcut">Ctrl+T</span>
                </a>
                <hr>
                <a href="#" role="menuitem" tabindex="-1" id="logout-btn">
                    <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                    </svg>
                    退出登录
                </a>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <div class="artboards-container">
            <!-- 仪表盘画板 -->
            <section class="artboard active-artboard" id="dashboard-artboard" role="main">
                <header class="artboard-header">
                    <div>
                        <h1 class="artboard-title">个人仪表盘</h1>
                        <p class="artboard-subtitle">欢迎回来！查看您的学习进度和成就</p>
                    </div>
                    <div>
                        <button class="btn btn-primary" id="start-interview-btn">
                            <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M8 5v14l11-7z"/>
                            </svg>
                            开始面试
                        </button>
                    </div>
                </header>

                <!-- 统计卡片 -->
                <div class="grid grid-4" style="margin-bottom: var(--space-8);">
                    <div class="stat-card bounce-in" style="animation-delay: 0.1s">
                        <div class="stat-number" data-count="23">0</div>
                        <div class="stat-label">完成面试</div>
                    </div>
                    <div class="stat-card bounce-in" style="animation-delay: 0.2s">
                        <div class="stat-number" data-count="87">0</div>
                        <div class="stat-label">技能评分</div>
                    </div>
                    <div class="stat-card bounce-in" style="animation-delay: 0.3s">
                        <div class="stat-number" data-count="156">0</div>
                        <div class="stat-label">学习时长</div>
                    </div>
                    <div class="stat-card bounce-in" style="animation-delay: 0.4s">
                        <div class="stat-number" data-count="12">0</div>
                        <div class="stat-label">获得徽章</div>
                    </div>
                </div>

                <!-- 功能卡片 -->
                <div class="grid grid-2">
                    <div class="card fade-in" style="animation-delay: 0.5s">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
                                </svg>
                            </div>
                            <h3 class="card-title">模拟面试</h3>
                        </div>
                        <p class="card-description">
                            体验真实的面试环境，通过AI智能分析提升您的面试表现。支持多种面试场景和难度级别。
                        </p>
                        <div class="progress">
                            <div class="progress-bar" style="width: 75%"></div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: var(--space-4);">
                            <span class="badge badge-success">进行中</span>
                            <button class="btn btn-outline">继续练习</button>
                        </div>
                    </div>

                    <div class="card fade-in" style="animation-delay: 0.6s">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            <h3 class="card-title">AI智能指导</h3>
                        </div>
                        <p class="card-description">
                            获得个性化的面试建议和改进方案，AI教练将根据您的表现提供精准的指导建议。
                        </p>
                        <div class="progress">
                            <div class="progress-bar" style="width: 60%"></div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: var(--space-4);">
                            <span class="badge badge-primary">已更新</span>
                            <button class="btn btn-secondary">查看建议</button>
                        </div>
                    </div>

                    <div class="card fade-in" style="animation-delay: 0.7s">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                            </div>
                            <h3 class="card-title">技能认证</h3>
                        </div>
                        <p class="card-description">
                            通过专业的技能测试获得权威认证，展示您的专业能力，提升求职竞争力。
                        </p>
                        <div class="progress">
                            <div class="progress-bar" style="width: 40%"></div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: var(--space-4);">
                            <span class="badge badge-warning">待完成</span>
                            <button class="btn btn-primary">开始测试</button>
                        </div>
                    </div>

                    <div class="card fade-in" style="animation-delay: 0.8s">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A2.98 2.98 0 0 0 17.15 7c-.8 0-1.54.5-1.85 1.26l-1.92 5.76A2 2 0 0 0 15.28 16H16v6h4z"/>
                                </svg>
                            </div>
                            <h3 class="card-title">学习社群</h3>
                        </div>
                        <p class="card-description">
                            加入活跃的学习社群，与其他求职者交流经验，分享面试心得，共同进步。
                        </p>
                        <div class="progress">
                            <div class="progress-bar" style="width: 90%"></div>
                        </div>
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: var(--space-4);">
                            <span class="badge badge-success">活跃</span>
                            <button class="btn btn-outline">加入讨论</button>
                        </div>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div class="card fade-in" style="margin-top: var(--space-8); animation-delay: 0.9s">
                    <div class="card-header">
                        <div class="card-icon">
                            <svg class="icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42A8.954 8.954 0 0 0 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"/>
                            </svg>
                        </div>
                        <h3 class="card-title">最近活动</h3>
                    </div>
                    
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>活动</th>
                                    <th>时间</th>
                                    <th>状态</th>
                                    <th>评分</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>前端开发面试</td>
                                    <td>2小时前</td>
                                    <td><span class="badge badge-success">已完成</span></td>
                                    <td>85分</td>
                                </tr>
                                <tr>
                                    <td>算法技能测试</td>
                                    <td>1天前</td>
                                    <td><span class="badge badge-primary">优秀</span></td>
                                    <td>92分</td>
                                </tr>
                                <tr>
                                    <td>行为面试练习</td>
                                    <td>3天前</td>
                                    <td><span class="badge badge-warning">待改进</span></td>
                                    <td>78分</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- 加载提示 -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-spinner"></div>
        <p>正在加载AI面试助手...</p>
    </div>

    <!-- Toast 通知容器 -->
    <div id="toast-container" class="toast-container"></div>

    <!-- 脚本文件 -->
    <script src="js/enhanced-interactions.js"></script>
    
    <!-- 演示脚本 -->
    <script>
        // 数字动画
        function animateNumbers() {
            const counters = document.querySelectorAll('[data-count]');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-count'));
                const increment = target / 50;
                let current = 0;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = target;
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current);
                    }
                }, 30);
            });
        }

        // Toast 通知演示
        function showToast(type, title, message) {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            
            toast.innerHTML = `
                <div class="toast-header">
                    <h4 class="toast-title">${title}</h4>
                    <button class="toast-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
                </div>
                <p class="toast-message">${message}</p>
            `;
            
            container.appendChild(toast);
            
            // 显示动画
            setTimeout(() => toast.classList.add('show'), 100);
            
            // 自动关闭
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 5000);
        }

        // 页面加载完成
        window.addEventListener('load', () => {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.style.opacity = '0';
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                    
                    // 启动动画
                    animateNumbers();
                    
                    // 欢迎消息
                    setTimeout(() => {
                        showToast('success', '欢迎回来！', '您的面试练习数据已同步完成');
                    }, 1000);
                    
                    // 演示搜索功能
                    setTimeout(() => {
                        showToast('info', '小贴士', '试试按 Ctrl+K 快速搜索功能');
                    }, 3000);
                    
                }, 500);
            }
        });

        // 开始面试按钮演示
        document.getElementById('start-interview-btn')?.addEventListener('click', () => {
            showToast('primary', '面试开始', '正在为您准备个性化的面试题目...');
        });

        // 主题切换演示
        document.getElementById('theme-toggle')?.addEventListener('click', (e) => {
            e.preventDefault();
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            showToast('info', '主题已切换', `已切换到${newTheme === 'dark' ? '深色' : '浅色'}模式`);
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 't') {
                e.preventDefault();
                document.getElementById('theme-toggle')?.click();
            }
        });
    </script>
</body>
</html>
