// 高级手势和触摸交互系统
class AdvancedGestureHandler {
    constructor() {
        this.touches = {};
        this.gestures = {
            swipe: { enabled: true, threshold: 50, velocity: 0.3 },
            pinch: { enabled: true, threshold: 10 },
            rotate: { enabled: true, threshold: 15 },
            longPress: { enabled: true, duration: 500 }
        };
        this.callbacks = {};
        this.longPressTimer = null;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
    }
    
    bindEvents() {
        // 触摸事件
        document.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: false });
        document.addEventListener('touchmove', (e) => this.handleTouchMove(e), { passive: false });
        document.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: false });
        
        // 鼠标事件（模拟触摸）
        document.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        document.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        document.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        
        // 阻止默认的触摸行为
        document.addEventListener('touchmove', (e) => {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        }, { passive: false });
    }
    
    handleTouchStart(e) {
        for (let i = 0; i < e.changedTouches.length; i++) {
            const touch = e.changedTouches[i];
            this.touches[touch.identifier] = {
                startX: touch.clientX,
                startY: touch.clientY,
                currentX: touch.clientX,
                currentY: touch.clientY,
                startTime: Date.now(),
                element: e.target
            };
        }
        
        // 开始长按检测
        if (this.gestures.longPress.enabled) {
            this.startLongPressDetection(e);
        }
        
        this.updateGestureData();
    }
    
    handleTouchMove(e) {
        for (let i = 0; i < e.changedTouches.length; i++) {
            const touch = e.changedTouches[i];
            if (this.touches[touch.identifier]) {
                this.touches[touch.identifier].currentX = touch.clientX;
                this.touches[touch.identifier].currentY = touch.clientY;
            }
        }
        
        this.clearLongPress();
        this.detectGestures();
    }
    
    handleTouchEnd(e) {
        for (let i = 0; i < e.changedTouches.length; i++) {
            const touch = e.changedTouches[i];
            if (this.touches[touch.identifier]) {
                this.detectSwipe(this.touches[touch.identifier]);
                delete this.touches[touch.identifier];
            }
        }
        
        this.clearLongPress();
    }
    
    // 鼠标事件处理（用于桌面测试）
    handleMouseDown(e) {
        this.touches['mouse'] = {
            startX: e.clientX,
            startY: e.clientY,
            currentX: e.clientX,
            currentY: e.clientY,
            startTime: Date.now(),
            element: e.target
        };
        
        if (this.gestures.longPress.enabled) {
            this.startLongPressDetection(e);
        }
    }
    
    handleMouseMove(e) {
        if (this.touches['mouse']) {
            this.touches['mouse'].currentX = e.clientX;
            this.touches['mouse'].currentY = e.clientY;
            this.clearLongPress();
        }
    }
    
    handleMouseUp(e) {
        if (this.touches['mouse']) {
            this.detectSwipe(this.touches['mouse']);
            delete this.touches['mouse'];
        }
        this.clearLongPress();
    }
    
    // 手势检测
    detectSwipe(touch) {
        if (!this.gestures.swipe.enabled) return;
        
        const deltaX = touch.currentX - touch.startX;
        const deltaY = touch.currentY - touch.startY;
        const deltaTime = Date.now() - touch.startTime;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const velocity = distance / deltaTime;
        
        if (distance > this.gestures.swipe.threshold && velocity > this.gestures.swipe.velocity) {
            const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;
            let direction;
            
            if (angle >= -45 && angle <= 45) direction = 'right';
            else if (angle >= 45 && angle <= 135) direction = 'down';
            else if (angle >= -135 && angle <= -45) direction = 'up';
            else direction = 'left';
            
            this.triggerCallback('swipe', {
                direction,
                distance,
                velocity,
                deltaX,
                deltaY,
                element: touch.element
            });
        }
    }
    
    detectPinch() {
        if (!this.gestures.pinch.enabled) return;
        
        const touchIds = Object.keys(this.touches);
        if (touchIds.length !== 2) return;
        
        const touch1 = this.touches[touchIds[0]];
        const touch2 = this.touches[touchIds[1]];
        
        const currentDistance = this.getDistance(
            touch1.currentX, touch1.currentY,
            touch2.currentX, touch2.currentY
        );
        
        const startDistance = this.getDistance(
            touch1.startX, touch1.startY,
            touch2.startX, touch2.startY
        );
        
        const scale = currentDistance / startDistance;
        const deltaScale = Math.abs(scale - 1);
        
        if (deltaScale > this.gestures.pinch.threshold / 100) {
            this.triggerCallback('pinch', {
                scale,
                deltaScale,
                centerX: (touch1.currentX + touch2.currentX) / 2,
                centerY: (touch1.currentY + touch2.currentY) / 2
            });
        }
    }
    
    detectRotate() {
        if (!this.gestures.rotate.enabled) return;
        
        const touchIds = Object.keys(this.touches);
        if (touchIds.length !== 2) return;
        
        const touch1 = this.touches[touchIds[0]];
        const touch2 = this.touches[touchIds[1]];
        
        const currentAngle = this.getAngle(
            touch1.currentX, touch1.currentY,
            touch2.currentX, touch2.currentY
        );
        
        const startAngle = this.getAngle(
            touch1.startX, touch1.startY,
            touch2.startX, touch2.startY
        );
        
        const rotation = currentAngle - startAngle;
        
        if (Math.abs(rotation) > this.gestures.rotate.threshold) {
            this.triggerCallback('rotate', {
                rotation,
                centerX: (touch1.currentX + touch2.currentX) / 2,
                centerY: (touch1.currentY + touch2.currentY) / 2
            });
        }
    }
    
    startLongPressDetection(e) {
        this.clearLongPress();
        this.longPressTimer = setTimeout(() => {
            this.triggerCallback('longPress', {
                x: e.touches ? e.touches[0].clientX : e.clientX,
                y: e.touches ? e.touches[0].clientY : e.clientY,
                element: e.target
            });
        }, this.gestures.longPress.duration);
    }
    
    clearLongPress() {
        if (this.longPressTimer) {
            clearTimeout(this.longPressTimer);
            this.longPressTimer = null;
        }
    }
    
    detectGestures() {
        this.detectPinch();
        this.detectRotate();
    }
    
    updateGestureData() {
        // 更新手势数据
    }
    
    // 工具函数
    getDistance(x1, y1, x2, y2) {
        return Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
    }
    
    getAngle(x1, y1, x2, y2) {
        return Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;
    }
    
    // 回调管理
    on(gesture, callback) {
        if (!this.callbacks[gesture]) {
            this.callbacks[gesture] = [];
        }
        this.callbacks[gesture].push(callback);
    }
    
    off(gesture, callback) {
        if (this.callbacks[gesture]) {
            const index = this.callbacks[gesture].indexOf(callback);
            if (index > -1) {
                this.callbacks[gesture].splice(index, 1);
            }
        }
    }
    
    triggerCallback(gesture, data) {
        if (this.callbacks[gesture]) {
            this.callbacks[gesture].forEach(callback => callback(data));
        }
    }
    
    // 配置手势
    configure(gestureType, options) {
        if (this.gestures[gestureType]) {
            Object.assign(this.gestures[gestureType], options);
        }
    }
}

// 高级视觉反馈系统
class VisualFeedbackSystem {
    constructor() {
        this.feedbackElements = new Map();
        this.init();
    }
    
    init() {
        // 创建样式
        this.createStyles();
    }
    
    createStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .visual-feedback {
                position: absolute;
                pointer-events: none;
                z-index: 9999;
                border-radius: 50%;
                transform: scale(0);
                opacity: 0.7;
            }
            
            .ripple-effect {
                background: radial-gradient(circle, rgba(99, 102, 241, 0.3) 0%, transparent 70%);
                animation: ripple 0.6s ease-out forwards;
            }
            
            .pulse-effect {
                background: rgba(99, 102, 241, 0.2);
                border: 2px solid rgba(99, 102, 241, 0.5);
                animation: pulse 1s ease-in-out infinite;
            }
            
            .glow-effect {
                background: rgba(99, 102, 241, 0.1);
                box-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
                animation: glow 0.8s ease-out forwards;
            }
            
            @keyframes ripple {
                0% {
                    transform: scale(0);
                    opacity: 0.7;
                }
                100% {
                    transform: scale(1);
                    opacity: 0;
                }
            }
            
            @keyframes pulse {
                0%, 100% {
                    transform: scale(1);
                    opacity: 0.7;
                }
                50% {
                    transform: scale(1.1);
                    opacity: 1;
                }
            }
            
            @keyframes glow {
                0% {
                    transform: scale(0.8);
                    opacity: 0;
                }
                50% {
                    transform: scale(1.1);
                    opacity: 0.8;
                }
                100% {
                    transform: scale(1);
                    opacity: 0;
                }
            }
            
            .shake-effect {
                animation: shake 0.5s ease-in-out;
            }
            
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
                20%, 40%, 60%, 80% { transform: translateX(2px); }
            }
            
            .bounce-effect {
                animation: bounce 0.6s ease-out;
            }
            
            @keyframes bounce {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.05); }
            }
        `;
        document.head.appendChild(style);
    }
    
    createRipple(x, y, size = 100, color = 'rgba(99, 102, 241, 0.3)') {
        const ripple = document.createElement('div');
        ripple.className = 'visual-feedback ripple-effect';
        ripple.style.left = (x - size / 2) + 'px';
        ripple.style.top = (y - size / 2) + 'px';
        ripple.style.width = size + 'px';
        ripple.style.height = size + 'px';
        ripple.style.background = `radial-gradient(circle, ${color} 0%, transparent 70%)`;
        
        document.body.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
        
        return ripple;
    }
    
    createPulse(element, duration = 1000) {
        element.classList.add('pulse-effect');
        
        setTimeout(() => {
            element.classList.remove('pulse-effect');
        }, duration);
    }
    
    createGlow(x, y, size = 80) {
        const glow = document.createElement('div');
        glow.className = 'visual-feedback glow-effect';
        glow.style.left = (x - size / 2) + 'px';
        glow.style.top = (y - size / 2) + 'px';
        glow.style.width = size + 'px';
        glow.style.height = size + 'px';
        
        document.body.appendChild(glow);
        
        setTimeout(() => {
            glow.remove();
        }, 800);
        
        return glow;
    }
    
    shake(element) {
        element.classList.add('shake-effect');
        setTimeout(() => {
            element.classList.remove('shake-effect');
        }, 500);
    }
    
    bounce(element) {
        element.classList.add('bounce-effect');
        setTimeout(() => {
            element.classList.remove('bounce-effect');
        }, 600);
    }
    
    // 自适应触摸反馈
    addTouchFeedback(element, type = 'ripple') {
        element.addEventListener('touchstart', (e) => {
            const touch = e.touches[0];
            const rect = element.getBoundingClientRect();
            const x = touch.clientX;
            const y = touch.clientY;
            
            switch (type) {
                case 'ripple':
                    this.createRipple(x, y);
                    break;
                case 'glow':
                    this.createGlow(x, y);
                    break;
                case 'pulse':
                    this.createPulse(element);
                    break;
                case 'bounce':
                    this.bounce(element);
                    break;
            }
        });
        
        // 鼠标点击也添加反馈
        element.addEventListener('mousedown', (e) => {
            const x = e.clientX;
            const y = e.clientY;
            
            switch (type) {
                case 'ripple':
                    this.createRipple(x, y);
                    break;
                case 'glow':
                    this.createGlow(x, y);
                    break;
            }
        });
    }
}

// 导出类
window.AdvancedGestureHandler = AdvancedGestureHandler;
window.VisualFeedbackSystem = VisualFeedbackSystem;
