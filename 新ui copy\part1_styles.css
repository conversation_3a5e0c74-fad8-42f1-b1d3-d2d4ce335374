/* This file will contain styles for artboards and components from part1.html */

        /* CSS变量 - 根据低饱和配色方案 */
        :root {
            /* 颜色系统 */
            --color-bg-primary: #FAF7F5;
            --color-bg-secondary: #F8F5F2;
            --color-bg-tertiary: #F2F2F2;
            --color-text-primary: #333333;
            --color-text-secondary: #4A4A4A;
            --color-text-tertiary: #757575;
            --color-accent-orange: #FF7A59;
            --color-accent-orange-light: #FFA07A;
            --color-accent-blue: #4A90E2;
            --color-accent-blue-light: #6DA3E9;
            --color-accent-gold: #FFD700;
            --color-accent-green: #4CAF50;
            --color-accent-gray: #F2F2F2;
            --color-bg-glass: rgba(255,255,255,0.6);
            --color-bg-glass-dark: rgba(50,50,50,0.5);
            --color-success: #4CAF50;
            --color-warning: #FF9800;
            --color-error: #F44336;
            --color-white: #FFFFFF;
            
            /* 字体系统 */
            --font-family: 'Inter', 'Roboto', 'Source <PERSON>', sans-serif;
            --font-size-xs: 12px;
            --font-size-sm: 14px;
            --font-size-md: 16px;
            --font-size-lg: 20px;
            --font-size-xl: 24px;
            --font-size-xxl: 36px;
            --font-weight-light: 300;
            --font-weight-regular: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
            
            /* 间距和尺寸 */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;
            --spacing-xl: 32px;
            --spacing-xxl: 48px;
            --border-radius-xs: 4px;
            --border-radius-sm: 8px;
            --border-radius-md: 12px;
            --border-radius-lg: 16px;
            --border-radius-xl: 24px;
            --border-radius-full: 9999px;
            
            /* 阴影 */
            --shadow-xs: 0 1px 3px rgba(0,0,0,0.04);
            --shadow-sm: 0 2px 8px rgba(0,0,0,0.05);
            --shadow-md: 0 4px 12px rgba(0,0,0,0.08);
            --shadow-lg: 0 8px 24px rgba(0,0,0,0.12);
            --shadow-xl: 0 16px 48px rgba(0,0,0,0.16);
            --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.05);
            
            /* 过渡和动画 */
            --transition-fast: 0.2s cubic-bezier(0.4,0,0.2,1);
            --transition-normal: 0.3s cubic-bezier(0.4,0,0.2,1);
            --transition-slow: 0.5s cubic-bezier(0.4,0,0.2,1);
        }
        
        /* 全局重置和基础样式 */
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        html { /* Target only html for scroll-behavior and base font-family */
            font-family: var(--font-family);
            scroll-behavior: smooth;
        }
        
        body {
            font-size: var(--font-size-md);
            line-height: 1.7;
            color: var(--color-text-primary);
            background-color: var(--color-bg-primary);
            min-height: 100vh;
            padding-top: 70px; /* 优化顶部留白，原为100px */
            letter-spacing: 0.01em;
        }
        
        /* 画板容器 */
        .artboards-container {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-xl);
            padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-xl) var(--spacing-xl); /* 优化顶部padding，原为var(--spacing-xxl) */
            justify-content: center;
            width: 100%;
            box-sizing: border-box;
            background: none;
        }
        
        /* 单个画板 */
        .artboard {
            width: 100%;
            max-width: 1200px;
            min-height: 700px;
            height: auto;
            margin-bottom: var(--spacing-xxl);
            background-color: transparent;
            overflow-x: hidden;
            overflow-y: auto;
            position: relative;
            box-shadow: none;
            display: none;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl) var(--spacing-lg);
        }
        
        .artboard.active-artboard {
            display: flex;
        }
        
        /* 画板标题 (Original was absolute, might not be needed if titles are inside artboards) */
        /* .artboard-title { ... } */ 
        /* If titles are inside artboards, style them like .artboard > .artboard-header h2 or similar */
        
        /* 玻璃态效果 */
        .glass {
            background-color: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(10px) saturate(150%);
            -webkit-backdrop-filter: blur(10px) saturate(150%);
            border-radius: var(--border-radius-md);
            border: 1px solid rgba(255, 255, 255, 0.25);
            box-shadow: var(--shadow-sm);
        }
        
        /* 卡片基础样式 (applied to .dashboard-card, .sidebar-module etc.) */
        .card, .glass-card, .dashboard-card, .sidebar-module, .profile-card, .settings-card, .gamification-card, .practice-area-container, .ai-coach-chat-area, .ai-coach-sessions-list, .interview-main-panel, .interview-sidebar {
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            background: var(--color-bg-glass);
            padding: var(--spacing-xl) var(--spacing-lg);
            border: none;
            transition: box-shadow var(--transition-normal), transform var(--transition-normal), background var(--transition-fast);
        }
        .card:hover, .glass-card:hover, .dashboard-card:hover, .sidebar-module:hover, .profile-card:hover, .settings-card:hover, .gamification-card:hover, .practice-area-container:hover, .ai-coach-chat-area:hover, .ai-coach-sessions-list:hover, .interview-main-panel:hover, .interview-sidebar:hover {
            box-shadow: var(--shadow-xl);
            transform: translateY(-2px) scale(1.02);
        }
        
        /* 按钮样式 */
        .btn, .btn-primary, .btn-secondary, .btn-outline, .btn-link, .btn-tertiary, .btn-danger, .btn-danger-outline, .btn-block {
            border-radius: var(--border-radius-full);
            font-weight: var(--font-weight-medium);
            box-shadow: var(--shadow-xs);
            border: none;
            transition: background var(--transition-fast), color var(--transition-fast), transform var(--transition-fast), box-shadow var(--transition-fast);
        }
        
        .btn-primary {
            background: var(--color-accent-orange);
            color: var(--color-white);
        }
        .btn-primary:hover {
            background: var(--color-accent-orange-light);
            transform: scale(1.03);
        }
        
        .btn-secondary {
            background: var(--color-accent-blue);
            color: var(--color-white);
        }
        .btn-secondary:hover {
            background: var(--color-accent-blue-light);
            transform: scale(1.03);
        }
        
        .btn-outline {
            background: var(--color-bg-primary);
            color: var(--color-accent-blue);
            border: 1px solid var(--color-accent-blue-light);
        }
        .btn-outline:hover {
            background: var(--color-accent-blue-light);
            color: var(--color-white);
        }
        
        .btn-link {
            background-color: transparent;
            color: var(--color-accent-blue);
            border: none;
            padding: 0;
            text-decoration: underline;
            font-weight: var(--font-weight-regular);
        }
        .btn-link:hover {
            color: var(--color-accent-blue-light);
        }
        
        .btn-small {
            padding: var(--spacing-xs) var(--spacing-md);
            font-size: var(--font-size-xs);
            border-radius: 6px; /* Smaller radius for small buttons */
        }
        .btn-small svg {
            width: 14px;
            height: 14px;
        }
        
        .btn-icon {
            background-color: transparent;
            border: none;
            padding: var(--spacing-xs);
            color: var(--color-text-secondary);
            cursor: pointer;
            border-radius: var(--border-radius-full); /* Make icon buttons circular */
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 32px; /* Fixed size for consistency */
            height: 32px;
        }
        .btn-icon:hover {
            color: var(--color-text-primary);
            background-color: var(--color-bg-tertiary);
        }
        .btn-icon svg {
            width: 20px; /* Icon size for icon buttons */
            height: 20px;
        }
        
        /* Forms */
        .form-group {
            margin-bottom: var(--spacing-md);
        }
        .form-label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
        }
        .form-input, .form-textarea, .form-select, .message-input-area .form-textarea {
            border-radius: var(--border-radius-md);
            border: none;
            background: var(--color-bg-secondary);
            color: var(--color-text-primary);
            font-size: var(--font-size-md);
            padding: var(--spacing-sm) var(--spacing-md);
            box-shadow: var(--shadow-xs);
            margin-bottom: var(--spacing-sm);
        }
        .form-input:focus, .form-textarea:focus, .form-select:focus, .message-input-area .form-textarea:focus {
            outline: 2px solid var(--color-accent-blue-light);
            background: var(--color-white);
        }
        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }
        .form-input[disabled] {
            background-color: var(--color-bg-secondary);
            cursor: not-allowed;
        }
        
        /* Scrollable content utility */
        .scrollable-content {
            overflow-y: auto;
            -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
        }
        .scrollable-content::-webkit-scrollbar {
            width: 6px;
        }
        .scrollable-content::-webkit-scrollbar-thumb {
            background-color: var(--color-text-tertiary);
            border-radius: var(--border-radius-full);
        }
        .scrollable-content::-webkit-scrollbar-track {
            background-color: var(--color-bg-secondary);
        }
        
        /* General page layout helpers from dynamic_island_ui.html that are now part of artboards */
        /* Note: The .artboard itself handles display logic now. */
        .main-content { /* This is inside .page-container */
            width: 100%;
        }
        
        /* Styles for specific elements from part1.html's head style, not covered by general tags */
        /* For example, if there were specific styles for .dropdown-menu-item, .dropdown-divider, etc. */
        /* These were in the part1.html <style> from line ~230 to line 1116 if my memory of chunk reads is correct */
        /* I will add a placeholder here, as I don't have the exact CSS from that full <style> block in one piece. */
        
        /* Placeholder for other specific styles from part1.html's head */
        /* Example: .dropdown-menu-item, .dropdown-divider specific styles would go here */
        /* Ensure these do not conflict with dynamic island's dropdowns if they are similarly named. */
        
        /* Styles for .smart-top-bar and .feature-curtain-overlay and their children from part1.html are intentionally omitted */
        /* as these HTML elements will not be used. */
        
        /* Any specific component styles from the various artboards in dynamic_island_ui.html (the 743 line file) */
        /* should ideally be here or in style.css if they are not already covered by global tags. */
        /* Example: .dashboard-grid, .video-area, .message-list etc. */
        /* Many of these are directly styled with utility classes or inline styles in the HTML provided, */
        /* but if there were dedicated CSS blocks, they'd go here or style.css */
        
        /* Add styles for .active-artboard to ensure it's visible */
        /* This is already handled by .artboard.active-artboard { display: flex; } */

        /* 画板容器 - 现在是 .page-container > .main-content > .artboards-container */
        .page-container {
            /* padding-top: 70px; */ /* 为 dynamic island 预留空间 - 由 style.css body padding-top 控制 */
            background-color: var(--color-bg-primary);
            min-height: calc(100vh - 70px); /* 这个70px可能也需要根据实际动态岛高度调整，或者让内容撑开 */
        }
        .main-content {
            max-width: 100%; /* part1 内容区可以更宽 */
            margin: 0 auto;
            padding: var(--spacing-lg);
        }

        .artboards-container {
            display: flex; /* 使得内部的 artboard 可以横向排列或按需排列 */
            flex-direction: column; /* 让 artboard 垂直堆叠 */
            gap: var(--spacing-xl); /* artboard 之间的间距 */
            align-items: center; /* 使 artboard 在容器内居中 */
        }
        
        /* 画板标题 - 移除绝对定位，作为 artboard 内的普通流元素 */
        .artboard-header { /* 通用画板头部样式 */
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--color-text-primary);
            padding-bottom: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            border-bottom: 1px solid var(--color-bg-tertiary);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .artboard-header h2 { /* 针对artboard内旧的h2标题 */
             font-size: var(--font-size-xl);
             font-weight: var(--font-weight-semibold);
             color: var(--color-text-primary);
             margin:0;
        }
        
        /* 玻璃态效果 */
        .glass {
            background-color: rgba(255, 255, 255, 0.75); /* 增加不透明度 */
            backdrop-filter: blur(12px) saturate(160%); /* 增强模糊和饱和度 */
            border-radius: var(--border-radius-md);
            box-shadow: 
                0 6px 12px rgba(0, 0, 0, 0.08), /* 稍微增强阴影 */
                inset 0 0 0 0.5px rgba(255, 255, 255, 0.7); /* 更细微的内边框 */
            border: 1px solid rgba(255, 255, 255, 0.2); /* 添加一个几乎看不见的边框增加质感 */
        }
        
        /* 按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-sm) var(--spacing-lg);
            border-radius: var(--border-radius-sm);
            border: 1px solid transparent;
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-fast);
            text-decoration: none;
            gap: var(--spacing-sm);
        }
        .btn svg {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }
        
        .btn-primary {
            background-color: var(--color-accent-orange);
            color: var(--color-white);
            border-color: var(--color-accent-orange);
        }
        .btn-primary:hover {
            background-color: #FF6347; /* 略深的橙色 */
            border-color: #FF6347;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .btn-secondary {
            background-color: var(--color-accent-blue);
            color: var(--color-white);
            border-color: var(--color-accent-blue);
        }
        .btn-secondary:hover {
            background-color: #3A82D2; /* 略深的蓝色 */
            border-color: #3A82D2;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .btn-outline {
            background-color: transparent;
            color: var(--color-text-secondary); /* 默认文字颜色稍深 */
            border: 1px solid var(--color-bg-tertiary); /* 边框颜色更柔和 */
        }

        .btn-outline:hover {
            background-color: var(--color-bg-secondary);
            color: var(--color-text-primary);
            border-color: var(--color-text-tertiary); /* 悬浮时边框颜色 */
        }
        
        .btn-link {
            background-color: transparent;
            color: var(--color-accent-blue);
            border: none;
            padding: 0;
            text-decoration: none; /* 默认无下划线 */
            font-weight: var(--font-weight-medium);
        }
        
        .btn-link:hover {
            color: var(--color-accent-blue-light);
            text-decoration: underline; /* 悬浮时显示下划线 */
        }

        .btn-sm { /* 统一小按钮样式 */
            padding: var(--spacing-xs) var(--spacing-md);
            font-size: var(--font-size-xs);
            border-radius: var(--border-radius-sm); /* 小按钮圆角略小 */
        }
        .btn-lg {
            padding: var(--spacing-md) var(--spacing-xl);
            font-size: var(--font-size-md);
        }

        .btn-icon {
            background-color: transparent;
            border: none;
            padding: var(--spacing-xs);
            color: var(--color-text-tertiary); /* 图标默认颜色 */
            cursor: pointer;
            display: inline-flex; /* 确保图标居中 */
            align-items: center;
            justify-content: center;
            border-radius: var(--border-radius-full); /* 圆形悬浮背景 */
            width: 36px; /* 固定尺寸 */
            height: 36px;
        }
        .btn-icon:hover {
            color: var(--color-text-primary);
            background-color: var(--color-bg-secondary); /* 悬浮背景色 */
        }
        .btn-icon.btn-sm {
             width: 32px; height: 32px;
        }
        .btn-icon svg {
            width: 20px; /* 默认图标大小 */
            height: 20px;
        }
        .btn-icon.btn-sm svg {
             width: 16px; height: 16px;
        }
        .btn-block {
            display: block;
            width: 100%;
            margin-bottom: var(--spacing-sm);
        }
        .btn-danger {
            background-color: var(--color-error);
            color: white;
            border-color: var(--color-error);
        }
        .btn-danger:hover {
            background-color: #E53935; /* 深一点的红色 */
            border-color: #E53935;
             transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn-danger-outline {
            background-color: transparent;
            color: var(--color-error);
            border: 1px solid var(--color-error);
        }
        .btn-danger-outline:hover {
            background-color: rgba(244, 67, 54, 0.1); /* 红色背景，低透明度 */
            color: #E53935;
        }
        .btn-tertiary { /* 针对仪表盘快速开始按钮 */
            background-color: var(--color-bg-secondary);
            color: var(--color-text-secondary);
            border: 1px solid var(--color-bg-tertiary);
        }
        .btn-tertiary:hover {
            background-color: var(--color-bg-tertiary);
            color: var(--color-text-primary);
            border-color: #DDD;
        }
        
        /* 表单样式 */
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--border-radius-sm); 
            border: 1px solid var(--color-bg-tertiary); /* 更柔和的边框 */
            background-color: var(--color-bg-secondary); 
            font-size: var(--font-size-sm);
            color: var(--color-text-primary);
            transition: all var(--transition-fast);
            box-shadow: var(--shadow-inner); /* 轻微内阴影 */
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--color-accent-blue); 
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2); /* 焦点辉光效果 */
            background-color: var(--color-white);
        }
        .form-input::placeholder, .form-textarea::placeholder {
            color: var(--color-text-tertiary);
            opacity: 0.7;
        }
        
        .form-label {
            display: block;
            margin-bottom: var(--spacing-xs);
            font-weight: var(--font-weight-medium);
            color: var(--color-text-secondary);
            font-size: var(--font-size-sm); /* 统一标签字体大小 */
        }

        .form-group {
            margin-bottom: var(--spacing-lg); /* 表单组间距稍大 */
        }
        .form-group-full { /* 用于占满整行的表单组，如个人简介 */
            grid-column: 1 / -1; 
        }
        .form-grid { /* 用于profile等页面的表单布局 */
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-md) var(--spacing-lg);
        }
        
        .checkbox-group, .form-switch { /* 改进 form-switch */
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            cursor: pointer;
        }
        .form-switch input[type="checkbox"] {
            appearance: none;
            width: 40px;
            height: 22px;
            background-color: var(--color-bg-tertiary);
            border-radius: var(--border-radius-full);
            position: relative;
            cursor: pointer;
            transition: background-color var(--transition-fast);
        }
        .form-switch input[type="checkbox"]::before {
            content: '';
            position: absolute;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background-color: white;
            top: 2px;
            left: 2px;
            transition: transform var(--transition-fast);
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }
        .form-switch input[type="checkbox"]:checked {
            background-color: var(--color-success);
        }
        .form-switch input[type="checkbox"]:checked::before {
            transform: translateX(18px);
        }
        .form-switch span {
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
        }

        /* 辅助类 */
        .text-center { text-align: center; }
        .text-muted { color: var(--color-text-tertiary) !important; } /* 增加!important 以覆盖特定组件内的颜色 */
        .text-muted-sm { font-size: var(--font-size-xs); color: var(--color-text-tertiary); }

        .mt-1 { margin-top: var(--spacing-sm) !important; }
        .mt-2 { margin-top: var(--spacing-md) !important; }
        .mb-1 { margin-bottom: var(--spacing-sm) !important; }
        .mb-2 { margin-bottom: var(--spacing-md) !important; }
        .ml-auto { margin-left: auto !important; }

        /* SVG图标 */
        .icon { /* 确保与dynamic island的.icon不冲突，但大多数情况下part1的HTML会用<use> */
            display: inline-block;
            width: 1em; 
            height: 1em;
            vertical-align: -0.15em; /* 更好地对齐 */
            fill: currentColor;
        }
        .avatar-icon { /* 用于聊天等处的头像SVG */
             width: 32px; height: 32px; border-radius: 50%; flex-shrink: 0; fill: var(--color-text-tertiary);
        }
        .avatar-icon use { /* 如果SVG use currentColor不生效 */
            fill: currentColor;
        }


        /* 移除了原画板的特定容器样式，因为现在artboard是顶级内容块 */
        /* .login-container, ..., .positive-psychology-container */

        /* 针对 artboard 内部模块的通用结构 */
        .module-header { /* 用于替换旧的 .ai-coach-header 等 */
            padding: var(--spacing-md) 0; /* 上下padding，左右通过artboard padding控制 */
            margin-bottom: var(--spacing-lg); 
            flex-shrink: 0; 
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .module-header h3 {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            margin: 0;
        }

        .module-main-content { /* 用于替换旧的 .chat-area 等 */
             flex-grow: 1; 
             overflow-y: auto; 
             display: flex; 
             gap: var(--spacing-lg);
        }
        
        /* 各种具体模块的特殊布局，尽量通用化 */
        /* 例如 AI Coach, Skill Practice 等两栏或三栏布局 */
        .two-column-layout {
            display: flex;
            gap: var(--spacing-lg);
            width:100%;
            height: 100%; /* 确保填满 artboard 高度 */
        }
        .sidebar-column {
            flex: 0 0 280px; /* 固定侧边栏宽度 */
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            overflow-y: auto;
            padding-right: var(--spacing-md); /* 给滚动条留空间 */
        }
        .main-column {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            overflow-y: auto;
        }
        /* 如果侧边栏在右边 */
        .two-column-layout.sidebar-right .sidebar-column {
            order: 1;
            padding-left: var(--spacing-md);
            padding-right: 0;
        }


        .placeholder-content {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px; /* 确保占位符有一定高度 */
            height: 100%;
            font-size: var(--font-size-md); /* 调整字体大小 */
            color: var(--color-text-tertiary);
            border: 2px dashed var(--color-bg-tertiary);
            border-radius: var(--border-radius-md);
            background-color: var(--color-bg-secondary); /* 轻微背景色 */
            text-align: center;
            padding: var(--spacing-lg);
        }
        
        /* AI Coach specific styles */
        /* .ai-coach-container 已经在 dynamic_island_ui.html 中 */
        /* .ai-coach-sessions-list 已被 .sidebar-column 和 .glass-card 替代大部分 */
        .ai-coach-container {
            display: flex;
            gap: var(--spacing-xl);
            width: 100%;
            margin-top: var(--spacing-lg);
        }

        .ai-coach-sessions-list {
            background: var(--color-bg-glass);
            box-shadow: var(--shadow-lg);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl) var(--spacing-lg);
            min-width: 320px;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }

        .ai-coach-session-item {
            background: var(--color-bg-secondary);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-sm);
            box-shadow: var(--shadow-xs);
            cursor: pointer;
            transition: background var(--transition-fast), box-shadow var(--transition-fast);
        }
        .ai-coach-session-item.active {
            background: var(--color-accent-blue-light);
            color: var(--color-white);
            box-shadow: var(--shadow-md);
        }
        .ai-coach-session-item:hover {
            background: var(--color-accent-blue);
            color: var(--color-white);
        }

        .ai-coach-chat-area {
            background: var(--color-bg-glass);
            box-shadow: var(--shadow-lg);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl) var(--spacing-lg);
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .message-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            padding: var(--spacing-lg) 0;
            overflow-y: auto;
            max-height: 400px;
        }

        .message-bubble {
            background: var(--color-bg-secondary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-xs);
            padding: var(--spacing-md);
            color: var(--color-text-primary);
            font-size: var(--font-size-md);
            max-width: 80%;
            word-break: break-word;
            transition: background var(--transition-fast);
        }
        .ai-message .message-bubble {
            background: var(--color-accent-blue-light);
            color: var(--color-white);
            align-self: flex-start;
        }
        .user-message .message-bubble {
            background: var(--color-accent-orange-light);
            color: var(--color-white);
            align-self: flex-end;
        }

        .message-input-area {
            background: var(--color-bg-secondary);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-sm);
            padding: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        .message-input-area .form-textarea {
            border-radius: var(--border-radius-md);
            border: none;
            background: var(--color-bg-primary);
            color: var(--color-text-primary);
            font-size: var(--font-size-md);
            flex: 1;
            resize: none;
            padding: var(--spacing-sm) var(--spacing-md);
        }
        .message-input-actions .btn-primary {
            background: var(--color-accent-orange);
            color: var(--color-white);
            border-radius: var(--border-radius-full);
            font-weight: var(--font-weight-medium);
            box-shadow: var(--shadow-xs);
            border: none;
            transition: background var(--transition-fast), transform var(--transition-fast);
        }
        .message-input-actions .btn-primary:hover {
            background: var(--color-accent-orange-light);
            transform: scale(1.03);
        }


        /* Skill Practice Specific */
        /* .skill-practice-container 在 dynamic_island_ui.html 中 */
        .skill-practice-header { /* 这个是技能练习顶部的header, 和artboard-header不同 */
             display: flex; align-items: center; justify-content: space-between;
             padding-bottom: var(--spacing-md);
             margin-bottom: var(--spacing-lg);
             border-bottom: 1px solid var(--color-bg-tertiary);
        }
         .skill-practice-header h2 { margin:0; font-size: var(--font-size-xl); }


        /* .skill-categories and .skill-points-grid are for the new layout */
        .skill-categories {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
        }
        .skill-category h4 {
            font-size: var(--font-size-md);
            color: var(--color-accent-blue);
            margin-bottom: var(--spacing-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        .skill-points-grid {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }
        .skill-point-item {
            background: var(--color-accent-orange-light);
            color: var(--color-white);
            border-radius: var(--border-radius-full);
            padding: var(--spacing-sm) var(--spacing-lg);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            border: none;
            box-shadow: var(--shadow-xs);
            cursor: pointer;
            transition: background var(--transition-fast), color var(--transition-fast), box-shadow var(--transition-fast), transform var(--transition-fast);
        }
        .skill-point-item.current-skill {
            background: var(--color-accent-blue);
            color: var(--color-white);
            box-shadow: var(--shadow-md);
        }
        .skill-point-item:hover {
            background: var(--color-accent-orange);
            transform: scale(1.04);
        }

        .practice-area-container {
            background: var(--color-bg-glass);
            box-shadow: var(--shadow-lg);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl) var(--spacing-lg);
            margin-top: var(--spacing-lg);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }

        .practice-controls-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: var(--spacing-lg);
            background: var(--color-bg-secondary);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-sm);
            padding: var(--spacing-md);
            margin-top: var(--spacing-md);
        }
        .practice-controls-bar .btn {
            border-radius: var(--border-radius-full);
            font-weight: var(--font-weight-medium);
            box-shadow: var(--shadow-xs);
            border: none;
            transition: background var(--transition-fast), transform var(--transition-fast);
        }
        .practice-controls-bar .btn-primary {
            background: var(--color-accent-blue);
            color: var(--color-white);
        }
        .practice-controls-bar .btn-primary:hover {
            background: var(--color-accent-blue-light);
            transform: scale(1.03);
        }
        .practice-controls-bar .btn-outline {
            background: var(--color-bg-primary);
            color: var(--color-accent-blue);
            border: 1px solid var(--color-accent-blue-light);
        }
        .practice-controls-bar .btn-outline:hover {
            background: var(--color-accent-blue-light);
            color: var(--color-white);
        }


        /* Profile & Settings Specific */
        /* .profile-settings-grid 在 dynamic_island_ui.html 中 */
        .profile-card, .settings-card, .gamification-card {
            background: var(--color-bg-glass);
            box-shadow: var(--shadow-lg);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl) var(--spacing-lg);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }
        .profile-card h3, .settings-card h3, .gamification-card h3 { /* 卡片标题 */
            font-size: var(--font-size-xl);
            margin-bottom: var(--spacing-lg);
        }

        .profile-avatar-section {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }
        #user-avatar-img-large {
            width: 100px;
            height: 100px;
            border-radius: var(--border-radius-full);
            object-fit: cover;
            box-shadow: var(--shadow-md);
            background: var(--color-bg-secondary);
        }
        .xp-level-section {
            background: var(--color-bg-secondary);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-xs);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }
        .xp-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: var(--font-size-sm);
            margin-bottom: var(--spacing-xs);
        }
        .xp-info strong { color: var(--color-text-primary); }

        .progress-bar {
            background: var(--color-accent-gray);
            border-radius: var(--border-radius-full);
            height: 10px;
            margin: var(--spacing-sm) 0;
            box-shadow: var(--shadow-xs);
        }
        .progress-bar-fill {
            background: linear-gradient(90deg, var(--color-accent-green), var(--color-accent-blue));
            border-radius: var(--border-radius-full);
            height: 100%;
            transition: width var(--transition-normal);
        }
        .card-profile-progress .progress-bar { /* 仪表盘上的进度条 */
            height: 12px; margin: var(--spacing-sm) 0 var(--spacing-md) 0;
        }

        .achievements-grid {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-lg);
            margin-top: var(--spacing-md);
        }
        .achievement-badge {
            background: var(--color-bg-secondary);
            border-radius: var(--border-radius-full);
            box-shadow: var(--shadow-xs);
            padding: var(--spacing-md);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-xs);
            min-width: 90px;
            min-height: 90px;
            position: relative;
            cursor: pointer;
            transition: background var(--transition-fast), color var(--transition-fast), box-shadow var(--transition-fast), transform var(--transition-fast);
        }
        .achievement-badge.locked {
            opacity: 0.5;
            filter: grayscale(1);
        }
        .achievement-badge:hover {
            box-shadow: var(--shadow-md);
            transform: scale(1.05);
        }
        .badge-tooltip {
            position: absolute;
            bottom: -32px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--color-accent-blue-light);
            color: var(--color-white);
            border-radius: var(--border-radius-md);
            padding: 4px 12px;
            font-size: var(--font-size-xs);
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity var(--transition-fast);
            z-index: 10;
        }
        .achievement-badge:hover .badge-tooltip {
            opacity: 1;
        }
        .btn-group {
            display: flex;
        }
        .btn-group .btn {
            border-radius: 0;
            border-right-width: 0; /* 移除内部按钮右边框 */
        }
        .btn-group .btn:first-child {
            border-top-left-radius: var(--border-radius-sm);
            border-bottom-left-radius: var(--border-radius-sm);
        }
        .btn-group .btn:last-child {
            border-top-right-radius: var(--border-radius-sm);
            border-bottom-right-radius: var(--border-radius-sm);
            border-right-width: 1px; /* 最后一个按钮显示右边框 */
        }
        .btn-group .btn.active { /* 按钮组中的激活状态 */
            background-color: var(--color-accent-blue);
            color: white;
            border-color: var(--color-accent-blue);
            z-index: 1; /* 确保激活按钮的边框在最上层 */
        }
         .btn-group .btn:not(.active):hover {
            z-index: 2; /* 确保悬浮按钮在未激活按钮之上 */
        }


        /* Dashboard Specific Styles */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: var(--spacing-xl);
            margin-top: var(--spacing-lg);
            width: 100%;
            box-sizing: border-box;
        }
        .dashboard-card {
            background: var(--color-bg-glass);
            box-shadow: var(--shadow-lg);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl) var(--spacing-lg);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
            transition: box-shadow var(--transition-normal), transform var(--transition-normal), background var(--transition-fast);
            border: none;
        }
        .dashboard-card:hover {
            box-shadow: var(--shadow-xl);
            transform: translateY(-2px) scale(1.02);
        }

        .card-profile-progress .progress-bar {
            background: var(--color-accent-gray);
            border-radius: var(--border-radius-full);
            height: 10px;
            margin: var(--spacing-sm) 0;
            box-shadow: var(--shadow-xs);
        }
        .card-profile-progress .progress-bar-fill {
            background: linear-gradient(90deg, var(--color-accent-orange), var(--color-accent-blue));
            border-radius: var(--border-radius-full);
            height: 100%;
            transition: width var(--transition-normal);
        }

        .status-badge {
            background: var(--color-accent-orange);
            color: var(--color-white);
            border-radius: var(--border-radius-full);
            font-size: var(--font-size-xs);
            padding: 2px 10px;
            font-weight: var(--font-weight-semibold);
            box-shadow: var(--shadow-xs);
        }

        .achievement-list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md);
        }
        .achievement-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            background: var(--color-bg-secondary);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-sm) var(--spacing-md);
            box-shadow: var(--shadow-xs);
        }
        .achievement-item.locked {
            opacity: 0.5;
            filter: grayscale(1);
        }

        .card-action-link {
            color: var(--color-accent-blue);
            font-size: var(--font-size-sm);
            text-decoration: none;
            font-weight: var(--font-weight-medium);
            transition: color var(--transition-fast);
        }
        .card-action-link:hover {
            color: var(--color-accent-orange);
            text-decoration: underline;
        }

        .card-quick-start .btn-tertiary {
            background: var(--color-accent-blue-light);
            color: var(--color-white);
            border-radius: var(--border-radius-full);
            font-weight: var(--font-weight-medium);
            margin-bottom: var(--spacing-sm);
            box-shadow: var(--shadow-sm);
            border: none;
            transition: background var(--transition-fast), transform var(--transition-fast);
        }
        .card-quick-start .btn-tertiary:hover {
            background: var(--color-accent-blue);
            transform: scale(1.03);
        }

        /* Mock Interview Specific Styles */
        /* .mock-interview-container is within an artboard */
        .interview-main-panel {
            background: var(--color-bg-glass);
            box-shadow: var(--shadow-lg);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl) var(--spacing-lg);
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
            width: 100%;
            margin-bottom: var(--spacing-xl);
        }
        .video-area {
            display: flex;
            gap: var(--spacing-lg);
            background: var(--color-bg-secondary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
            padding: var(--spacing-lg);
        }
        .video-feed {
            background: var(--color-white);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-sm);
            padding: var(--spacing-sm);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: var(--spacing-sm);
        }
        .interview-controls-bar {
            background: var(--color-bg-glass);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-sm);
            padding: var(--spacing-md) var(--spacing-lg);
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: var(--spacing-lg);
            margin-top: var(--spacing-md);
        }
        .control-buttons-group .btn {
            border-radius: var(--border-radius-full);
            font-weight: var(--font-weight-medium);
            box-shadow: var(--shadow-xs);
            background: var(--color-accent-blue-light);
            color: var(--color-white);
            border: none;
            transition: background var(--transition-fast), transform var(--transition-fast);
        }
        .control-buttons-group .btn:hover {
            background: var(--color-accent-blue);
            transform: scale(1.03);
        }
        .interview-sidebar {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
            background: var(--color-bg-glass);
            box-shadow: var(--shadow-lg);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl) var(--spacing-lg);
            min-width: 320px;
        }
        .sidebar-module {
            background: var(--color-bg-secondary);
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-sm);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }
        .sidebar-module h4 { font-size: var(--font-size-md); margin-bottom: var(--spacing-sm); }
        #current-interview-question {
            font-size: var(--font-size-sm);
            line-height: 1.5;
            margin-bottom: var(--spacing-md);
            min-height: 3em; /* 给问题文本留出一些高度 */
        }
        .question-actions {
            display: flex;
            gap: var(--spacing-sm);
        }
        .feedback-list {
            list-style: none;
            padding: 0;
            margin: 0;
            font-size: var(--font-size-sm);
        }
        .feedback-list li {
            margin-bottom: var(--spacing-xs);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        .feedback-tag {
            padding: 2px 6px;
            border-radius: var(--border-radius-sm);
            font-size: 10px; /* 更小的标签 */
            font-weight: var(--font-weight-medium);
            color: var(--color-white);
        }
        .feedback-tag.positive { background-color: var(--color-success); }
        .feedback-tag.negative { background-color: var(--color-error); }
        .feedback-tag.neutral { background-color: var(--color-text-tertiary); }


        /* 移除了原 .smart-top-bar 和 .feature-curtain-overlay 相关样式 */
        /* 这些由 dynamic_island_ui.html 和 style.css 控制 */

        /* 确保SVG图标在<use>时能正确应用颜色 */
        svg symbol polyline, svg symbol path, svg symbol circle, svg symbol rect, svg symbol line {
            fill: currentColor; /* 尝试确保子元素也继承颜色 */
        }
        /* 对于特定用途的SVG，如果颜色固定，则在SVG内部直接定义 */

        /* Media Queries - 保持 part1.html原有的响应式断点 */
        @media (max-width: 1200px) {
            .artboard {
                max-width: 100%;
                padding: var(--spacing-lg) var(--spacing-sm);
            }
            .dashboard-grid, .achievements-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-lg);
            }
            .ai-coach-container, .skill-categories {
                flex-direction: column;
                gap: var(--spacing-lg);
            }
        }

        @media (max-width: 992px) { /* 平板 */
            .two-column-layout {
                flex-direction: column;
            }
            .sidebar-column {
                flex: 0 0 auto; /* 高度自动 */
                max-height: 300px; /* 给一个最大高度，使其可滚动 */
                padding-right: 0;
            }
             .two-column-layout.sidebar-right .sidebar-column {
                padding-left: 0;
            }
            .video-area {
                grid-template-columns: 1fr; /* 视频上下堆叠 */
                min-height: 400px; /* 保证视频区域高度 */
            }
            .dashboard-grid {
                 grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .artboards-container {
                padding: var(--spacing-lg) var(--spacing-xs);
            }
            .artboard {
                padding: var(--spacing-md) var(--spacing-xs);
            }
            .dashboard-card, .profile-card, .settings-card, .gamification-card, .practice-area-container, .ai-coach-chat-area, .ai-coach-sessions-list, .interview-main-panel, .interview-sidebar {
                padding: var(--spacing-md) var(--spacing-xs);
                margin-bottom: var(--spacing-md);
            }
            .form-grid {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }
            .skill-points-grid {
                flex-direction: column;
                gap: var(--spacing-sm);
            }
            .practice-controls-bar {
                flex-direction: column;
                gap: var(--spacing-sm);
                align-items: stretch;
            }
            .message-list {
                max-height: 200px;
            }
            .artboard-header h2, .module-header h3, .profile-card h3, .settings-card h3, .gamification-card h3 {
                font-size: var(--font-size-lg); /* 缩小标题 */
            }
            .skill-practice-header h2 { font-size: var(--font-size-lg); }
            .message-input-area .form-textarea { max-height: 80px; } /* 减少输入框最大高度 */
            .skill-points-grid {
                grid-template-columns: 1fr; /* 单列显示 */
            }
             .form-grid {
                grid-template-columns: 1fr; /* 单列显示 */
            }
            .dashboard-grid {
                 grid-template-columns: 1fr;
            }
        } 

        /* 动画与过渡优化 */
        .card, .glass-card, .dashboard-card, .sidebar-module, .profile-card, .settings-card, .gamification-card, .practice-area-container, .ai-coach-chat-area, .ai-coach-sessions-list, .interview-main-panel, .interview-sidebar {
            transition: box-shadow var(--transition-normal), transform var(--transition-normal), background var(--transition-fast);
        }
        .btn, .btn-primary, .btn-secondary, .btn-outline, .btn-link, .btn-tertiary, .btn-danger, .btn-danger-outline, .btn-block {
            transition: background var(--transition-fast), color var(--transition-fast), transform var(--transition-fast), box-shadow var(--transition-fast);
        }
        .skill-point-item, .ai-coach-session-item, .achievement-badge {
            transition: background var(--transition-fast), color var(--transition-fast), box-shadow var(--transition-fast), transform var(--transition-fast);
        }
        input, textarea, select {
            transition: background var(--transition-fast), box-shadow var(--transition-fast), outline var(--transition-fast);
        } 

        body.dark-mode {
            --color-bg-primary: #23272F;
            --color-bg-secondary: #2C313A;
            --color-bg-tertiary: #22252B;
            --color-text-primary: #F3F6FA;
            --color-text-secondary: #BFC8D1;
            --color-text-tertiary: #7A869A;
            --color-accent-orange: #FF9A5A;
            --color-accent-orange-light: #FFB07A;
            --color-accent-blue: #5CAEFF;
            --color-accent-blue-light: #7DC3FF;
            --color-accent-gold: #FFD700;
            --color-accent-green: #4CAF50;
            --color-accent-gray: #31343B;
            --color-bg-glass: rgba(44,49,58,0.7);
            --color-bg-glass-dark: rgba(30,33,40,0.8);
            --color-success: #4CAF50;
            --color-warning: #FFB300;
            --color-error: #FF6B6B;
            --color-white: #23272F;
            --shadow-xs: 0 1px 3px rgba(0,0,0,0.18);
            --shadow-sm: 0 2px 8px rgba(0,0,0,0.22);
            --shadow-md: 0 4px 12px rgba(0,0,0,0.28);
            --shadow-lg: 0 8px 24px rgba(0,0,0,0.32);
            --shadow-xl: 0 16px 48px rgba(0,0,0,0.38);
        } 

        /* 波纹动画效果 */
        .ripple-effect {
            position: relative;
            overflow: hidden;
        }
        .ripple-effect .ripple {
            position: absolute;
            border-radius: 50%;
            transform: scale(0);
            animation: ripple-animate 0.6s linear;
            background: rgba(255,255,255,0.4);
            pointer-events: none;
            z-index: 2;
        }
        @keyframes ripple-animate {
            to {
                transform: scale(2.5);
                opacity: 0;
            }
        }

        /* 按钮按压缩放 */
        .btn:active, .btn-primary:active, .btn-secondary:active, .btn-outline:active, .btn-tertiary:active {
            transform: scale(0.96);
            box-shadow: var(--shadow-sm);
        }
        .card:active, .glass-card:active, .dashboard-card:active {
            transform: scale(0.98);
            box-shadow: var(--shadow-md);
        } 

        /* 渐变色块工具类 */
        .gradient-bg-orange {
            background: linear-gradient(90deg, #FF7A59 0%, #FFA07A 100%);
            color: #fff;
        }
        .gradient-bg-blue {
            background: linear-gradient(90deg, #4A90E2 0%, #6DA3E9 100%);
            color: #fff;
        }
        .gradient-bg-green {
            background: linear-gradient(90deg, #4CAF50 0%, #7ED957 100%);
            color: #fff;
        }
        .gradient-bg-multi {
            background: linear-gradient(90deg, #FF7A59 0%, #4A90E2 50%, #FFD700 100%);
            color: #fff;
        } 