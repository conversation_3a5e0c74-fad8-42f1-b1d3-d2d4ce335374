body {
    margin: 0;
    font-family: '<PERSON>', '<PERSON>o', 'Source <PERSON>', sans-serif; /* Updated to use part1_styles font stack */
    background-color: #FAF7F5; /* Updated to match part1_styles --color-bg-primary */
    color: #333;
    min-height: 200vh; /* For scrolling to see fixed header */
    padding-top: 100px; /* Space for the dynamic island */
}

#dynamic-island-container {
    position: fixed;
    top: 15px; /* Slight offset from top */
    left: 50%;
    transform: translateX(-50%); 
    display: flex;
    align-items: center;
    justify-content: center; /* Center islands initially, JS might reposition */
    padding: 5px;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-radius: 30px; /* Pill shape for the container itself */
    z-index: 1000;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.17);
    border: 1px solid rgba(255, 255, 255, 0.18);
    transition: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
    gap: 8px;
}

.island {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    min-width: 40px; /* Minimum touch target */
    height: 40px;
    background-color: rgba(0, 0, 0, 0.2); /* Darker islands */
    border-radius: 20px; /* Rounded individual islands */
    cursor: pointer;
    transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1);
    color: white;
    overflow: hidden; /* Important for expanding search etc. */
}

.island:hover {
    background-color: rgba(0, 0, 0, 0.3);
    transform: scale(1.05);
    box-shadow: 0 0 15px 2px rgba(0, 122, 255, 0.5); /* Blue glow */
}

.island .icon {
    width: 22px;
    height: 22px;
    fill: currentColor;
    transition: transform 0.3s ease;
}

.island:active .icon {
    transform: scale(0.9);
}

/* Logo specific */
.logo-island {
    background-color: rgba(255, 165, 0, 0.3); /* Orange hint for logo */
}
.logo-island:hover {
    box-shadow: 0 0 15px 2px rgba(255, 165, 0, 0.7);
}
.logo-svg {
    animation: breath 3s infinite ease-in-out;
}
@keyframes breath {
    0%, 100% { opacity: 0.7; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
}

/* Search Island */
.search-island {
    padding: 10px;
}

#search-input {
    background: transparent;
    border: none;
    outline: none;
    color: white;
    font-size: 14px;
    width: 0;
    padding: 0;
    margin-left: 0;
    opacity: 0;
    transition: width 0.35s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s 0.1s ease, padding 0.35s cubic-bezier(0.4, 0, 0.2, 1), margin-left 0.35s cubic-bezier(0.4, 0, 0.2, 1) ;
}

.search-trigger-icon {
    transition: opacity 0.3s ease;
}

.search-island.active {
    /* width: auto; /* Allow it to expand based on input */
    min-width: 200px; /* Expanded width */
    justify-content: flex-start;
}

.search-island.active #search-input {
    width: 150px; /* Adjust as needed */
    opacity: 1;
    padding: 0 5px;
    margin-left: 8px;
}

.search-island.active .search-trigger-icon {
    opacity: 0.7; /* Slightly dim when search is active */
}


/* Notification Island */
.notification-dot {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 8px;
    height: 8px;
    background-color: #FFA500; /* Orange dot */
    border-radius: 50%;
    border: 1px solid rgba(0,0,0,0.5);
    display: none; /* Hidden by default */
    animation: pulse 1.5s infinite ease-in-out;
}

.notification-dot.visible {
    display: block;
}

@keyframes pulse {
    0% { transform: scale(0.9); opacity: 0.7; }
    50% { transform: scale(1.1); opacity: 1; }
    100% { transform: scale(0.9); opacity: 0.7; }
}

/* User Island & Menu */
.user-island {
    padding: 5px; /* Smaller padding to fit avatar nicely */
}

.avatar-img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
    display: block;
}

.user-menu {
    position: absolute;
    top: calc(100% + 10px); /* Below the island */
    right: 0;
    background-color: rgba(50, 50, 50, 0.5); /* Darker glass for menu */
    backdrop-filter: blur(15px) saturate(150%);
    -webkit-backdrop-filter: blur(15px) saturate(150%);
    border-radius: 10px;
    padding: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    width: 180px;
    z-index: 1001;
    display: flex;
    flex-direction: column;
    gap: 5px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px) scale(0.95);
    transform-origin: top right;
    transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.user-island.menu-active .user-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.user-menu a {
    text-decoration: none;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.user-menu a:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

/* Tooltips for nav items */
[data-tooltip]::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: calc(100% + 8px); /* Position above the island */
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0,0,0,0.8);
    color: white;
    padding: 6px 10px;
    border-radius: 5px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease, transform 0.2s ease;
    pointer-events: none; /* So it doesn't interfere with hover on island */
}

.nav-island:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px); /* Slight upward move */
}

/* Content area for demo */
.content {
    padding: 20px;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.content h1 {
    color: #2c3e50;
}

.content p, .content li {
    line-height: 1.6;
    color: #555;
    text-align: left;
}

.content ul {
    list-style-type: disc;
    padding-left: 20px;
    margin-bottom: 20px;
}

.content button {
    background-color: #007AFF; /* Blue */
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    margin: 5px;
    transition: background-color 0.2s ease, transform 0.1s ease;
}

.content button:hover {
    background-color: #0056b3;
}
.content button:active {
    transform: scale(0.98);
}


/* Contextual changes */
body.context-interview #dynamic-island-container {
    background-color: rgba(0, 0, 0, 0.2); /* Darker, more focused */
}

/* Reset styles for all interactive islands before applying context-specific ones */
#dynamic-island-container .island:not(#logo-island):not(#user-island):not(#notification-island):not(#search-island) {
    opacity: 1;
    transform: scale(1);
    box-shadow: none; /* Explicitly remove box-shadow before context rules apply */
    transition: opacity 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease; /* Ensure smooth transitions for reset */
}

/* General dimmed state for islands in specific contexts */
body.context-interview .island:not(#logo-island):not(#user-island):not(#notification-island):not(#nav-interview):not(#search-island),
body.context-dashboard .island:not(#logo-island):not(#user-island):not(#notification-island):not(#nav-coach):not(#search-island)
{
    opacity: 0.4;
    transform: scale(0.9);
}

/* Specific highlighted islands */
body.context-interview #nav-interview {
    opacity: 1;
    transform: scale(1.1);
    box-shadow: 0 0 20px 5px rgba(0, 122, 255, 0.7); /* Brighter blue glow */
}

body.context-dashboard #dynamic-island-container {
    /* Optional: different style for dashboard's island container */
}

body.context-dashboard #nav-coach {
    opacity: 1;
    transform: scale(1.1);
    box-shadow: 0 0 20px 5px rgba(80, 200, 120, 0.7); /* Greenish glow for AI Coach */
}

/* Context-specific styles for dynamic island */
/* Example: Highlight active nav island based on body class */

/* General style for non-active islands in task modes */
body.context-interview #dynamic-island-container,
body.context-settings #dynamic-island-container,
body.context-ai-coach #dynamic-island-container,
body.context-skill-practice #dynamic-island-container {
    /* background-color: rgba(230, 230, 230, 0.6); Greyish tone when in a specific context */
}

#dynamic-island-container .island:not(#logo-island):not(#user-island):not(#notification-island):not(#search-island) {
    /* Default appearance for context-switchable islands */
    /* opacity: 0.7; */
    /* filter: grayscale(50%); */
}

/* Dim non-active islands in specific contexts */
body.context-interview .island:not(#logo-island):not(#user-island):not(#notification-island):not(#nav-interview):not(#search-island),
body.context-settings .island:not(#logo-island):not(#user-island):not(#notification-island):not(#nav-settings):not(#search-island),
body.context-ai-coach .island:not(#logo-island):not(#user-island):not(#notification-island):not(#nav-coach):not(#search-island),
body.context-skill-practice .island:not(#logo-island):not(#user-island):not(#notification-island):not(#nav-skills):not(#search-island)
{
    opacity: 0.5;
    transform: scale(0.9);
}

/* Highlight active island based on context */
body.context-interview #nav-interview,
body.context-settings #nav-settings, /* Assuming #nav-settings exists or will be created */
body.context-ai-coach #nav-coach,
body.context-skill-practice #nav-skills,
body.context-dashboard #nav-dashboard {
    opacity: 1;
    transform: scale(1.1);
    box-shadow: 0 0 15px 3px var(--color-accent-blue, rgba(0, 122, 255, 0.7)); /* Use CSS var with fallback */
    background-color: rgba(0, 100, 200, 0.4);
}

/* Styles from original inline style of dynamic_island_ui.html */
.content {
    padding-top: 80px; /* Adjust based on island height */
    padding-left: 20px;
    padding-right: 20px;
    text-align: center;
    /* Note: max-width and margin: auto from previous .content rule might be overridden or conflict.
       This will be resolved if layouts look incorrect. */
}

.context-display {
    margin-top: 20px;
    padding: 10px;
    border: 1px solid #ccc;
    background-color: #f9f9f9;
} 