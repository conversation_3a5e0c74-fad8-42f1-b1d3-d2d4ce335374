<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Island UI</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="part1_styles.css">
</head>
<body class="context-dashboard">
    <div id="dynamic-island-container">
        <div class="island logo-island" id="logo-island">
            <svg class="icon logo-svg" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 7h2v2h-2z"/></svg>
        </div>

        <div class="island nav-island" id="nav-interview" data-tooltip="模拟面试">
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-8 14H7v-2h4v2zm0-3H7v-2h4v2zm0-3H7V9h4v2zm6 6h-4v-2h4v2zm0-3h-4v-2h4v2zm0-3h-4V9h4v2z"/></svg>
        </div>

        <div class="island nav-island" id="nav-coach" data-tooltip="AI教练">
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4zm0 10.99h7c-.53 4.12-3.28 7.79-7 8.94V12H5V6.3l7-3.11v8.8z"/></svg>
        </div>
        
        <!-- 新增：成长导航按钮 -->
        <div class="island nav-island" id="nav-career" data-tooltip="成长导航">
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor"><use xlink:href="#icon-career"></use></svg>
        </div>
        <!-- 新增：面试伙伴社群按钮 -->
        <div class="island nav-island" id="nav-community" data-tooltip="面试伙伴社群">
            <svg class="icon" viewBox="0 0 24 24" fill="currentColor"><use xlink:href="#icon-community"></use></svg>
        </div>

        <div class="island search-island" id="search-island">
            <svg class="icon search-trigger-icon" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/></svg>
            <input type="text" id="search-input" placeholder="全局搜索...">
        </div>

        <div class="island notification-island" id="notification-island" data-tooltip="通知中心">
             <svg class="icon" viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg"><path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.63-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2zm-2 1H8v-6c0-2.48 1.51-4.5 4-4.5s4 2.02 4 4.5v6z"/></svg>
            <div class="notification-dot"></div>
        </div>

        <div class="island user-island" id="user-island">
            <img src="https://via.placeholder.com/30" alt="User Avatar" class="avatar-img">
            <div class="user-menu">
                <a href="#">个人中心</a>
                <a href="#">设置</a>
                <a href="#">切换模式</a>
                <a href="#" id="logout-btn">退出登录</a>
            </div>
        </div>
    </div>

    <svg style="display: none;">
        <defs>
            <symbol id="icon-home" viewBox="0 0 24 24">
                <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
            </symbol>
            <symbol id="icon-dashboard" viewBox="0 0 24 24">
                <path d="M13 3h-2v10h2V3zm4.83 2.17l-1.42 1.42C17.99 7.86 19 9.81 19 12c0 3.87-3.13 7-7 7s-7-3.13-7-7c0-2.19 1.01-4.14 2.58-5.42L6.17 5.17C4.23 6.82 3 9.26 3 12c0 4.97 4.03 9 9 9s9-4.03 9-9c0-2.74-1.23-5.18-3.17-6.83z"/>
            </symbol>
            <symbol id="icon-search" viewBox="0 0 24 24">
                <path d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
            </symbol>
            <symbol id="icon-bell" viewBox="0 0 24 24">
                <path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.63-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2zm-2 1H8v-6c0-2.48 1.51-4.5 4-4.5s4 2.02 4 4.5v6z"/>
            </symbol>
            <symbol id="icon-user-circle" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm0-14c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 10c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
            </symbol>
            <symbol id="icon-settings" viewBox="0 0 24 24">
                <path d="M19.43 12.98c.04-.32.07-.64.07-.98s-.03-.66-.07-.98l2.11-1.65c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.3-.61-.22l-2.49 1c-.52-.4-1.08-.73-1.69-.98l-.38-2.65C14.46 2.18 14.25 2 14 2h-4c-.25 0-.46.18-.49.42l-.38 2.65c-.61.25-1.17.59-1.69.98l-2.49-1c-.23-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64l2.11 1.65c-.04.32-.07.65-.07.98s.03.66.07.98l-2.11 1.65c-.19.15-.24.42.12.64l2 3.46c.12.22.39.3.61.22l2.49-1c.52.4 1.08.73 1.69.98l.38 2.65c.03.24.24.42.49.42h4c.25 0 .46-.18.49-.42l.38-2.65c.61-.25 1.17-.59 1.69-.98l2.49 1c.23.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.65zM12 15.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5z"/>
            </symbol>
            <symbol id="icon-help-circle" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v2h-2V7zm0 4h2v6h-2v-6z"/>
            </symbol>
            <symbol id="icon-briefcase" viewBox="0 0 24 24">
                <path d="M20 6h-4V4c0-1.11-.89-2-2-2h-4c-1.11 0-2 .89-2 2v2H4c-1.11 0-2 .89-2 2v11c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zM10 4h4v2h-4V4zm10 15H4V8h16v11z"/>
            </symbol>
            <symbol id="icon-users" viewBox="0 0 24 24">
                <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V18h14v-1.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V18h6v-1.5c0-2.33-4.67-3.5-7-3.5z"/>
            </symbol>
            <symbol id="icon-message-circle" viewBox="0 0 24 24">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
            </symbol>
            <symbol id="icon-star" viewBox="0 0 24 24">
                <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
            </symbol>
            <symbol id="icon-mic" viewBox="0 0 24 24">
                <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm-1.2-9.1c0-.66.54-1.2 1.2-1.2s1.2.54 1.2 1.2V9c0 .66-.54 1.2-1.2 1.2s-1.2-.54-1.2-1.2V4.9zm6.5 6.1c0 3-2.5 5.5-5.5 5.5S6.3 14 6.3 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.3z"/>
            </symbol>
            <symbol id="icon-video" viewBox="0 0 24 24">
                <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4zM15 16H5V8h10v8z"/>
            </symbol>
            <symbol id="icon-monitor" viewBox="0 0 24 24">
                <path d="M20 3H4c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2h6v2H8v2h8v-2h-2v-2h6c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 13H4V5h16v11z"/>
            </symbol>
            <symbol id="icon-edit-3" viewBox="0 0 24 24">
                <path d="M14.06 9.02l.92.92L5.92 19H5v-.92l9.06-9.06M17.66 3c-.26 0-.51.1-.7.29l-1.83 1.83 3.75 3.75 1.83-1.83c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.2-.2-.45-.29-.71-.29zm-3.6 3.19L3 17.25V21h3.75L17.81 9.94l-3.75-3.75z"/>
            </symbol>
            <symbol id="icon-copy" viewBox="0 0 24 24">
                <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
            </symbol>
            <symbol id="icon-trash-2" viewBox="0 0 24 24">
                <path d="M3 6v18h18V6H3zm16 16H5V8h14v14zM17 4h-4l-1-1h-4L7 4H3v2h18V4z"/>
            </symbol>
            <symbol id="icon-maximize-2" viewBox="0 0 24 24">
                <path d="M15 3h6v6h-2V5h-4V3zm0 18h4v-4h2v6h-6v-2zM3 9V3h6v2H5v4H3zm0 12v-4H1v6h6v-2H3z"/>
            </symbol>
            <symbol id="icon-minimize-2" viewBox="0 0 24 24">
                <path d="M4 14h6v6H8v-4H4v-2zm0-10h4V0H2v6h2V4zm16 6V4h-6v2h4v4h2zm0 10h-4v4h6v-6h-2v4z"/>
            </symbol>
            <symbol id="icon-zoom-in" viewBox="0 0 24 24">
                <path d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM10 9.5H7v1h3v3h1v-3h3v-1h-3v-3h-1v3z"/>
            </symbol>
            <symbol id="icon-zoom-out" viewBox="0 0 24 24">
                <path d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0 0 16 9.5 6.5 6.5 0 1 0 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14zM7 9h6v1H7z"/>
            </symbol>
            <symbol id="icon-refresh-cw" viewBox="0 0 24 24">
                <path d="M21.65 7.35C20.88 6.21 19.57 5.5 18 5.5c-3.04 0-5.5 2.46-5.5 5.5s2.46 5.5 5.5 5.5c1.28 0 2.48-.43 3.42-1.16l-1.42-1.42C19.28 13.65 18.67 14 18 14c-1.93 0-3.5-1.57-3.5-3.5S16.07 7 18 7c.78 0 1.48.29 2.04.75L18.5 9.5h4V5.5l-1.85 1.85zM2.35 16.65C3.12 17.79 4.43 18.5 6 18.5c3.04 0 5.5-2.46 5.5-5.5S9.04 7.5 6 7.5c-1.28 0-2.48.43-3.42 1.16l1.42 1.42C4.72 10.35 5.33 10 6 10c1.93 0 3.5 1.57 3.5 3.5S7.93 17 6 17c-.78 0-1.48-.29-2.04-.75L5.5 14.5h-4v4l1.85-1.85z"/>
            </symbol>
            <symbol id="icon-move" viewBox="0 0 24 24">
                <path d="M12 2l-4 4h3v4H7v3l-4-4 4-4v3h4V7h3l4 4-4 4zM22 12l-4 4h3v4h-4v-3l-4 4 4 4v-3h4v-4h3l-4-4zM2 12l4-4H3V4h4v3l4-4-4-4v3H4v4H1l4 4z"/>
            </symbol>
            <symbol id="icon-chevron-left" viewBox="0 0 24 24">
                <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
            </symbol>
            <symbol id="icon-chevron-right" viewBox="0 0 24 24">
                <path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>
            </symbol>
             <symbol id="icon-chevrons-left" viewBox="0 0 24 24">
                <path d="M11.67 3.87L9.9 2.1 0 12l9.9 9.9 1.77-1.77L3.54 12z"/><path d="M18.67 3.87L16.9 2.1 7 12l9.9 9.9 1.77-1.77L10.54 12z"/>
            </symbol>
            <symbol id="icon-chevrons-right" viewBox="0 0 24 24">
                 <path d="M5.33 2.1L7.1 3.87 13.46 12l-6.36 8.13-1.77 1.77L14.1 12z"/><path d="M12.33 2.1L14.1 3.87 20.46 12l-6.36 8.13-1.77 1.77L21.1 12z"/>
            </symbol>
            <symbol id="icon-play" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
            </symbol>
            <symbol id="icon-pause" viewBox="0 0 24 24">
                <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
            </symbol>
            <symbol id="icon-stop-circle" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-2-4h4V8h-4v8z"/>
            </symbol>
             <symbol id="icon-rotate-cw" viewBox="0 0 24 24">
                <path d="M21.65 7.35C20.88 6.21 19.57 5.5 18 5.5c-3.04 0-5.5 2.46-5.5 5.5s2.46 5.5 5.5 5.5c1.28 0 2.48-.43 3.42-1.16l-1.42-1.42C19.28 13.65 18.67 14 18 14c-1.93 0-3.5-1.57-3.5-3.5S16.07 7 18 7c.78 0 1.48.29 2.04.75L18.5 9.5h4V5.5l-1.85 1.85z"/>
            </symbol>
             <symbol id="icon-arrow-left-circle" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-2-9h4v2H9v3l-4-4 4-4v3z"/>
            </symbol>
            <symbol id="icon-award" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2zm0 3.24L9.7 9.99 4.53 10.7l3.63 3.54-.86 5.01L12 16.97l4.7 2.28-.86-5.01 3.63-3.54-5.17-.71L12 5.24z"/>
            </symbol>
            <symbol id="icon-trending-up" viewBox="0 0 24 24">
                <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
            </symbol>
             <symbol id="icon-bar-chart-2" viewBox="0 0 24 24">
                <path d="M18 20V10m-6 10V4M6 20v-6"/>
            </symbol>
            <symbol id="icon-sliders" viewBox="0 0 24 24">
                <path d="M4 21v-7M4 10V3M12 21v-9M12 8V3M20 21v-5M20 12V3M1 14h6M9 8h6M17 16h6"/>
            </symbol>
            <symbol id="icon-filter" viewBox="0 0 24 24">
                <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z"/>
            </symbol>
            <symbol id="icon-grid" viewBox="0 0 24 24">
                <path d="M3 3h7v7H3zm0 11h7v7H3zm11 0h7v7h-7zm0-11h7v7h-7z"/>
            </symbol>
            <symbol id="icon-layout" viewBox="0 0 24 24">
                <path d="M3 3v18h18V3H3zm16 16H5V5h14v14zM7 7h4v4H7zm0 6h4v4H7zm6-6h4v4h-4zm0 6h4v4h-4z"/>
            </symbol>
            <symbol id="icon-sidebar" viewBox="0 0 24 24">
                <path d="M3 3h18v18H3V3zm16 2H5v14h14V5zM9 7H7v2h2V7zm0 4H7v2h2v-2zm0 4H7v2h2v-2z"/>
            </symbol>
            <symbol id="icon-list" viewBox="0 0 24 24">
                <path d="M8 6h13M8 12h13M8 18h13M3 6h1v1H3zm0 5h1v1H3zm0 5h1v1H3z"/>
            </symbol>
            <symbol id="icon-menu" viewBox="0 0 24 24">
                <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
            </symbol>
            <symbol id="icon-more-horizontal" viewBox="0 0 24 24">
                <circle cx="12" cy="12" r="2"/><circle cx="19" cy="12" r="2"/><circle cx="5" cy="12" r="2"/>
            </symbol>
            <symbol id="icon-more-vertical" viewBox="0 0 24 24">
                 <circle cx="12" cy="12" r="2"/><circle cx="12" cy="5" r="2"/><circle cx="12" cy="19" r="2"/>
            </symbol>
            <symbol id="icon-plus-circle" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v4h4v2h-4v4H9v-4H5v-2h4V7z"/>
            </symbol>
            <symbol id="icon-minus-circle" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-4-9h8v2H8v-2z"/>
            </symbol>
            <symbol id="icon-x-circle" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm3.59-13L12 10.59 8.41 7 7 8.41 10.59 12 7 15.59 8.41 17 12 13.41 15.59 17 17 15.59 13.41 12 17 8.41z"/>
            </symbol>
             <symbol id="icon-check-circle" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-2.07-7.21l-3.71-3.71-1.42 1.42L8.93 14l5.66-5.66-1.41-1.41-4.24 4.24z"/>
            </symbol>
            <symbol id="icon-alert-triangle" viewBox="0 0 24 24">
                <path d="M12 2L.5 21h23L12 2zm0 4.76L18.06 19H5.94L12 6.76zM11 10v4h2v-4h-2zm0 6v2h2v-2h-2z"/>
            </symbol>
             <symbol id="icon-info" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v2h-2V7zm0 4h2v6h-2v-6z"/>
            </symbol>
            <symbol id="icon-question-circle" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14a3.5 3.5 0 0 0-3.5 3.5H10c0-.83.67-1.5 1.5-1.5S13 7.67 13 8.5c0 .53-.27.97-.68 1.25L11.5 11h1v1h-2.5c0-.5.25-1 .75-1.25l.8-.48C12.45 9.85 12 9.24 12 8.5c0-1.1-.9-2-2-2s-2 .9-2 2H6.5C6.5 7.14 8.64 5 11.5 5S15 6.14 15 8.5c0 .83-.34 1.57-.92 2.12L12.5 12v1h3v-1.5c0-1.1.9-2 2-2h.5c1.1 0 2 .9 2 2s-.9 2-2 2H17v1.5c0 1.38-1.12 2.5-2.5 2.5S12 17.88 12 16.5H10c0 .83.67 1.5 1.5 1.5s1.5-.67 1.5-1.5c0-.53-.27-.97-.68-1.25L11.5 14h-1v-1h2.5c0 .5-.25 1-.75 1.25l-.8.48c-.9.42-1.45 1.32-1.45 2.27 0 1.1.9 2 2 2s2-.9 2-2h1.5c0 1.93-1.57 3.5-3.5 3.5S8 17.93 8 16c0-1.07.53-2.02 1.36-2.64L10.5 12.5V11h-3v1.5c0 1.1-.9 2-2 2h-.5c-1.1 0-2-.9-2-2s.9-2 2-2H7V9.5C7 8.12 8.12 7 9.5 7S12 5.62 12 4.5 10.88 2 9.5 2 7 3.12 7 4.5H5c0-1.93 1.57-3.5 3.5-3.5S12 2.57 12 4.5zm-1 11.5h2v2h-2v-2z"/>
            </symbol>
            <symbol id="icon-log-out" viewBox="0 0 24 24">
                <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
            </symbol>
            <symbol id="icon-upload-cloud" viewBox="0 0 24 24">
                <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5 5 5h-3z"/>
            </symbol>
            <symbol id="icon-download-cloud" viewBox="0 0 24 24">
                <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM12 17l-4-4h2.5V9h3v4H16l-4 4z"/>
            </symbol>
             <symbol id="icon-folder" viewBox="0 0 24 24">
                <path d="M10 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z"/>
            </symbol>
            <symbol id="icon-file" viewBox="0 0 24 24">
                <path d="M13 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V9l-7-7zM18 20H6V4h6v5h5v11z"/>
            </symbol>
            <symbol id="icon-file-text" viewBox="0 0 24 24">
                <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zM16 18H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V4l5 5h-5z"/>
            </symbol>
            <symbol id="icon-image" viewBox="0 0 24 24">
                <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
            </symbol>
             <symbol id="icon-link" viewBox="0 0 24 24">
                <path d="M17 7h-4v2h4c1.65 0 3 1.35 3 3s-1.35 3-3 3h-4v2h4c2.76 0 5-2.24 5-5s-2.24-5-5-5zm-6 8H7c-1.65 0-3-1.35-3-3s1.35-3 3-3h4V7H7c-2.76 0-5 2.24-5 5s2.24 5 5 5h4v-2z"/>
            </symbol>
            <symbol id="icon-external-link" viewBox="0 0 24 24">
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6v2H5v11h11v-6h2zM15 3h6v6h-2V5h-4V3z"/>
            </symbol>
             <symbol id="icon-share-2" viewBox="0 0 24 24">
                <circle cx="18" cy="5" r="3"/><circle cx="6" cy="12" r="3"/><circle cx="18" cy="19" r="3"/>
                <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/><line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/>
            </symbol>
             <symbol id="icon-download" viewBox="0 0 24 24">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4H1v4a4 4 0 0 0 4 4h14a4 4 0 0 0 4-4v-4h-2zm-1-4l-1.41-1.41L13 12.17V4h-2v8.17L8.41 9.59 7 11l5 5 5-5z"/>
            </symbol>
            <symbol id="icon-upload" viewBox="0 0 24 24">
                <path d="M3 19v-4H1v4a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-4h-2v4H3zm4-14L12 0l5 5h-3v9H8V5H3z"/>
            </symbol>
            <symbol id="icon-zap" viewBox="0 0 24 24">
                <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"/>
            </symbol>
            <symbol id="icon-code" viewBox="0 0 24 24">
                <polyline points="16 18 22 12 16 6"/><polyline points="8 6 2 12 8 18"/>
            </symbol>
            <symbol id="icon-gift" viewBox="0 0 24 24">
                 <polyline points="20 12 20 22 4 22 4 12"/><rect x="2" y="7" width="20" height="5"/><line x1="12" y1="22" x2="12" y2="7"/><path d="M12 7H7.5a2.5 2.5 0 0 1 0-5C9.81 2 12 4.19 12 7zm0 0h4.5a2.5 2.5 0 0 0 0-5C14.19 2 12 4.19 12 7z"/>
            </symbol>
            <symbol id="icon-award-solid" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </symbol>
            <symbol id="icon-trophy" viewBox="0 0 24 24">
                <path d="M20.25 3h-2.5C17.03 3 16.5 3.53 16.5 4.25v.5c0 .72.53 1.25 1.25 1.25h2.5c.72 0 1.25-.53 1.25-1.25v-.5C21.5 3.53 20.97 3 20.25 3zM12 11c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-9C8.5 2 5.28 3.89 4.1 6.95c-.24.61.19 1.28.85 1.35.08.01.16.01.24.01.55 0 1.04-.38 1.19-.91.85-2.19 2.89-3.65 5.37-3.65s4.52 1.46 5.37 3.65c.15.53.64.91 1.19.91.08 0 .16 0 .24-.01.66-.07 1.09-.74.85-1.35C18.72 3.89 15.5 2 12 2zm-1 16H6.78c-.65 0-1.23-.44-1.46-1.05L4.09 13.5c-.2-.52.02-1.1.53-1.3L7.5 11.02c.3-.12.63-.08.9.11l1.74 1.24c.24.17.57.17.81 0l1.74-1.24c.27-.19.6-.23.9-.11l2.88 1.18c.51.2.73.78.53 1.3l-1.23 3.45c-.23.61-.81 1.05-1.46 1.05H13v2.25c0 .41-.34.75-.75.75h-.5c-.41 0-.75-.34-.75-.75V18zm7.25-9.75c-.72 0-1.25.53-1.25 1.25v.5c0 .72.53 1.25 1.25 1.25h2.5c.72 0 1.25-.53 1.25-1.25v-.5c0-.72-.53-1.25-1.25-1.25h-2.5z"/>
            </symbol>
            <symbol id="icon-chat-alt" viewBox="0 0 24 24">
                <path d="M21 11.5a8.5 8.5 0 0 1-17 0 8.5 8.5 0 0 1 17 0zm-2 0a6.5 6.5 0 1 0-13 0 6.5 6.5 0 0 0 13 0z"/><path d="M12 16a1 1 0 0 1-.71-.29l-3-3a1 1 0 0 1 0-1.42l3-3a1 1 0 1 1 1.42 1.42L10.41 11H14a1 1 0 0 1 0 2h-3.59l2.29 2.29A1 1 0 0 1 12 16z"/><path d="M4.22 19.78a.75.75 0 0 1-.53-1.28l2.5-2.5a.75.75 0 1 1 1.06 1.06l-2.5 2.5a.75.75 0 0 1-.53.22z"/>
            </symbol>
             <symbol id="icon-user" viewBox="0 0 24 24">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/>
            </symbol>
            <symbol id="icon-bot" viewBox="0 0 24 24"> <!-- Basic Bot Icon -->
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 15c-1.86 0-3.43-1.26-3.87-2.99h7.74c-.44 1.73-2.01 2.99-3.87 2.99zM12 6c-2.21 0-4 1.79-4 4h8c0-2.21-1.79-4-4-4zm-5.5 5c-.83 0-1.5.67-1.5 1.5S5.67 14 6.5 14s1.5-.67 1.5-1.5S7.33 11 6.5 11zm11 0c-.83 0-1.5.67-1.5 1.5s.67 1.5 1.5 1.5 1.5-.67 1.5-1.5-.67-1.5-1.5-1.5z"/>
            </symbol>
            <!-- 新增：成长导航icon -->
            <symbol id="icon-career" viewBox="0 0 24 24">
                <path d="M12 2a7 7 0 0 1 7 7c0 3.87-3.13 7-7 7s-7-3.13-7-7a7 7 0 0 1 7-7zm0 2C8.13 4 5 7.13 5 11c0 2.76 2.24 5 5 5s5-2.24 5-5c0-3.87-3.13-7-7-7zm0 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 2c-2.67 0-8 1.34-8 4v3h16v-3c0-2.66-5.33-4-8-4z"/>
            </symbol>
            <!-- 新增：社群icon -->
            <symbol id="icon-community" viewBox="0 0 24 24">
                <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V20h14v-3.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V20h6v-3.5c0-2.33-4.67-3.5-7-3.5z"/>
            </symbol>

        </defs>
    </svg>

    <div class="page-container">
        <main class="main-content">
            <!-- Artboards Container: This will hold multiple "pages" or "views" -->
            <div class="artboards-container" id="artboards-container">

                <!-- Dashboard Artboard (Default Visible) -->
                <div class="artboard active-artboard" id="dashboard-artboard">
                    <header class="artboard-header">
                        <h2>我的仪表盘</h2>
                        <div class="header-actions">
                            <button class="btn btn-secondary btn-sm">
                                <svg width="16" height="16"><use xlink:href="#icon-sliders"></use></svg>
                                自定义视图
                            </button>
                            <button class="btn btn-primary btn-sm">
                                <svg width="16" height="16"><use xlink:href="#icon-plus-circle"></use></svg>
                                添加小组件
                            </button>
                        </div>
                    </header>
                    <div class="dashboard-grid">
                        <div class="dashboard-card card-profile-progress glass-card">
                            <div class="card-header">
                                <h3>个人资料完成度</h3>
                                <span class="status-badge status-warning">75%</span>
                            </div>
                            <p>完善您的个人资料，以便AI教练更好地为您量身定制辅导计划。</p>
                            <div class="progress-bar">
                                <div class="progress-bar-fill" style="width: 75%;"></div>
                            </div>
                            <a href="#profile-container" class="card-action-link goto-profile">继续完善 &rarr;</a>
                        </div>

                        <div class="dashboard-card card-upcoming-session glass-card">
                            <div class="card-header">
                                <h3>近期模拟面试</h3>
                                <svg width="20" height="20"><use xlink:href="#icon-video"></use></svg>
                            </div>
                            <p><strong>产品经理 | 字节跳动</strong></p>
                            <p class="text-muted">时间：2024年07月28日 14:30</p>
                            <div class="card-actions">
                                <button class="btn btn-primary btn-sm">进入面试间</button>
                                <button class="btn btn-outline btn-sm">修改时间</button>
                            </div>
                        </div>

                        <div class="dashboard-card card-quick-start glass-card">
                             <h3>快速开始</h3>
                             <button class="btn btn-tertiary btn-block">
                                <svg width="18" height="18"><use xlink:href="#icon-mic"></use></svg>
                                进行一次快速发音练习
                             </button>
                             <button class="btn btn-tertiary btn-block">
                                <svg width="18" height="18"><use xlink:href="#icon-message-circle"></use></svg>
                                与AI教练讨论STAR案例
                             </button>
                             <button class="btn btn-tertiary btn-block goto-mock-interview">
                                <svg width="18" height="18"><use xlink:href="#icon-users"></use></svg>
                                查看已安排的模拟面试
                             </button>
                        </div>
                        
                        <div class="dashboard-card card-achievements glass-card">
                            <div class="card-header">
                                <h3>我的成就</h3>
                                <svg width="20" height="20" style="color:var(--color-accent-gold);"><use xlink:href="#icon-award-solid"></use></svg>
                            </div>
                            <div class="achievement-list">
                                <div class="achievement-item">
                                    <svg width="28" height="28"><use xlink:href="#icon-award"></use></svg>
                                    <div>
                                        <p><strong>面试初体验</strong></p>
                                        <p class="text-muted-sm">完成首次模拟面试</p>
                                    </div>
                                </div>
                                <div class="achievement-item locked">
                                    <svg width="28" height="28"><use xlink:href="#icon-trending-up"></use></svg>
                                     <div>
                                        <p><strong>持续进步奖</strong></p>
                                        <p class="text-muted-sm">连续7天完成练习</p>
                                    </div>
                                </div>
                            </div>
                             <a href="#gamification-module" class="card-action-link goto-achievements">查看所有成就 &rarr;</a>
                        </div>
                    </div>
                </div>

                <!-- Mock Interview Artboard (Initially Hidden) -->
                <div class="artboard" id="mock-interview-artboard">
                    <div class="mock-interview-container">
                        <div class="interview-main-panel">
                            <div class="video-area glass-card">
                                <div class="video-feed local-video">
                                    <img src="https://via.placeholder.com/400x300/cccccc/808080?text=我的影像" alt="My Video Feed">
                                    <div class="video-controls">
                                        <button class="btn-icon" title="麦克风 开/关"><svg width="20" height="20"><use xlink:href="#icon-mic"></use></svg></button>
                                        <button class="btn-icon" title="摄像头 开/关"><svg width="20" height="20"><use xlink:href="#icon-video"></use></svg></button>
                                        <button class="btn-icon" title="屏幕共享"><svg width="20" height="20"><use xlink:href="#icon-monitor"></use></svg></button>
                                    </div>
                                    <span class="participant-name">我 (求职者)</span>
                                </div>
                                <div class="video-feed remote-video">
                                    <img src="https://via.placeholder.com/400x300/A0A0A0/E0E0E0?text=AI面试官" alt="AI Interviewer Video Feed">
                                     <div class="ai-status">
                                        <span class="typing-indicator">AI面试官正在思考...</span>
                                    </div>
                                    <span class="participant-name">AI面试官</span>
                                </div>
                            </div>

                            <div class="interview-controls-bar glass-card">
                                <div class="interview-timer">
                                    <svg width="20" height="20"><use xlink:href="#icon-stop-circle" style="color: var(--color-danger);"></use></svg>
                                    <span>面试用时: <strong>12:34</strong></span>
                                </div>
                                <div class="control-buttons-group">
                                    <button class="btn btn-secondary">
                                        <svg width="18" height="18"><use xlink:href="#icon-pause"></use></svg>
                                        暂停面试
                                    </button>
                                    <button class="btn btn-danger">
                                        <svg width="18" height="18"><use xlink:href="#icon-stop-circle"></use></svg>
                                        结束面试
                                    </button>
                                </div>
                                <button class="btn btn-icon" title="面试设置">
                                    <svg width="20" height="20"><use xlink:href="#icon-settings"></use></svg>
                                </button>
                            </div>
                        </div>

                        <div class="interview-sidebar">
                            <div class="sidebar-module question-module glass-card">
                                <h4>当前问题</h4>
                                <p id="current-interview-question">请介绍一个你认为最能体现你解决问题能力的项目。</p>
                                <div class="question-actions">
                                    <button class="btn btn-outline btn-sm">
                                        <svg width="14" height="14"><use xlink:href="#icon-rotate-cw"></use></svg>
                                        重复问题
                                    </button>
                                    <button class="btn btn-outline btn-sm">
                                         <svg width="14" height="14"><use xlink:href="#icon-help-circle"></use></svg>
                                        提示
                                    </button>
                                </div>
                            </div>

                            <div class="sidebar-module notes-module glass-card">
                                <h4>个人笔记 (仅自己可见)</h4>
                                <textarea class="form-textarea" rows="5" placeholder="记录要点、STAR要素..."></textarea>
                                <button class="btn btn-secondary btn-sm btn-block">保存笔记</button>
                            </div>
                            
                            <div class="sidebar-module feedback-preview-module glass-card">
                                <h4>实时反馈摘要</h4>
                                <ul class="feedback-list">
                                    <li><span class="feedback-tag positive">发音清晰</span> 注意语速</li>
                                    <li><span class="feedback-tag neutral">眼神交流</span> 稍有不足</li>
                                    <li><span class="feedback-tag positive">STAR原则</span> 运用得当</li>
                                </ul>
                                <a href="#" class="card-action-link">查看完整报告 (面试后) &rarr;</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI Coach Artboard (Initially Hidden) -->
                <div class="artboard" id="ai-coach-artboard">
                    <div class="ai-coach-container">
                        <aside class="ai-coach-sessions-list glass-card">
                            <div class="sessions-header">
                                <h3>AI辅导会话</h3>
                                <button class="btn btn-icon" title="新建会话">
                                    <svg width="20" height="20"><use xlink:href="#icon-plus-circle"></use></svg>
                                </button>
                            </div>
                            <input type="text" class="form-input" placeholder="搜索会话..." style="margin-bottom: var(--spacing-md);">
                            
                            <div class="ai-coach-session-item active">
                                <h5>STAR案例打磨 - 电商项目</h5>
                                <p>上次沟通：昨天 15:30</p>
                            </div>
                            <div class="ai-coach-session-item">
                                <h5>模拟面试复盘 - 技术岗</h5>
                                <p>上次沟通：3天前</p>
                            </div>
                            <div class="ai-coach-session-item">
                                <h5>沟通技巧提升</h5>
                                <p>上次沟通：上周</p>
                            </div>
                            <div style="margin-top:auto; padding-top:var(--spacing-md);">
                                <button class="btn btn-outline btn-small" style="width:100%;">加载更多会话</button>
                            </div>
                        </aside>

                        <main class="ai-coach-chat-area">
                            <header class="chat-header glass-card">
                                <div>
                                    <h3>STAR案例打磨 - 电商项目</h3>
                                    <p><span class="status-dot online"></span>AI教练在线</p>
                                </div>
                                <button class="btn btn-icon" title="更多操作">
                                    <svg width="20" height="20"><use xlink:href="#icon-more-horizontal"></use></svg>
                                </button>
                            </header>
                            <div class="message-list-container">
                                <div class="message-list" id="message-list-coach">
                                    <div class="message ai-message">
                                        <svg width="32" height="32" class="avatar-icon"><use xlink:href="#icon-bot"></use></svg>
                                        <div class="message-content">
                                            <div class="message-bubble glass-card">
                                                <p>你好！我们继续上次关于电商项目的讨论。你提到在Action（行动）部分可以更具体一些，准备好了吗？</p>
                                                <div class="quick-replies">
                                                    <button class="btn btn-outline btn-small quick-reply-btn">准备好了，开始吧！</button>
                                                    <button class="btn btn-outline btn-small quick-reply-btn">让我想一想...</button>
                                                </div>
                                            </div>
                                            <span class="message-timestamp">10:32 AM</span>
                                        </div>
                                    </div>
                                    <div class="message user-message">
                                        <div class="message-content">
                                            <div class="message-bubble">
                                                <p>准备好了，我重新梳理了一下我负责的促销活动策划和执行部分。</p>
                                            </div>
                                            <span class="message-timestamp">10:33 AM</span>
                                        </div>
                                        <svg width="32" height="32" class="avatar-icon"><use xlink:href="#icon-user"></use></svg>
                                    </div>
                                     <div class="message ai-message">
                                        <svg width="32" height="32" class="avatar-icon"><use xlink:href="#icon-bot"></use></svg>
                                        <div class="message-content">
                                            <div class="message-bubble glass-card">
                                                <p>太棒了！请详细说明一下你的行动（Action）。比如，你具体做了哪些事情来策划和执行促销活动？可以从目标设定、方案设计、跨部门协作、推广渠道、数据监控等方面来展开。</p>
                                            </div>
                                            <span class="message-timestamp">10:34 AM</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="message-input-area glass-card">
                                <textarea class="form-textarea" rows="1" placeholder="输入你的消息..."></textarea>
                                <div class="message-input-actions">
                                    <button class="btn btn-icon" title="上传附件">
                                        <svg width="20" height="20"><use xlink:href="#icon-upload-cloud"></use></svg>
                                    </button>
                                    <button class="btn btn-icon" title="语音输入">
                                        <svg width="20" height="20"><use xlink:href="#icon-mic"></use></svg>
                                    </button>
                                    <button class="btn btn-primary btn-sm">
                                        发送
                                        <svg width="16" height="16" style="margin-left: var(--spacing-xs);"><use xlink:href="#icon-zap"></use></svg>
                                    </button>
                                </div>
                            </div>
                        </main>
                    </div>
                </div>

                <!-- Skill Practice Artboard (Initially Hidden) -->
                <div class="artboard" id="skill-practice-artboard">
                    <div class="skill-practice-container" id="skill-practice-module">
                        <header class="skill-practice-header">
                            <h2>专项技能提升</h2>
                            <div>
                                <button class="btn btn-outline btn-sm">
                                    <svg width="16" height="16"><use xlink:href="#icon-filter"></use></svg>
                                    筛选
                                </button>
                                <button class="btn btn-primary btn-sm">
                                    <svg width="16" height="16"><use xlink:href="#icon-zap"></use></svg>
                                    每日一练
                                </button>
                            </div>
                        </header>
                
                        <div class="skill-categories">
                            <div class="skill-category">
                                <h4><svg width="18" height="18" style="margin-right:var(--spacing-xs); vertical-align:bottom;"><use xlink:href="#icon-star"></use></svg>行为面试技巧 (STAR)</h4>
                                <div class="skill-points-grid">
                                    <button class="skill-point-item current-skill" data-skill-type="star">项目经历阐述</button>
                                    <button class="skill-point-item" data-skill-type="star">团队协作案例</button>
                                    <button class="skill-point-item" data-skill-type="star">解决冲突能力</button>
                                    <button class="skill-point-item" data-skill-type="star">抗压能力证明</button>
                                    <button class="skill-point-item" data-skill-type="star">领导力展现</button>
                                </div>
                            </div>
                            <div class="skill-category">
                                <h4><svg width="18" height="18" style="margin-right:var(--spacing-xs); vertical-align:bottom;"><use xlink:href="#icon-mic"></use></svg>口语表达与沟通</h4>
                                <div class="skill-points-grid">
                                    <button class="skill-point-item" data-skill-type="speech">即兴演讲</button>
                                    <button class="skill-point-item" data-skill-type="speaking">清晰发音练习</button>
                                    <button class="skill-point-item" data-skill-type="speech">逻辑表达</button>
                                    <button class="skill-point-item" data-skill-type="speaking">英文面试对话</button>
                                </div>
                            </div>
                             <div class="skill-category">
                                <h4><svg width="18" height="18" style="margin-right:var(--spacing-xs); vertical-align:bottom;"><use xlink:href="#icon-briefcase"></use></svg>特定岗位技能</h4>
                                <div class="skill-points-grid">
                                    <button class="skill-point-item" data-skill-type="custom">产品Sense问答</button>
                                    <button class="skill-point-item" data-skill-type="custom">技术方案阐述</button>
                                    <button class="skill-point-item" data-skill-type="custom">设计作品集展示</button>
                                </div>
                            </div>
                        </div>
                
                        <div class="practice-area-container glass-card">
                            <div class="practice-info-bar">
                                <h3>练习: 项目经历阐述</h3>
                                <p>难度: <span style="color:var(--color-warning); font-weight:500;">中级</span> | 预计用时: 10分钟</p>
                            </div>
                            <div class="practice-question-area">
                                <!-- Content will be loaded by JS based on selected skill -->
                                <p class="placeholder-content">请从左侧选择一个技能点开始练习。</p>
                            </div>
                             <div class="practice-controls-bar">
                                <button class="btn btn-outline">
                                    <svg width="16" height="16"><use xlink:href="#icon-help-circle"></use></svg>
                                    查看提示/范例
                                </button>
                                <div>
                                    <button class="btn btn-secondary">
                                        <svg width="16" height="16"><use xlink:href="#icon-upload-cloud"></use></svg>
                                        提交获取反馈
                                    </button>
                                    <button class="btn btn-primary">
                                        <svg width="16" height="16"><use xlink:href="#icon-chevrons-right"></use></svg>
                                        下一个练习
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile & Settings Artboard (Initially Hidden) -->
                <div class="artboard" id="profile-settings-artboard">
                    <div class="profile-settings-grid">
                        <div class="profile-card glass-card" id="profile-container">
                            <h3>个人中心</h3>
                            <div class="profile-avatar-section">
                                <img src="https://via.placeholder.com/100/A0A0A0/E0E0E0?text=User" alt="User Avatar" id="user-avatar-img-large">
                                <button class="btn btn-secondary btn-sm">
                                    <svg width="16" height="16"><use xlink:href="#icon-edit-3"></use></svg>
                                    更换头像
                                </button>
                            </div>
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="profile-name" class="form-label">昵称</label>
                                    <input type="text" id="profile-name" class="form-input" value="AI挑战者">
                                </div>
                                <div class="form-group">
                                    <label for="profile-email" class="form-label">邮箱 (不可更改)</label>
                                    <input type="email" id="profile-email" class="form-input" value="<EMAIL>" disabled>
                                </div>
                                <div class="form-group form-group-full">
                                    <label for="profile-bio" class="form-label">个人简介</label>
                                    <textarea id="profile-bio" class="form-textarea" rows="3" placeholder="介绍一下自己，你的目标岗位，期望的提升方向等。">积极寻求产品经理岗位机会，希望通过模拟面试提升面试表现，尤其是案例分析和压力面试环节。</textarea>
                                </div>
                                 <div class="form-group">
                                    <label for="profile-target-role" class="form-label">目标岗位</label>
                                    <input type="text" id="profile-target-role" class="form-input" value="产品经理">
                                </div>
                                <div class="form-group">
                                    <label for="profile-target-industry" class="form-label">目标行业</label>
                                    <input type="text" id="profile-target-industry" class="form-input" value="互联网, SaaS">
                                </div>
                            </div>
                            <button class="btn btn-primary" style="margin-top:var(--spacing-lg);">保存更改</button>
                        </div>

                        <div class="settings-card glass-card" id="settings-admin-container">
                            <h3>偏好设置</h3>
                            <div class="form-group">
                                <label class="form-label">AI教练模式</label>
                                <div class="btn-group">
                                    <button class="btn btn-secondary active">循序渐进</button>
                                    <button class="btn btn-secondary">严格挑战</button>
                                </div>
                            </div>
                             <div class="form-group">
                                <label for="setting-language" class="form-label">界面语言</label>
                                <select id="setting-language" class="form-select">
                                    <option value="zh-CN" selected>简体中文</option>
                                    <option value="en-US">English</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-switch">
                                    <input type="checkbox" checked>
                                    <span>开启邮件通知 (面试提醒、报告生成)</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="form-switch">
                                    <input type="checkbox">
                                    <span>允许AI收集匿名数据以改进服务</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="form-label">数据管理</label>
                                <button class="btn btn-outline btn-sm">导出我的数据</button>
                                <button class="btn btn-danger-outline btn-sm" style="margin-left: var(--spacing-sm);">删除我的账户</button>
                            </div>
                             <button class="btn btn-primary" style="margin-top:var(--spacing-lg);">保存设置</button>
                        </div>

                        <div class="gamification-card glass-card" id="gamification-module">
                            <h3>成就与激励</h3>
                            <div class="xp-level-section">
                                <div class="xp-info">
                                    <span>等级: <strong>进阶能手 LV.3</strong></span>
                                    <span>XP: <strong>1250 / 2000</strong></span>
                                </div>
                                <div class="progress-bar">
                                    <div class="progress-bar-fill" style="width: 62.5%; background-color: var(--color-accent-green);"></div>
                                </div>
                            </div>
                            <h4>已解锁成就</h4>
                            <div class="achievements-grid">
                                <div class="achievement-badge">
                                    <svg width="48" height="48"><use xlink:href="#icon-award-solid"></use></svg>
                                    <p>面试首秀</p>
                                    <span class="badge-tooltip">完成第一次模拟面试</span>
                                </div>
                                <div class="achievement-badge">
                                     <svg width="48" height="48" style="color:var(--color-accent-blue);"><use xlink:href="#icon-star"></use></svg>
                                    <p>辅导常客</p>
                                     <span class="badge-tooltip">完成5次AI教练辅导</span>
                                </div>
                                <div class="achievement-badge">
                                     <svg width="48" height="48" style="color:var(--color-accent-orange);"><use xlink:href="#icon-zap"></use></svg>
                                    <p>练习达人</p>
                                    <span class="badge-tooltip">完成10个技能练习点</span>
                                </div>
                                <div class="achievement-badge locked">
                                     <svg width="48" height="48"><use xlink:href="#icon-trending-up"></use></svg>
                                    <p>进步之星</p>
                                    <span class="badge-tooltip">连续7天使用平台</span>
                                </div>
                                 <div class="achievement-badge locked">
                                     <svg width="48" height="48"><use xlink:href="#icon-trophy"></use></svg>
                                    <p>面试大师</p>
                                     <span class="badge-tooltip">在模拟面试中获得S级评价</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Career Development Artboard (from part1.html) -->
                <div class="artboard" id="career-dev-artboard">
                    <div class="career-dev-container">
                        <header class="career-dev-header glass" style="display: flex; align-items: center; justify-content: space-between;">
                            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                <svg width="32" height="32" style="color: var(--color-accent-orange);"><use xlink:href="#icon-career"></use></svg>
                                <h3 style="margin:0; font-size: var(--font-size-xl);">我的成长导航</h3>
                            </div>
                            <div>
                                <button class="btn btn-outline btn-small">
                                    <svg width="16" height="16"><use xlink:href="#icon-settings"></use></svg>
                                    编辑我的目标
                                </button>
                                 <button class="btn btn-primary btn-small" style="margin-left: var(--spacing-sm);">
                                    <svg width="16" height="16"><use xlink:href="#icon-zap"></use></svg>
                                    AI生成新发展建议
                                </button>
                            </div>
                        </header>
                        <main class="career-dev-main-content" style="display: grid; grid-template-columns: 3fr 1fr; gap: var(--spacing-lg);">
                            <div class="career-column-main" style="display: flex; flex-direction: column; gap: var(--spacing-lg);">
                                <div class="content-card glass-card" id="career-goal-setting">
                                    <div style="display:flex; justify-content:space-between; align-items:center; margin-bottom:var(--spacing-sm);">
                                        <h4 style="font-size:var(--font-size-lg);">我的职业目标</h4>
                                        <a href="#" class="btn-link btn-small"><svg width="14" height="14"><use xlink:href="#icon-edit-pencil"></use></svg> 修改</a>
                                    </div>
                                    <p style="margin-bottom:var(--spacing-xs);"><strong>短期目标:</strong> Java后端工程师 (深圳) <span class="status-badge info" style="margin-left:var(--spacing-sm); font-size:var(--font-size-xs); padding: 2px 6px; background-color: var(--color-accent-blue-light); color:white; border-radius:var(--border-radius-sm);">进行中</span></p>
                                    <p><strong>长期发展:</strong> 成为一名技术架构师，专注于高并发系统设计。</p>
                                </div>
                                <div class="content-card glass-card" id="learning-path-planning">
                                     <h4 style="font-size:var(--font-size-lg); margin-bottom:var(--spacing-md);">AI推荐学习计划</h4>
                                     <div class="resource-card" style="padding:var(--spacing-md); border: 1px solid var(--color-bg-tertiary); border-radius:var(--border-radius-md); margin-bottom: var(--spacing-md); background-color: var(--color-white); box-shadow: var(--shadow-sm);">
                                        <div style="display:flex; justify-content:space-between; align-items:flex-start;">
                                            <div>
                                                <h5 style="font-size:var(--font-size-md); margin-bottom:var(--spacing-xs);">Java并发编程深度解析</h5>
                                                <p style="font-size:var(--font-size-xs); color:var(--color-text-secondary); margin-bottom:var(--spacing-sm);">包含5个专项练习，2篇核心文章导读，预计3天完成。</p>
                                            </div>
                                            <span style="font-size:var(--font-size-xs); background-color: var(--color-accent-orange); color:white; padding: 2px 8px; border-radius:var(--border-radius-full); font-weight:500;">核心</span>
                                        </div>
                                        <div style="display:flex; justify-content:space-between; align-items:center; margin-top:var(--spacing-sm);">
                                            <div class="progress-bar" style="width:70%; height:10px; background-color:var(--color-bg-secondary); border-radius:var(--border-radius-full);"><div style="width:40%; height:100%; background-color:var(--color-accent-orange); border-radius:var(--border-radius-full);"></div></div>
                                            <a href="#" class="btn btn-primary btn-small">继续学习</a>
                                        </div>
                                     </div>
                                      <div class="resource-card" style="padding:var(--spacing-md); border: 1px solid var(--color-bg-tertiary); border-radius:var(--border-radius-md); margin-bottom: var(--spacing-md); background-color: var(--color-white); box-shadow: var(--shadow-sm); opacity:0.8;">
                                        <h5 style="font-size:var(--font-size-md);  margin-bottom:var(--spacing-xs);">分布式系统设计入门</h5>
                                        <p style="font-size:var(--font-size-xs); color:var(--color-text-secondary); margin-bottom:var(--spacing-sm);">推荐课程《分布式系统原理与实践》，预计1周完成。</p>
                                        <div style="display:flex; justify-content:space-between; align-items:center; margin-top:var(--spacing-sm);">
                                            <div class="progress-bar" style="width:70%; height:10px; background-color:var(--color-bg-secondary); border-radius:var(--border-radius-full);"><div style="width:10%; height:100%; background-color:var(--color-accent-orange); border-radius:var(--border-radius-full);"></div></div>
                                            <a href="#" class="btn btn-outline btn-small">开始学习</a>
                                        </div>
                                     </div>
                                     <a href="#" class="btn-link" style="font-size:var(--font-size-sm); display:inline-block; margin-top:var(--spacing-xs);">查看所有学习计划 &rarr;</a>
                                </div>
                            </div>
                            <div class="career-column-sidebar" style="display: flex; flex-direction: column; gap: var(--spacing-lg);">
                                <div class="content-card glass-card" id="skills-assessment">
                                    <h4 style="font-size:var(--font-size-lg); margin-bottom:var(--spacing-sm);">综合能力概览</h4>
                                    <p style="font-size:var(--font-size-sm); color:var(--color-text-secondary); margin-bottom:var(--spacing-xs);">待提升关键技能 TOP3 (与目标岗位对比):</p>
                                    <ul style="list-style:none; padding:0; margin:0; font-size:var(--font-size-sm);">
                                        <li style="margin-bottom:var(--spacing-xxs);">1. JVM性能调优 (<span style="color:var(--color-error);">差距: 40%</span>)</li>
                                        <li style="margin-bottom:var(--spacing-xxs);">2. 分布式系统设计 (<span style="color:var(--color-error);">差距: 30%</span>)</li>
                                        <li style="margin-bottom:var(--spacing-xxs);">3. 高并发处理 (<span style="color:var(--color-warning);">差距: 25%</span>)</li>
                                    </ul>
                                    <button class="btn btn-secondary btn-small" style="width:100%; margin-top:var(--spacing-md);">查看完整技能画像</button>
                                </div>
                                 <div class="content-card glass-card">
                                    <h4 style="font-size:var(--font-size-lg); margin-bottom:var(--spacing-sm);">快捷入口</h4>
                                    <div style="display:grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-sm);">
                                        <button class="btn btn-outline btn-small" style="width:100%;">行业洞察</button>
                                        <button class="btn btn-primary btn-small" style="width:100%;">简历优化</button>
                                        <button class="btn btn-outline btn-small" style="width:100%;">求职日历</button>
                                        <button class="btn btn-outline btn-small" style="width:100%;">模拟Offer</button>
                                        <button class="btn btn-outline btn-small" style="width:100%;">校友网络</button>
                                        <button class="btn btn-outline btn-small" style="width:100%;">校园招聘</button>
                                    </div>
                                </div>
                            </div>
                        </main>
                    </div>
                </div>

                <!-- Community Artboard (from part1.html) -->
                <div class="artboard" id="community-artboard">
                    <div class="community-container">
                        <header class="community-header glass" style="display: flex; align-items: center; justify-content: space-between;">
                            <div style="display: flex; align-items: center; gap: var(--spacing-sm);">
                                <svg width="32" height="32" style="color: var(--color-accent-blue);"><use xlink:href="#icon-community"></use></svg>
                                <h3 style="margin:0; font-size: var(--font-size-xl);">面试伙伴社群</h3>
                            </div>
                             <div style="position: relative; display: flex; align-items: center;">
                                <svg width="16" height="16" style="position: absolute; left: 12px; color: var(--color-text-tertiary);"><use xlink:href="#icon-search"></use></svg>
                                <input type="text" placeholder="搜索帖子、话题、用户..." class="form-input" style="width: 300px; padding-left: 36px;">
                            </div>
                            <div>
                                <button class="btn btn-primary btn-small">
                                    <svg width="16" height="16"><use xlink:href="#icon-edit-pencil"></use></svg> 发布新帖
                                </button>
                                 <img src="https://source.unsplash.com/random/32x32?abstract,avatar" alt="User Avatar" style="width: 32px; height: 32px; border-radius: 50%; cursor: pointer; margin-left: var(--spacing-md); filter: grayscale(80%) blur(1px); opacity: 0.7;">
                            </div>
                        </header>
                        <main class="community-main-content">
                            <aside class="community-sidebar-left glass-card" style="min-width: 220px; flex-shrink:0; padding:var(--spacing-md);">
                                <h4 style="font-size:var(--font-size-md); margin-bottom:var(--spacing-sm);">板块导航</h4>
                                <ul style="list-style:none; padding:0; margin:0;">
                                    <li style="margin-bottom:var(--spacing-xs);"><a href="#" class="profile-nav-item active" style="padding:var(--spacing-xs) var(--spacing-sm);">经验分享</a></li>
                                    <li style="margin-bottom:var(--spacing-xs);"><a href="#" class="profile-nav-item" style="padding:var(--spacing-xs) var(--spacing-sm);">行业交流</a></li>
                                    <li style="margin-bottom:var(--spacing-xs);"><a href="#" class="profile-nav-item" style="padding:var(--spacing-xs) var(--spacing-sm);">简历互助</a></li>
                                    <li style="margin-bottom:var(--spacing-xs);"><a href="#" class="profile-nav-item" style="padding:var(--spacing-xs) var(--spacing-sm);">每日打卡</a></li>
                                    <li style="margin-bottom:var(--spacing-xs);"><a href="#" class="profile-nav-item" style="padding:var(--spacing-xs) var(--spacing-sm);">小组招募</a></li>
                                </ul>
                                 <h4 style="font-size:var(--font-size-md); margin-top:var(--spacing-lg); margin-bottom:var(--spacing-sm);">我的动态</h4>
                                 <ul style="list-style:none; padding:0; margin:0;">
                                    <li style="margin-bottom:var(--spacing-xs);"><a href="#" class="profile-nav-item" style="padding:var(--spacing-xs) var(--spacing-sm);"><svg width="16" height="16"><use xlink:href="#icon-article"></use></svg>我的帖子</a></li>
                                    <li style="margin-bottom:var(--spacing-xs);"><a href="#" class="profile-nav-item" style="padding:var(--spacing-xs) var(--spacing-sm);"><svg width="16" height="16"><use xlink:href="#icon-star"></use></svg>我的收藏</a></li>
                                 </ul>
                            </aside>
                            <section class="community-feed-area" style="flex-grow: 1;">
                                <div class="post-card glass-card" style="margin-bottom: var(--spacing-md); padding:var(--spacing-md);">
                                    <div style="display:flex; align-items:center; margin-bottom:var(--spacing-sm);">
                                        <img src="https://source.unsplash.com/random/32x32?panda" alt="Anon Avatar" style="width:32px; height:32px; border-radius:50%; margin-right:var(--spacing-sm);">
                                        <div>
                                            <span style="font-weight:var(--font-weight-medium); font-size:var(--font-size-sm);">匿名小熊猫</span> <span style="font-size: var(--font-size-xs); background-color: var(--color-accent-blue); color:white; padding: 1px 5px; border-radius:var(--border-radius-full);">Lv.3</span>
                                            <span style="font-size:var(--font-size-xs); color:var(--color-text-tertiary); margin-left:var(--spacing-xs);">发布于 2小时前 - <span style="color:var(--color-accent-orange);">经验分享</span></span>
                                        </div>
                                    </div>
                                    <h4 style="font-size:var(--font-size-lg); margin-bottom:var(--spacing-xs);">求助！明天面字节后端，有点紧张怎么办？</h4>
                                    <p style="font-size:var(--font-size-sm); color:var(--color-text-secondary); margin-bottom:var(--spacing-sm); max-height: 60px; overflow:hidden; text-overflow:ellipsis;">已经准备了很久了，但是临近面试还是非常焦虑，有没有大佬给点建议，或者分享一下面经？感激不尽！听说一二面主要问八股和项目...</p>
                                    <div style="display:flex; gap:var(--spacing-md); align-items:center; font-size:var(--font-size-sm); color:var(--color-text-secondary);">
                                        <button class="btn-icon small"><svg width="16" height="16"><use xlink:href="#icon-thumbs-up"></use></svg> 12</button>
                                        <button class="btn-icon small"><svg width="16" height="16"><use xlink:href="#icon-chat-alt"></use></svg> 5</button>
                                        <button class="btn-icon small"><svg width="16" height="16"><use xlink:href="#icon-star"></use></svg> 收藏</button>
                                        <span style="margin-left:auto; font-size:var(--font-size-xs); background-color:var(--color-warning); color:white; padding:2px 6px; border-radius:var(--border-radius-sm);">悬赏 50币</span>
                                    </div>
                                </div>
                                 <div class="post-card glass-card" style="margin-bottom: var(--spacing-md); padding:var(--spacing-md); opacity:0.7;">
                                    <p style="text-align:center; color: var(--color-text-tertiary);">更多帖子加载中...</p>
                                </div>
                            </section>
                            <aside class="community-sidebar-right glass-card" style="min-width: 240px; flex-shrink:0; padding:var(--spacing-md);">
                                <h4 style="font-size:var(--font-size-md); margin-bottom:var(--spacing-sm);">热门话题</h4>
                                <div style="display:flex; flex-wrap:wrap; gap:var(--spacing-xs); margin-bottom:var(--spacing-lg);">
                                    <span style="font-size:var(--font-size-xs); background-color:var(--color-bg-secondary); padding:3px 8px; border-radius:var(--border-radius-full); cursor:pointer;">#Java后端#</span>
                                    <span style="font-size:var(--font-size-xs); background-color:var(--color-bg-secondary); padding:3px 8px; border-radius:var(--border-radius-full); cursor:pointer;">#大厂面经#</span>
                                </div>
                                 <h4 style="font-size:var(--font-size-md); margin-bottom:var(--spacing-sm);">模拟面试小组招募</h4>
                                <div style="font-size:var(--font-size-sm); color: var(--color-text-secondary);">暂无活跃招募... <a href="#" class="btn-link btn-small">发起一个?</a></div>
                            </aside>
                        </main>
                    </div>
                </div>

            </div> <!-- End of artboards-container -->
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html> 