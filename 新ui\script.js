/**
 * AI面试助手 - 动态岛导航系统
 * 优化版本 - 提升性能、错误处理和代码质量
 * @version 2.0
 * <AUTHOR>
 */

// 全局配置
const CONFIG = {
    ANIMATION_DURATION: 300,
    DEBOUNCE_DELAY: 150,
    STAGGER_DELAY: 100,
    PERFORMANCE_MODE: window.matchMedia('(prefers-reduced-motion: reduce)').matches
};

// 工具函数
const Utils = {
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 安全的元素选择器
    safeQuerySelector(selector, context = document) {
        try {
            return context.querySelector(selector);
        } catch (error) {
            console.warn(`Invalid selector: ${selector}`, error);
            return null;
        }
    },

    // 安全的元素选择器（多个）
    safeQuerySelectorAll(selector, context = document) {
        try {
            return context.querySelectorAll(selector);
        } catch (error) {
            console.warn(`Invalid selector: ${selector}`, error);
            return [];
        }
    }
};

// DOM元素缓存和错误处理
const DOMElements = {
    init() {
        this.islandContainer = Utils.safeQuerySelector('#dynamic-island-container');
        this.islands = Utils.safeQuerySelectorAll('.island');
        this.searchIsland = Utils.safeQuerySelector('#search-island');
        this.searchInput = Utils.safeQuerySelector('#search-input');
        this.searchTriggerIcon = this.searchIsland?.querySelector('.search-trigger-icon');
        this.userIsland = Utils.safeQuerySelector('#user-island');
        this.notificationIsland = Utils.safeQuerySelector('#notification-island');
        this.notificationDot = this.notificationIsland?.querySelector('.notification-dot');

        // 导航岛元素
        this.logoIsland = Utils.safeQuerySelector('#logo-island');
        this.navInterview = Utils.safeQuerySelector('#nav-interview');
        this.navCoach = Utils.safeQuerySelector('#nav-coach');
        this.navCareer = Utils.safeQuerySelector('#nav-career');
        this.navCommunity = Utils.safeQuerySelector('#nav-community');
        this.navPractice = Utils.safeQuerySelector('#nav-practice');

        // 验证关键元素
        this.validateElements();
    },

    validateElements() {
        const requiredElements = ['islandContainer', 'searchIsland', 'userIsland'];
        const missingElements = requiredElements.filter(key => !this[key]);

        if (missingElements.length > 0) {
            console.error('Missing required DOM elements:', missingElements);
        }
    }
};

document.addEventListener('DOMContentLoaded', () => {
    // 初始化DOM元素
    DOMElements.init();


    // 画板管理系统
    const ArtboardManager = {
        artboards: {
            dashboard: Utils.safeQuerySelector('#dashboard-artboard'),
            interview: Utils.safeQuerySelector('#mock-interview-artboard'),
            coach: Utils.safeQuerySelector('#ai-coach-artboard'),
            skills: Utils.safeQuerySelector('#skill-practice-artboard'),
            profile: Utils.safeQuerySelector('#profile-settings-artboard'),
            career: Utils.safeQuerySelector('#career-dev-artboard'),
            community: Utils.safeQuerySelector('#community-artboard')
        },

        currentContext: 'dashboard',

        // 切换画板和上下文
        switchContextAndArtboard(newContext) {
            if (!newContext || newContext === this.currentContext) return;

            try {
                // 更新body类名
                document.body.className = document.body.className
                    .replace(/context-\w+/g, '')
                    .trim();
                document.body.classList.add(`context-${newContext}`);

                // 隐藏所有画板
                this.hideAllArtboards();

                // 显示目标画板
                setTimeout(() => {
                    this.showArtboard(newContext);
                    this.currentContext = newContext;
                }, CONFIG.ANIMATION_DURATION / 2);

                // 关闭活跃的UI元素
                this.closeActiveElements();

                // 触发自定义事件
                this.dispatchContextChangeEvent(newContext);

            } catch (error) {
                console.error('Error switching artboard:', error);
                this.fallbackToDefault();
            }
        },

        hideAllArtboards() {
            Object.values(this.artboards).forEach(artboard => {
                if (artboard) {
                    artboard.classList.remove('active-artboard');
                    artboard.style.opacity = '0';
                    artboard.style.transition = `opacity ${CONFIG.ANIMATION_DURATION}ms ease-out`;
                }
            });
        },

        showArtboard(context) {
            const artboard = this.artboards[context];
            if (artboard) {
                artboard.classList.add('active-artboard');
                artboard.style.opacity = '1';
                artboard.style.transition = `opacity ${CONFIG.ANIMATION_DURATION * 1.5}ms ease-in`;

                // 初始化画板特定功能
                this.initArtboardFeatures(context);
            } else {
                console.warn(`Artboard not found for context: ${context}`);
                this.fallbackToDefault();
            }
        },

        initArtboardFeatures(context) {
            // 使用requestAnimationFrame确保DOM更新完成
            requestAnimationFrame(() => {
                switch (context) {
                    case 'coach':
                        initAICoachChatArea();
                        break;
                    case 'skills':
                        initSkillPracticeArea();
                        break;
                    default:
                        break;
                }
                initArtboardAccordions();
                initArtboardTabs();
            });
        },

        closeActiveElements() {
            SearchManager.close();
            UserMenuManager.close();
        },

        fallbackToDefault() {
            if (this.artboards.dashboard) {
                this.switchContextAndArtboard('dashboard');
            }
        },

        dispatchContextChangeEvent(newContext) {
            const event = new CustomEvent('contextChange', {
                detail: {
                    newContext,
                    previousContext: this.currentContext
                }
            });
            document.dispatchEvent(event);
        }
    };

    // 搜索管理器
    const SearchManager = {
        isActive: false,

        init() {
            if (!DOMElements.searchIsland || !DOMElements.searchInput) return;

            this.bindEvents();
        },

        bindEvents() {
            // 搜索触发器点击事件
            DOMElements.searchTriggerIcon?.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggle();
            });

            // 搜索输入框事件
            DOMElements.searchInput?.addEventListener('click', (e) => {
                e.stopPropagation();
            });

            // 键盘事件
            DOMElements.searchInput?.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    this.close();
                } else if (e.key === 'Enter') {
                    this.handleSearch();
                }
            });

            // 输入事件（防抖处理）
            DOMElements.searchInput?.addEventListener('input',
                Utils.debounce(this.handleInput.bind(this), CONFIG.DEBOUNCE_DELAY)
            );
        },

        toggle() {
            this.isActive ? this.close() : this.open();
        },

        open() {
            if (!DOMElements.searchIsland || this.isActive) return;

            DOMElements.searchIsland.classList.add('active');
            DOMElements.searchInput?.focus();
            this.isActive = true;

            // 关闭其他活跃元素
            UserMenuManager.close();
        },

        close() {
            if (!DOMElements.searchIsland || !this.isActive) return;

            DOMElements.searchIsland.classList.remove('active');
            DOMElements.searchInput?.blur();
            this.isActive = false;
        },

        handleSearch() {
            const query = DOMElements.searchInput?.value.trim();
            if (query) {
                console.log('Searching for:', query);
                // 实现搜索逻辑
                this.performSearch(query);
            }
            this.close();
        },

        handleInput(e) {
            const query = e.target.value.trim();
            if (query.length > 2) {
                // 实现实时搜索建议
                this.showSuggestions(query);
            } else {
                this.hideSuggestions();
            }
        },

        performSearch(query) {
            // TODO: 实现实际搜索功能
            console.log('Performing search for:', query);
        },

        showSuggestions(query) {
            // TODO: 显示搜索建议
            console.log('Showing suggestions for:', query);
        },

        hideSuggestions() {
            // TODO: 隐藏搜索建议
        }
    };

    // 用户菜单管理器
    const UserMenuManager = {
        isActive: false,

        init() {
            if (!DOMElements.userIsland) return;

            this.bindEvents();
        },

        bindEvents() {
            DOMElements.userIsland?.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggle();
            });

            // 菜单项点击事件
            const userMenuLinks = DOMElements.userIsland?.querySelectorAll('.user-menu a');
            userMenuLinks?.forEach(link => {
                link.addEventListener('click', (e) => {
                    this.handleMenuItemClick(e, link);
                });
            });
        },

        toggle() {
            this.isActive ? this.close() : this.open();
        },

        open() {
            if (!DOMElements.userIsland || this.isActive) return;

            DOMElements.userIsland.classList.add('menu-active');
            this.isActive = true;

            // 关闭其他活跃元素
            SearchManager.close();
        },

        close() {
            if (!DOMElements.userIsland || !this.isActive) return;

            DOMElements.userIsland.classList.remove('menu-active');
            this.isActive = false;
        },

        handleMenuItemClick(e, link) {
            const text = link.textContent.trim();

            if (text.includes('个人中心')) {
                e.preventDefault();
                ArtboardManager.switchContextAndArtboard('profile');
                this.close();
            } else if (text.includes('主题切换')) {
                e.preventDefault();
                ThemeManager.toggle();
            } else if (text.includes('退出登录')) {
                e.preventDefault();
                this.handleLogout();
            }
        },

        handleLogout() {
            if (confirm('确定要退出登录吗？')) {
                // TODO: 实现登出逻辑
                console.log('User logged out');
            }
        }
    };

    // 主题管理器
    const ThemeManager = {
        currentTheme: localStorage.getItem('theme') || 'light',

        init() {
            this.applyTheme(this.currentTheme);
            this.bindEvents();
        },

        bindEvents() {
            // 监听系统主题变化
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (this.currentTheme === 'auto') {
                    this.applyTheme('auto');
                }
            });
        },

        toggle() {
            const themes = ['light', 'dark', 'auto'];
            const currentIndex = themes.indexOf(this.currentTheme);
            const nextTheme = themes[(currentIndex + 1) % themes.length];
            this.setTheme(nextTheme);
        },

        setTheme(theme) {
            this.currentTheme = theme;
            this.applyTheme(theme);
            localStorage.setItem('theme', theme);

            // 触发主题变化事件
            document.dispatchEvent(new CustomEvent('themeChange', {
                detail: { theme }
            }));
        },

        applyTheme(theme) {
            document.documentElement.removeAttribute('data-theme');

            if (theme === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
            } else if (theme === 'auto') {
                const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                if (prefersDark) {
                    document.documentElement.setAttribute('data-theme', 'dark');
                }
            }
        }
    };

    // 导航管理器
    const NavigationManager = {
        init() {
            this.bindNavigationEvents();
            this.initializeContext();
        },

        bindNavigationEvents() {
            const navigationMap = {
                logoIsland: 'dashboard',
                navInterview: 'interview',
                navCoach: 'coach',
                navCareer: 'career',
                navCommunity: 'community',
                navPractice: 'skills'
            };

            Object.entries(navigationMap).forEach(([elementKey, context]) => {
                const element = DOMElements[elementKey];
                if (element) {
                    element.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.navigateTo(context);
                    });
                }
            });
        },

        navigateTo(context) {
            ArtboardManager.switchContextAndArtboard(context);
            this.updateActiveNavigation(context);
        },

        updateActiveNavigation(context) {
            // 移除所有导航项的活跃状态
            DOMElements.islands.forEach(island => {
                island.classList.remove('active');
            });

            // 添加当前导航项的活跃状态
            const activeElement = this.getNavigationElement(context);
            if (activeElement) {
                activeElement.classList.add('active');
            }
        },

        getNavigationElement(context) {
            const contextMap = {
                dashboard: DOMElements.logoIsland,
                interview: DOMElements.navInterview,
                coach: DOMElements.navCoach,
                career: DOMElements.navCareer,
                community: DOMElements.navCommunity,
                skills: DOMElements.navPractice
            };
            return contextMap[context];
        },

        initializeContext() {
            // 从URL hash或默认值确定初始上下文
            const hash = window.location.hash.slice(1);
            const initialContext = hash && ArtboardManager.artboards[hash] ? hash : 'dashboard';
            this.navigateTo(initialContext);
        }
    };

    // 全局事件管理器
    const EventManager = {
        init() {
            this.bindGlobalEvents();
        },

        bindGlobalEvents() {
            // 点击外部关闭活跃元素
            document.addEventListener('click', (e) => {
                if (!this.isClickInsideElement(e, DOMElements.searchIsland)) {
                    SearchManager.close();
                }
                if (!this.isClickInsideElement(e, DOMElements.userIsland)) {
                    UserMenuManager.close();
                }
            });

            // 键盘快捷键
            document.addEventListener('keydown', (e) => {
                this.handleGlobalKeydown(e);
            });

            // 窗口大小变化
            window.addEventListener('resize', Utils.throttle(() => {
                this.handleResize();
            }, 250));

            // 页面可见性变化
            document.addEventListener('visibilitychange', () => {
                this.handleVisibilityChange();
            });
        },

        isClickInsideElement(event, element) {
            return element && element.contains(event.target);
        },

        handleGlobalKeydown(e) {
            // Ctrl/Cmd + K 打开搜索
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                SearchManager.open();
            }

            // Escape 关闭所有活跃元素
            if (e.key === 'Escape') {
                SearchManager.close();
                UserMenuManager.close();
            }
        },

        handleResize() {
            // 响应式处理
            const isMobile = window.innerWidth < 768;
            document.body.classList.toggle('mobile', isMobile);
        },

        handleVisibilityChange() {
            if (document.hidden) {
                // 页面隐藏时的处理
                SearchManager.close();
                UserMenuManager.close();
            }
        }
    };

    // 通知管理器
    const NotificationManager = {
        init() {
            this.bindEvents();
        },

        bindEvents() {
            DOMElements.notificationIsland?.addEventListener('click', () => {
                this.handleNotificationClick();
            });
        },

        handleNotificationClick() {
            // 移除通知点
            DOMElements.notificationDot?.classList.remove('visible');

            // TODO: 显示通知面板
            console.log('Notification center opened');
        },

        showNotification(message, type = 'info') {
            // 显示通知点
            DOMElements.notificationDot?.classList.add('visible');

            // TODO: 实现Toast通知
            console.log(`Notification: ${message} (${type})`);
        }
    };

    // 性能监控器
    const PerformanceMonitor = {
        init() {
            if (CONFIG.PERFORMANCE_MODE) {
                this.enablePerformanceMode();
            }
            this.monitorPerformance();
        },

        enablePerformanceMode() {
            document.body.classList.add('performance-mode');
            console.log('Performance mode enabled');
        },

        monitorPerformance() {
            // 监控页面性能
            if ('performance' in window) {
                window.addEventListener('load', () => {
                    setTimeout(() => {
                        const perfData = performance.getEntriesByType('navigation')[0];
                        console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
                    }, 0);
                });
            }
        }
    };

    // 主应用初始化
    const App = {
        init() {
            try {
                // 初始化各个管理器
                ThemeManager.init();
                SearchManager.init();
                UserMenuManager.init();
                NavigationManager.init();
                NotificationManager.init();
                EventManager.init();
                PerformanceMonitor.init();

                // 初始化动画增强
                this.initAnimationEnhancements();

                console.log('✅ AI面试助手应用初始化完成');
            } catch (error) {
                console.error('❌ 应用初始化失败:', error);
            }
        },

        initAnimationEnhancements() {
            // 动态岛悬停效果增强
            DOMElements.islands.forEach(island => {
                island.addEventListener('mouseenter', () => {
                    if (!CONFIG.PERFORMANCE_MODE) {
                        island.style.transform = 'translateY(-2px) scale(1.05)';
                        island.style.boxShadow = '0 8px 25px rgba(99, 102, 241, 0.15)';
                    }
                });

                island.addEventListener('mouseleave', () => {
                    island.style.transform = '';
                    island.style.boxShadow = '';
                });
            });
        }
    };

    // 启动应用
    App.init();

    // ===== 画板特定功能模块 =====

    // 手风琴组件管理器
    const AccordionManager = {
        setupAccordion(containerSelector, headerSelector, contentSelector, openClass, iconSelector, iconOpenHref, iconClosedHref) {
            const accordions = Utils.safeQuerySelectorAll(containerSelector);

            accordions.forEach(accordion => {
                const header = accordion.querySelector(headerSelector);
                const content = accordion.querySelector(contentSelector);
                const iconUse = iconSelector && header ? header.querySelector(iconSelector + ' use') : null;

                // 防止重复绑定
                if (header && !header.dataset.accordionBound) {
                    header.addEventListener('click', () => {
                        this.toggleAccordion(accordion, content, openClass, iconUse, iconOpenHref, iconClosedHref);
                    });
                    header.dataset.accordionBound = 'true';
                }
            });
        },

        toggleAccordion(accordion, content, openClass, iconUse, iconOpenHref, iconClosedHref) {
            const isOpen = content.style.display === 'block';

            // 切换显示状态
            content.style.display = isOpen ? 'none' : 'block';

            // 切换类名
            accordion.classList.toggle(openClass, !isOpen);

            // 切换图标
            if (iconUse && iconOpenHref && iconClosedHref) {
                iconUse.setAttribute('xlink:href', isOpen ? iconClosedHref : iconOpenHref);
            }

            // 添加动画效果
            if (!CONFIG.PERFORMANCE_MODE) {
                content.style.transition = 'all 0.3s ease';
            }
        }
    };

    // 初始化画板手风琴
    function initArtboardAccordions() {
        try {
            // 技能分类手风琴
            AccordionManager.setupAccordion(
                '.skill-category-item',
                '.category-header',
                '.skill-point-list',
                'active',
                '.chevron-icon',
                '#icon-chevron-down',
                '#icon-chevron-right'
            );

            // 默认展开第一个技能分类的子列表
            const firstActiveSkillCategory = Utils.safeQuerySelector('.skill-category-item.active .skill-point-list');
            if (firstActiveSkillCategory) {
                firstActiveSkillCategory.style.display = 'block';
                const firstActiveChevron = Utils.safeQuerySelector('.skill-category-item.active .category-header .chevron-icon use');
                if (firstActiveChevron) {
                    firstActiveChevron.setAttribute('xlink:href', '#icon-chevron-down');
                }
            }

            // FAQ手风琴
            AccordionManager.setupAccordion(
                '.faq-item',
                'h4',
                'p',
                'open',
                '.chevron-icon',
                '#icon-chevron-down',
                '#icon-chevron-right'
            );
        } catch (error) {
            console.warn('Error initializing accordions:', error);
        }
    }

    // Tab组件管理器
    const TabManager = {
        setupTabs(navItemSelector, contentSectionSelector, activeClass) {
            const navItems = Utils.safeQuerySelectorAll(navItemSelector);
            const contentSections = Utils.safeQuerySelectorAll(contentSectionSelector);

            navItems.forEach(item => {
                // 防止重复绑定
                if (!item.dataset.tabBound) {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.switchTab(item, navItems, contentSections, activeClass);
                    });
                    item.dataset.tabBound = 'true';
                }
            });

            // 设置默认活跃标签
            this.setDefaultActiveTab(navItemSelector, navItems, contentSections, activeClass);
        },

        switchTab(clickedItem, navItems, contentSections, activeClass) {
            // 取消激活所有导航项和内容区域
            navItems.forEach(i => i.classList.remove(activeClass));
            contentSections.forEach(s => {
                s.style.display = 'none';
                s.classList.remove(activeClass);
            });

            // 激活点击的导航项
            clickedItem.classList.add(activeClass);

            // 激活目标内容区域
            const targetId = clickedItem.getAttribute('href')?.substring(1);
            if (targetId) {
                const targetContent = Utils.safeQuerySelector(`#${targetId}`);
                if (targetContent) {
                    targetContent.style.display = 'block';
                    targetContent.classList.add(activeClass);

                    // 添加淡入动画
                    if (!CONFIG.PERFORMANCE_MODE) {
                        targetContent.style.opacity = '0';
                        targetContent.style.transition = 'opacity 0.3s ease';
                        requestAnimationFrame(() => {
                            targetContent.style.opacity = '1';
                        });
                    }
                }
            }
        },

        setDefaultActiveTab(navItemSelector, navItems, contentSections, activeClass) {
            let defaultActiveItem = Utils.safeQuerySelector(`${navItemSelector}.${activeClass}`);
            if (!defaultActiveItem && navItems.length > 0) {
                defaultActiveItem = navItems[0];
                defaultActiveItem.classList.add(activeClass);
            }

            if (defaultActiveItem) {
                contentSections.forEach(s => s.style.display = 'none');
                const defaultTargetId = defaultActiveItem.getAttribute('href')?.substring(1);
                if (defaultTargetId) {
                    const defaultContent = Utils.safeQuerySelector(`#${defaultTargetId}`);
                    if (defaultContent) {
                        defaultContent.style.display = 'block';
                        defaultContent.classList.add(activeClass);
                    }
                }
            }
        }
    };

    // 初始化画板标签页
    function initArtboardTabs() {
        try {
            // 个人中心tab
            TabManager.setupTabs('.profile-tab-nav .tab-nav-item', '.profile-tab-content', 'active');
            // 成就/等级tab
            TabManager.setupTabs('.achievement-tab-nav .tab-nav-item', '.achievement-tab-content', 'active');
            // 帮助中心tab
            TabManager.setupTabs('.help-nav-item', '.help-content-area section', 'active');
        } catch (error) {
            console.warn('Error initializing tabs:', error);
        }
    }

    // AI教练聊天区管理器
    const ChatManager = {
        initAICoachChatArea() {
            try {
                this.setupMessageList();
                this.setupMessageInput();
            } catch (error) {
                console.warn('Error initializing AI coach chat area:', error);
            }
        },

        setupMessageList() {
            const messageListCoach = Utils.safeQuerySelector('#message-list-coach');
            if (messageListCoach) {
                // 自动滚动到底部
                messageListCoach.scrollTop = messageListCoach.scrollHeight;

                // 添加平滑滚动
                messageListCoach.style.scrollBehavior = 'smooth';
            }
        },

        setupMessageInput() {
            const coachMessageInput = Utils.safeQuerySelector('#coach-message-input');
            const coachSendButton = Utils.safeQuerySelector('#coach-send-btn');

            if (!coachMessageInput) return;

            // 自适应高度
            if (!coachMessageInput.dataset.resizeBound) {
                coachMessageInput.addEventListener('input', () => {
                    this.autoResizeTextarea(coachMessageInput);
                });
                coachMessageInput.dataset.resizeBound = 'true';
            }

            // 回车发送消息
            if (!coachMessageInput.dataset.enterBound) {
                coachMessageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage(coachMessageInput, coachSendButton);
                    }
                });
                coachMessageInput.dataset.enterBound = 'true';
            }

            // 初始高度设置
            this.autoResizeTextarea(coachMessageInput);
        },

        autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'; // 最大高度限制
        },

        sendMessage(input, button) {
            const message = input.value.trim();
            if (message) {
                // TODO: 实现发送消息逻辑
                console.log('Sending message:', message);
                input.value = '';
                this.autoResizeTextarea(input);
            }
        }
    };

    // 为了向后兼容，保留原函数名
    function initAICoachChatArea() {
        ChatManager.initAICoachChatArea();
    }

    // 技能练习区管理器
    const SkillPracticeManager = {
        initSkillPracticeArea() {
            const skillPracticeContainer = Utils.safeQuerySelector('#skill-practice-artboard');
            if (!skillPracticeContainer) return;

            try {
                this.setupSkillPoints(skillPracticeContainer);
                this.setupControlButtons(skillPracticeContainer);
                this.initializeDefaultSkill(skillPracticeContainer);
            } catch (error) {
                console.warn('Error initializing skill practice area:', error);
            }
        },

        setupSkillPoints(container) {
            const skillPoints = container.querySelectorAll('.skill-point-item');
            const skillDetailPanels = container.querySelectorAll('.skill-detail-panel');

            skillPoints.forEach(item => {
                if (!item.dataset.skillBound) {
                    item.addEventListener('click', () => {
                        this.switchSkill(item, skillPoints, skillDetailPanels);
                    });
                    item.dataset.skillBound = 'true';
                }
            });
        },

        switchSkill(selectedItem, allSkillPoints, skillDetailPanels) {
            // 切换当前技能高亮
            allSkillPoints.forEach(i => i.classList.remove('current-skill'));
            selectedItem.classList.add('current-skill');

            // 切换技能详情面板
            const targetId = selectedItem.getAttribute('data-target');
            skillDetailPanels.forEach(panel => {
                if (panel.id === targetId) {
                    panel.style.display = 'block';
                    // 添加淡入动画
                    if (!CONFIG.PERFORMANCE_MODE) {
                        panel.style.opacity = '0';
                        panel.style.transition = 'opacity 0.3s ease';
                        requestAnimationFrame(() => {
                            panel.style.opacity = '1';
                        });
                    }
                } else {
                    panel.style.display = 'none';
                }
            });
        },

        setupControlButtons(container) {
            const buttons = {
                viewHint: container.querySelector('.practice-controls-bar .btn-outline'),
                submitFeedback: container.querySelector('.practice-controls-bar .btn-secondary'),
                nextPractice: container.querySelector('.practice-controls-bar .btn-primary')
            };

            // 查看提示按钮
            if (buttons.viewHint && !buttons.viewHint.dataset.bound) {
                buttons.viewHint.addEventListener('click', () => {
                    this.showHint(container);
                });
                buttons.viewHint.dataset.bound = 'true';
            }

            // 提交反馈按钮
            if (buttons.submitFeedback && !buttons.submitFeedback.dataset.bound) {
                buttons.submitFeedback.addEventListener('click', () => {
                    this.submitFeedback();
                });
                buttons.submitFeedback.dataset.bound = 'true';
            }

            // 下一个练习按钮
            if (buttons.nextPractice && !buttons.nextPractice.dataset.bound) {
                buttons.nextPractice.addEventListener('click', () => {
                    this.goToNextPractice(container);
                });
                buttons.nextPractice.dataset.bound = 'true';
            }
        },

        initializeDefaultSkill(container) {
            const initialSkill = container.querySelector('.skill-point-item.current-skill');
            if (initialSkill) {
                initialSkill.click();
            }
        },

        showHint(container) {
            const hintPanel = container.querySelector('.practice-hint-panel');
            if (hintPanel) {
                hintPanel.style.display = 'block';
                // TODO: 实现提示内容加载
                console.log('Showing practice hint');
            }
        },

        submitFeedback() {
            // TODO: 实现反馈提交逻辑
            NotificationManager.showNotification('反馈已提交！', 'success');
        },

        goToNextPractice(container) {
            const current = container.querySelector('.skill-point-item.current-skill');
            if (current) {
                let next = current.nextElementSibling;
                while (next && !next.classList.contains('skill-point-item')) {
                    next = next.nextElementSibling;
                }
                if (next) {
                    next.click();
                } else {
                    NotificationManager.showNotification('已经是最后一个练习了', 'info');
                }
            }
        }
    };

    // 为了向后兼容，保留原函数名
    function initSkillPracticeArea() {
        SkillPracticeManager.initSkillPracticeArea();
    }

    // 页面加载完成处理
    window.addEventListener('load', () => {
        document.body.classList.add('loaded');
        console.log('🎉 页面加载完成');
    });

    // 导出全局API供外部使用
    window.AIInterviewApp = {
        ArtboardManager,
        SearchManager,
        UserMenuManager,
        ThemeManager,
        NavigationManager,
        NotificationManager,
        ChatManager,
        SkillPracticeManager,
        AccordionManager,
        TabManager,
        Utils,
        CONFIG
    };

    console.log('🚀 AI面试助手动态岛导航系统已完全初始化');

});