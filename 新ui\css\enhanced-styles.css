/*
 * 增强版样式 - UI美化、用户体验和动画
 * 优化版本 - 统一设计系统和性能优化
 * @version 2.0
 * <AUTHOR>
 */

/* CSS变量系统 - 统一设计令牌 */
:root {
    /* 主色彩系统 - 基于品牌色调优化 */
    --color-primary: #6366f1;
    --color-primary-light: #818cf8;
    --color-primary-dark: #4f46e5;
    --color-primary-alpha: rgba(99, 102, 241, 0.1);

    /* 辅助色彩 */
    --color-secondary: #ec4899;
    --color-secondary-light: #f472b6;
    --color-secondary-dark: #db2777;

    /* 功能色彩 */
    --color-accent: #f59e0b;
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    --color-info: #3b82f6;
    
    /* 渐变色系统 - 现代化配色方案 */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);
    --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
    --gradient-warm: linear-gradient(135deg, #f97316 0%, #eab308 100%);
    --gradient-cool: linear-gradient(135deg, #22c55e 0%, #06b6d4 100%);
    --gradient-cosmic: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    --gradient-aurora: linear-gradient(135deg, #a855f7 0%, #06b6d4 50%, #22c55e 100%);
    --gradient-sunset: linear-gradient(135deg, #ff6b6b 0%, #ffa726 50%, #ffeb3b 100%);
    --gradient-ocean: linear-gradient(135deg, #667eea 0%, #06b6d4 100%);
    --gradient-forest: linear-gradient(135deg, #22c55e 0%, #10b981 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

    /* 背景色系统 - 深色主题优化 */
    --bg-primary: #0a0a0f;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-quaternary: #0f3460;
    --bg-glass: rgba(26, 26, 46, 0.8);
    --bg-glass-light: rgba(255, 255, 255, 0.05);
    --bg-card: rgba(255, 255, 255, 0.03);
    --bg-hover: rgba(255, 255, 255, 0.08);
    --bg-overlay: rgba(0, 0, 0, 0.7);
    --bg-overlay-light: rgba(0, 0, 0, 0.3);

    /* 文字颜色系统 - 深色主题优化 */
    --text-primary: #ffffff;
    --text-secondary: #b4b4b8;
    --text-tertiary: #8b8b90;
    --text-muted: #6b6b70;
    --text-inverse: #0a0a0f;
    --text-link: var(--color-primary);
    --text-link-hover: var(--color-primary-light);

    /* 特殊色彩 - 更丰富的色彩系统 */
    --color-accent-blue: #3b82f6;
    --color-accent-green: #22c55e;
    --color-accent-orange: #f97316;
    --color-accent-purple: #a855f7;
    --color-accent-pink: #ec4899;
    --color-accent-gold: #eab308;
    --color-accent-cyan: #06b6d4;
    --color-accent-emerald: #10b981;
    --color-accent-rose: #f43f5e;
    --color-accent-indigo: #6366f1;
    
    /* 阴影系统 - 优化性能和层次感 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
    --shadow-glow-lg: 0 0 40px rgba(99, 102, 241, 0.2);
    --shadow-colored: 0 8px 25px rgba(99, 102, 241, 0.15);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);

    /* 边框半径系统 - 统一圆角规范 */
    --radius-none: 0;
    --radius-xs: 0.125rem;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    /* 间距系统 - 8px基准网格 */
    --space-0: 0;
    --space-1: 0.25rem;  /* 4px */
    --space-2: 0.5rem;   /* 8px */
    --space-3: 0.75rem;  /* 12px */
    --space-4: 1rem;     /* 16px */
    --space-5: 1.25rem;  /* 20px */
    --space-6: 1.5rem;   /* 24px */
    --space-8: 2rem;     /* 32px */
    --space-10: 2.5rem;  /* 40px */
    --space-12: 3rem;    /* 48px */
    --space-16: 4rem;    /* 64px */
    --space-20: 5rem;    /* 80px */
    --space-24: 6rem;    /* 96px */
    
    /* 过渡和动画系统 - 优化性能和用户体验 */
    --transition-all: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease-out;
    --transition-normal: all 0.3s ease-out;
    --transition-slow: all 0.5s ease-out;
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-elastic: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-spring: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    /* 字体系统 */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, monospace;
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Z-index层级系统 - 避免层级冲突 */
    --z-base: 0;
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
    --z-max: 2147483647;
}

/* 深色模式支持 */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-glass: rgba(30, 41, 59, 0.8);
    --text-primary: #f8fafc;
    --text-secondary: #e2e8f0;
    --text-tertiary: #cbd5e1;
    --text-muted: #94a3b8;
}

/* 基础重置 */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    background-image:
        radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(6, 182, 212, 0.1) 0%, transparent 50%);
    background-attachment: fixed;
    min-height: 100vh;
    overflow-x: hidden;
    transition: var(--transition-normal);
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(135deg, transparent 0%, rgba(102, 126, 234, 0.05) 50%, transparent 100%),
        linear-gradient(45deg, transparent 0%, rgba(168, 85, 247, 0.05) 50%, transparent 100%);
    background-size: 400% 400%, 300% 300%;
    animation: gradientShift 20s ease infinite, gradientShift2 15s ease infinite reverse;
    pointer-events: none;
    z-index: -1;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%, 0% 50%; }
    50% { background-position: 100% 50%, 100% 50%; }
}

@keyframes gradientShift2 {
    0%, 100% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.1); }
}

/* 页面容器美化 */
.page-container {
    min-height: 100vh;
    background: var(--bg-glass-light);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: relative;
}

.main-content {
    padding: var(--space-8) var(--space-6);
    max-width: 1400px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

/* 网格背景效果 */
.page-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
    background-size: 50px 50px;
    pointer-events: none;
    z-index: 0;
}

/* 增强的动态岛样式 */
#dynamic-island-container {
    position: fixed;
    top: var(--space-6);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3);
    background: var(--bg-glass);
    backdrop-filter: blur(30px) saturate(180%);
    border-radius: var(--radius-full);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 4px 16px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    z-index: var(--z-fixed);
    transition: var(--transition-all);
    animation: slideDown 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes slideDown {
    0% {
        transform: translateX(-50%) translateY(-100px);
        opacity: 0;
    }
    100% {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
    }
}

/* 岛屿通用样式 */
.island {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 48px;
    height: 48px;
    padding: var(--space-3);
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: var(--transition-spring);
    color: var(--text-primary);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.island::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.island:hover::before {
    left: 100%;
}

.island:hover {
    transform: translateY(-4px) scale(1.05);
    background: var(--bg-hover);
    box-shadow:
        0 12px 32px rgba(0, 0, 0, 0.2),
        0 6px 16px rgba(0, 0, 0, 0.1),
        0 0 24px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(102, 126, 234, 0.4);
}

.island:active {
    transform: translateY(0) scale(0.98);
}

.island .icon {
    width: 20px;
    height: 20px;
    transition: var(--transition-bounce);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.island:hover .icon {
    transform: rotate(5deg) scale(1.1);
}

/* 特殊岛屿样式 */
.logo-island {
    background: var(--gradient-primary);
    color: white;
    animation: pulse 3s infinite;
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
    }
}

.search-island {
    transition: var(--transition-elastic);
    min-width: 44px;
}

.search-island.active {
    min-width: 200px;
    background: rgba(255, 255, 255, 0.9);
    padding: var(--space-2) var(--space-4);
}

#search-input {
    background: transparent;
    border: none;
    outline: none;
    color: var(--text-primary);
    font-size: 14px;
    width: 0;
    opacity: 0;
    transition: var(--transition-elastic);
    margin-left: 0;
}

.search-island.active #search-input {
    width: 150px;
    opacity: 1;
    margin-left: var(--space-2);
}

.notification-island {
    position: relative;
}

.notification-dot {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 12px;
    height: 12px;
    background: var(--color-error);
    border-radius: var(--radius-full);
    border: 2px solid var(--bg-primary);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-4px);
    }
    60% {
        transform: translateY(-2px);
    }
}

.user-island {
    position: relative;
}

.avatar-img {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: var(--transition-bounce);
}

.island:hover .avatar-img {
    border-color: var(--color-primary);
    transform: scale(1.1);
}

/* 工具提示 */
.island[data-tooltip] {
    position: relative;
}

.island[data-tooltip]::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: var(--text-inverse);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-fast);
    pointer-events: none;
    z-index: var(--z-tooltip);
}

.island[data-tooltip]:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* 加载屏幕 */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loading-screen p {
    margin-top: var(--space-4);
    color: var(--text-secondary);
    font-size: var(--font-size-md);
}

/* 用户菜单增强 */
.user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--space-2);
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: var(--space-2);
    min-width: 200px;
    display: none;
    z-index: var(--z-dropdown);
    overflow: hidden;
}

.user-menu a {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3) var(--space-4);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.user-menu a:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(2px);
}

.user-menu .icon {
    width: 16px;
    height: 16px;
    opacity: 0.7;
}

.user-menu hr {
    border: none;
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: var(--space-2) 0;
}

/* Toast 通知系统 */
.toast-container {
    position: fixed;
    bottom: var(--space-6);
    right: var(--space-6);
    z-index: var(--z-toast);
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
    max-width: 400px;
}

.toast {
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transform: translateX(100%);
    transition: var(--transition-elastic);
    position: relative;
    overflow: hidden;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    border-left: 4px solid var(--color-success);
}

.toast.error {
    border-left: 4px solid var(--color-error);
}

.toast.warning {
    border-left: 4px solid var(--color-warning);
}

.toast.info {
    border-left: 4px solid var(--color-info);
}

.toast-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: var(--space-2);
}

.toast-title {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
    font-size: var(--font-size-sm);
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    margin-left: auto;
    font-size: 16px;
    line-height: 1;
    transition: var(--transition-fast);
}

.toast-close:hover {
    color: var(--text-primary);
}

.toast-message {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.4;
    margin: 0;
}

/* 活跃导航状态 */
.nav-island.active {
    background: var(--gradient-primary) !important;
    color: white !important;
    transform: scale(1.1);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(99, 102, 241, 0.4);
}

.nav-island.active .icon {
    transform: scale(1.1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* 搜索结果下拉 */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: var(--space-2);
    max-height: 300px;
    overflow-y: auto;
    z-index: var(--z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-normal);
}

.search-results.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.search-result-item {
    padding: var(--space-3) var(--space-4);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-result-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-title {
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-1);
}

.search-result-description {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
    line-height: 1.3;
}

/* 快捷键提示 */
.keyboard-shortcut {
    display: inline-flex;
    align-items: center;
    padding: 2px 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
    font-size: 10px;
    font-weight: var(--font-weight-medium);
    color: var(--text-tertiary);
    margin-left: auto;
}

/* 画板切换动画 */
.artboard {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.artboard:not(.active-artboard) {
    opacity: 0;
    transform: translateY(20px) scale(0.98);
    pointer-events: none;
}

.artboard.active-artboard {
    opacity: 1;
    transform: translateY(0) scale(1);
    pointer-events: auto;
}

/* 滚动优化 */
.artboards-container {
    scroll-behavior: smooth;
}

/* 聚焦指示器 */
.island:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
    border-radius: var(--radius-lg);
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .island {
        border: 2px solid var(--text-primary);
    }
    
    .island:hover {
        border-color: var(--color-primary);
    }
}

/* 移动端优化 */
@media (max-width: 480px) {
    .toast-container {
        bottom: var(--space-4);
        right: var(--space-4);
        left: var(--space-4);
    }
    
    .toast {
        transform: translateY(100%);
    }
    
    .toast.show {
        transform: translateY(0);
    }
    
    .user-menu {
        right: -100px;
        min-width: 180px;
    }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
    .island,
    .artboard,
    .toast,
    .user-menu,
    .search-results {
        transition: none !important;
        animation: none !important;
    }

    .island::before {
        display: none;
    }
}

/* ========== 页面美化样式 ========== */

/* 美化卡片样式 */
.glass-card {
    background: var(--bg-card);
    backdrop-filter: blur(30px) saturate(180%);
    -webkit-backdrop-filter: blur(30px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.2),
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(255, 255, 255, 0.05);
    transition: var(--transition-spring);
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

.glass-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 24px 48px rgba(0, 0, 0, 0.25),
        0 12px 24px rgba(0, 0, 0, 0.15),
        0 0 32px rgba(102, 126, 234, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

/* 仪表盘网格布局美化 */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-6);
}

.dashboard-card {
    padding: var(--space-6);
    position: relative;
}

.dashboard-card h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-3);
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.dashboard-card p {
    color: var(--text-secondary);
    margin-bottom: var(--space-4);
    line-height: 1.6;
}

/* 卡片头部样式 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
}

.card-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

/* 状态徽章 */
.status-badge {
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.status-success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.status-info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
}

/* 进度条美化 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin: var(--space-4) 0;
    position: relative;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: var(--radius-full);
    transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.progress-bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 卡片动作链接 */
.card-action-link {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
}

.card-action-link:hover {
    color: var(--color-primary-light);
    transform: translateX(4px);
}

/* 按钮美化 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    font-family: inherit;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow:
        0 4px 16px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 12px 32px rgba(102, 126, 234, 0.4),
        0 6px 16px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-secondary {
    background: var(--bg-card);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.btn-secondary:hover {
    background: var(--bg-hover);
    transform: translateY(-2px) scale(1.02);
    box-shadow:
        0 8px 24px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.25);
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-tertiary {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-tertiary:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-block {
    width: 100%;
    margin-bottom: var(--space-3);
}

.btn-sm {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--space-4) var(--space-6);
    font-size: var(--font-size-md);
}

/* 卡片动作按钮组 */
.card-actions {
    display: flex;
    gap: var(--space-3);
    margin-top: var(--space-4);
}

/* 成就列表美化 */
.achievement-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    margin: var(--space-4) 0;
}

.achievement-item {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-3);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
}

.achievement-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateX(4px);
}

.achievement-item.locked {
    opacity: 0.5;
    filter: grayscale(1);
}

.achievement-item svg {
    color: var(--color-accent-gold);
    flex-shrink: 0;
}

.achievement-item div p {
    margin: 0;
}

.achievement-item div p:first-child {
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.achievement-item div p:last-child {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

/* 文本样式 */
.text-muted {
    color: var(--text-secondary);
}

.text-muted-sm {
    color: var(--text-secondary);
    font-size: var(--font-size-xs);
}

/* 页面头部美化 */
.artboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
    padding: var(--space-4) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.artboard-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-actions {
    display: flex;
    gap: var(--space-3);
    align-items: center;
}

/* 面试界面美化 */
.mock-interview-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    height: calc(100vh - 120px);
}

.interview-main-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.video-area {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
    padding: var(--space-4);
    min-height: 400px;
}

.video-feed {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-feed img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-lg);
}

.video-controls {
    position: absolute;
    bottom: var(--space-3);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: var(--space-2);
    background: rgba(0, 0, 0, 0.7);
    padding: var(--space-2);
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
}

.btn-icon {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.participant-name {
    position: absolute;
    top: var(--space-3);
    left: var(--space-3);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

.ai-status {
    position: absolute;
    bottom: var(--space-3);
    right: var(--space-3);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.typing-indicator::after {
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: currentColor;
    animation: typing 1.4s infinite;
}

@keyframes typing {
    0%, 60%, 100% { opacity: 0; }
    30% { opacity: 1; }
}

/* 面试控制栏 */
.interview-controls-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-lg);
}

.interview-timer {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
}

.control-buttons-group {
    display: flex;
    gap: var(--space-3);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .video-area {
        grid-template-columns: 1fr;
        min-height: 300px;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .artboard-header {
        flex-direction: column;
        gap: var(--space-3);
        align-items: flex-start;
    }

    .header-actions {
        width: 100%;
        justify-content: flex-start;
    }

    .card-actions {
        flex-direction: column;
    }

    .interview-controls-bar {
        flex-direction: column;
        gap: var(--space-3);
    }
}

/* 加载动画 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-left: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 悬浮效果 */
.floating-element {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* 脉冲效果 */
.pulse-effect {
    animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(102, 126, 234, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.8);
    }
}

/* AI教练聊天界面美化 */
.ai-coach-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: var(--space-4);
    height: calc(100vh - 120px);
}

.ai-coach-sessions-list {
    padding: var(--space-4);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.sessions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
}

.sessions-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    color: var(--text-primary);
}

.ai-coach-session-item {
    padding: var(--space-3);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-2);
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.ai-coach-session-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
}

.ai-coach-session-item.active {
    background: rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.3);
}

.ai-coach-session-item h5 {
    margin: 0 0 var(--space-1) 0;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.ai-coach-session-item p {
    margin: 0;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.ai-coach-chat-area {
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-header {
    padding: var(--space-4);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    color: var(--text-primary);
}

.chat-header p {
    margin: var(--space-1) 0 0 0;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--color-success);
    animation: pulse 2s infinite;
}

.status-dot.online {
    background: var(--color-success);
}

.message-list-container {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-4);
}

.message-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.message {
    display: flex;
    gap: var(--space-3);
    align-items: flex-start;
}

.message.user-message {
    flex-direction: row-reverse;
}

.avatar-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    padding: var(--space-2);
    color: var(--text-primary);
    flex-shrink: 0;
}

.message-content {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
    max-width: 70%;
}

.message.user-message .message-content {
    align-items: flex-end;
}

.message-bubble {
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.user-message .message-bubble {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: rgba(255, 255, 255, 0.2);
}

.message-bubble p {
    margin: 0;
    line-height: 1.5;
}

.message-timestamp {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    padding: 0 var(--space-2);
}

.quick-replies {
    display: flex;
    gap: var(--space-2);
    margin-top: var(--space-3);
    flex-wrap: wrap;
}

.quick-reply-btn {
    font-size: var(--font-size-xs);
    padding: var(--space-1) var(--space-3);
}

.message-input-area {
    padding: var(--space-4);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.message-input-area .form-textarea {
    width: 100%;
    min-height: 60px;
    resize: vertical;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    padding: var(--space-3);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-family: inherit;
}

.message-input-area .form-textarea:focus {
    outline: none;
    border-color: var(--color-primary);
    background: rgba(255, 255, 255, 0.1);
}

.message-input-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--space-3);
}

/* 表单元素美化 */
.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: var(--space-3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-family: inherit;
    font-size: var(--font-size-sm);
    transition: all 0.2s ease;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: var(--color-primary);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-label {
    display: block;
    margin-bottom: var(--space-2);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.form-group {
    margin-bottom: var(--space-4);
}

.form-group-full {
    grid-column: 1 / -1;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4);
}

/* 技能练习页面美化 */
.skill-practice-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.skill-practice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-4) 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.skill-practice-header h2 {
    margin: 0;
    font-size: var(--font-size-2xl);
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.skill-categories {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.skill-category {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.skill-category h4 {
    margin: 0 0 var(--space-4) 0;
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.skill-points-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-3);
}

.skill-point-item {
    padding: var(--space-3) var(--space-4);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    text-align: center;
}

.skill-point-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.skill-point-item.current-skill {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.practice-area-container {
    padding: var(--space-6);
    min-height: 400px;
}

.practice-info-bar {
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-4);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.practice-info-bar h3 {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--font-size-lg);
    color: var(--text-primary);
}

.practice-info-bar p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.practice-question-area {
    margin: var(--space-6) 0;
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.placeholder-content {
    color: var(--text-secondary);
    font-style: italic;
    text-align: center;
}

.practice-controls-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-4);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 个人资料页面美化 */
.profile-settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
}

.profile-card,
.settings-card,
.gamification-card {
    padding: var(--space-6);
}

.profile-card h3,
.settings-card h3,
.gamification-card h3 {
    margin: 0 0 var(--space-4) 0;
    font-size: var(--font-size-xl);
    color: var(--text-primary);
}

.profile-avatar-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-6);
}

.profile-avatar-section img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 4px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
}

.profile-avatar-section img:hover {
    border-color: var(--color-primary);
    transform: scale(1.05);
}

/* 按钮组美化 */
.btn-group {
    display: flex;
    border-radius: var(--radius-lg);
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group .btn {
    border-radius: 0;
    border: none;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-group .btn:last-child {
    border-right: none;
}

.btn-group .btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

/* 表单开关美化 */
.form-switch {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

.form-switch input[type="checkbox"] {
    width: 44px;
    height: 24px;
    appearance: none;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
}

.form-switch input[type="checkbox"]:checked {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.form-switch input[type="checkbox"]::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.form-switch input[type="checkbox"]:checked::before {
    transform: translateX(20px);
}

/* 成就系统美化 */
.xp-level-section {
    margin-bottom: var(--space-6);
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.xp-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-3);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--space-4);
    margin-top: var(--space-4);
}

.achievement-badge {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
    position: relative;
    cursor: pointer;
}

.achievement-badge:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.achievement-badge.locked {
    opacity: 0.4;
    filter: grayscale(1);
}

.achievement-badge svg {
    color: var(--color-accent-gold);
    transition: all 0.2s ease;
}

.achievement-badge:hover svg {
    transform: scale(1.1);
}

.achievement-badge p {
    margin: 0;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    text-align: center;
}

.badge-tooltip {
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-primary);
    color: var(--text-inverse);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    z-index: 10;
}

.achievement-badge:hover .badge-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* 社群页面美化 */
.community-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
    height: calc(100vh - 120px);
}

.community-header {
    padding: var(--space-4);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.community-main-content {
    display: grid;
    grid-template-columns: 220px 1fr 240px;
    gap: var(--space-4);
    flex: 1;
    overflow: hidden;
}

.community-sidebar-left,
.community-sidebar-right {
    overflow-y: auto;
}

.community-feed-area {
    overflow-y: auto;
    padding: var(--space-4);
}

.post-card {
    margin-bottom: var(--space-4);
    transition: all 0.2s ease;
}

.post-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.post-card h4 {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--font-size-md);
    color: var(--text-primary);
    line-height: 1.4;
}

.post-card p {
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.5;
}

.btn-icon.small {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: var(--space-1);
    padding: var(--space-1);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
    font-size: var(--font-size-xs);
}

.btn-icon.small:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

/* 职业发展页面美化 */
.career-dev-container {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.career-dev-header {
    padding: var(--space-4);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.career-dev-main-content {
    display: grid;
    grid-template-columns: 3fr 1fr;
    gap: var(--space-6);
}

.career-column-main,
.career-column-sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.content-card {
    padding: var(--space-4);
}

.content-card h4 {
    margin: 0 0 var(--space-3) 0;
    font-size: var(--font-size-lg);
    color: var(--text-primary);
}

.resource-card {
    padding: var(--space-4);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: var(--space-3);
    transition: all 0.2s ease;
}

.resource-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.resource-card h5 {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--font-size-md);
    color: var(--text-primary);
}

.resource-card p {
    margin: 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* 特殊效果和动画 */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
}

.btn-link {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
}

.btn-link:hover {
    color: var(--color-primary-light);
    transform: translateX(2px);
}

.btn-small {
    padding: var(--space-1) var(--space-2);
    font-size: var(--font-size-xs);
}

/* 反馈标签美化 */
.feedback-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.feedback-list li {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.feedback-tag {
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.feedback-tag.positive {
    background: var(--color-success);
    color: white;
}

.feedback-tag.neutral {
    background: var(--color-warning);
    color: white;
}

.feedback-tag.negative {
    background: var(--color-error);
    color: white;
}

/* 侧边栏模块美化 */
.sidebar-module {
    margin-bottom: var(--space-4);
    padding: var(--space-4);
}

.sidebar-module h4 {
    margin: 0 0 var(--space-3) 0;
    font-size: var(--font-size-md);
    color: var(--text-primary);
}

.question-actions {
    display: flex;
    gap: var(--space-2);
    margin-top: var(--space-3);
}

/* 布局优化 */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.container-sm {
    max-width: 640px;
}

.container-md {
    max-width: 768px;
}

.container-lg {
    max-width: 1024px;
}

.container-xl {
    max-width: 1280px;
}

/* 网格系统优化 */
.grid {
    display: grid;
    gap: var(--space-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.col-span-1 { grid-column: span 1; }
.col-span-2 { grid-column: span 2; }
.col-span-3 { grid-column: span 3; }
.col-span-4 { grid-column: span 4; }

/* Flexbox 工具类 */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

/* 间距工具类 */
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

/* 最终响应式调整 */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--space-4);
    }

    .main-content {
        padding: var(--space-6) var(--space-4);
    }

    #dynamic-island-container {
        top: var(--space-4);
        gap: var(--space-2);
        padding: var(--space-2);
    }

    .island {
        min-width: 44px;
        height: 44px;
        padding: var(--space-2);
    }

    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: 1fr;
    }

    .profile-settings-grid {
        grid-template-columns: 1fr;
    }

    .community-main-content {
        grid-template-columns: 1fr;
    }

    .community-sidebar-left,
    .community-sidebar-right {
        display: none;
    }

    .career-dev-main-content {
        grid-template-columns: 1fr;
    }

    .ai-coach-container {
        grid-template-columns: 1fr;
    }

    .ai-coach-sessions-list {
        display: none;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--space-3);
    }

    .main-content {
        padding: var(--space-4) var(--space-3);
    }

    #dynamic-island-container {
        flex-wrap: wrap;
        max-width: calc(100vw - 2rem);
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .glass-card:hover {
        transform: translateY(-4px) scale(1.01);
    }
}
