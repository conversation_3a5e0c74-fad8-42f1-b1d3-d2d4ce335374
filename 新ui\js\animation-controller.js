// Advanced Animation Controller with Performance Optimizations
class AdvancedAnimationController {
    // Singleton pattern
    static instance = null;
    
    constructor() {
        if (AdvancedAnimationController.instance) {
            return AdvancedAnimationController.instance;
        }
        
        this.animations = new Map();
        this.timeline = [];
        this.isPlaying = false;
        this.currentTime = 0;
        this.playbackRate = 1;
        this.observers = new Map();
        this.performanceConfig = null;
        this.animationFrame = null;
        this.isInitialized = false;
        
        // Bind methods
        this.handleResize = this.throttle(this.handleResize.bind(this), 250);
        this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
        
        this.init();
        AdvancedAnimationController.instance = this;
    }
    
    static getInstance() {
        if (!AdvancedAnimationController.instance) {
            new AdvancedAnimationController();
        }
        return AdvancedAnimationController.instance;
    }
    
    async init() {
        try {
            // Wait for performance config
            if (window.PerformanceConfig) {
                this.performanceConfig = window.PerformanceConfig.getInstance();
                await this.performanceConfig.initialize();
            }
            
            this.setupIntersectionObserver();
            this.setupMutationObserver();
            this.bindEvents();
            this.setupPerformanceOptimizations();
            
            this.isInitialized = true;
            console.log('AdvancedAnimationController initialized successfully');
        } catch (error) {
            console.error('Failed to initialize AdvancedAnimationController:', error);
        }
    }
    
    setupPerformanceOptimizations() {
        if (!this.performanceConfig) return;
        
        const settings = this.performanceConfig.getOptimizedSettings();
        
        // Disable complex animations on low-end devices
        if (settings.animations.complexity === 'minimal') {
            this.disableComplexAnimations();
        }
        
        // Reduce animation frequency based on performance
        if (settings.animations.fps < 60) {
            this.setTargetFPS(settings.animations.fps);
        }
    }
    
    setTargetFPS(targetFPS) {
        this.frameInterval = 1000 / targetFPS;
        this.lastFrameTime = 0;
    }
    
    shouldSkipFrame(currentTime) {
        if (!this.frameInterval) return false;
        
        if (currentTime - this.lastFrameTime < this.frameInterval) {
            return true;
        }
        this.lastFrameTime = currentTime;
        return false;
    }
    
    // 创建动画序列
    createSequence(name, steps) {
        const sequence = {
            name,
            steps,
            duration: 0,
            delay: 0,
            easing: 'ease-out',
            onComplete: null,
            onProgress: null
        };
        
        // 计算总时长
        sequence.duration = steps.reduce((total, step) => 
            Math.max(total, (step.delay || 0) + (step.duration || 0)), 0);
        
        this.animations.set(name, sequence);
        return sequence;
    }
    
    // 并行动画
    parallel(animations) {
        const promises = animations.map(anim => this.play(anim));
        return Promise.all(promises);
    }
    
    // 串行动画
    sequence(animations) {
        return animations.reduce((promise, anim) => 
            promise.then(() => this.play(anim)), Promise.resolve());
    }
    
    // 交错动画
    stagger(elements, animation, staggerDelay = 100) {
        const promises = [];
        
        elements.forEach((element, index) => {
            const delayedAnimation = {
                ...animation,
                delay: (animation.delay || 0) + (index * staggerDelay)
            };
            
            promises.push(this.animateElement(element, delayedAnimation));
        });
        
        return Promise.all(promises);
    }
    
    // 元素动画
    animateElement(element, options) {
        return new Promise((resolve) => {
            const {
                duration = 300,
                delay = 0,
                easing = 'ease-out',
                from = {},
                to = {},
                onComplete = null,
                onProgress = null
            } = options;
            
            // 设置初始状态
            Object.assign(element.style, from);
            
            // 应用过渡
            element.style.transition = `all ${duration}ms ${easing}`;
            
            setTimeout(() => {
                // 应用最终状态
                Object.assign(element.style, to);
                
                // 监听过渡完成
                const handleTransitionEnd = (e) => {
                    if (e.target === element) {
                        element.removeEventListener('transitionend', handleTransitionEnd);
                        element.style.transition = '';
                        if (onComplete) onComplete(element);
                        resolve(element);
                    }
                };
                
                element.addEventListener('transitionend', handleTransitionEnd);
                
                // 进度监听（模拟）
                if (onProgress) {
                    const progressInterval = setInterval(() => {
                        const elapsed = Date.now() - startTime;
                        const progress = Math.min(elapsed / duration, 1);
                        onProgress(progress, element);
                        
                        if (progress >= 1) {
                            clearInterval(progressInterval);
                        }
                    }, 16);
                }
                
                const startTime = Date.now();
            }, delay);
        });
    }
    
    // 关键帧动画
    keyframes(element, keyframes, options = {}) {
        const {
            duration = 300,
            delay = 0,
            easing = 'ease-out',
            iterations = 1,
            direction = 'normal',
            fillMode = 'forwards'
        } = options;
        
        const animationName = `keyframe-${Date.now()}-${Math.random()}`;
        
        // 创建关键帧CSS
        const keyframeCSS = this.generateKeyframeCSS(animationName, keyframes);
        
        // 添加样式到头部
        const style = document.createElement('style');
        style.textContent = keyframeCSS;
        document.head.appendChild(style);
        
        // 应用动画
        element.style.animation = `${animationName} ${duration}ms ${easing} ${delay}ms ${iterations} ${direction} ${fillMode}`;
        
        return new Promise((resolve) => {
            const handleAnimationEnd = () => {
                element.removeEventListener('animationend', handleAnimationEnd);
                element.style.animation = '';
                document.head.removeChild(style);
                resolve(element);
            };
            
            element.addEventListener('animationend', handleAnimationEnd);
        });
    }
    
    generateKeyframeCSS(name, keyframes) {
        let css = `@keyframes ${name} {\n`;
        
        Object.entries(keyframes).forEach(([percentage, styles]) => {
            css += `  ${percentage} {\n`;
            Object.entries(styles).forEach(([property, value]) => {
                css += `    ${this.camelToKebab(property)}: ${value};\n`;
            });
            css += `  }\n`;
        });
        
        css += '}';
        return css;
    }
    
    camelToKebab(str) {
        return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
    }
    
    // 弹性动画
    spring(element, options) {
        const {
            from = {},
            to = {},
            stiffness = 200,
            damping = 10,
            mass = 1,
            onComplete = null
        } = options;
        
        // 设置初始状态
        Object.assign(element.style, from);
        
        // Spring动画实现
        const keys = Object.keys(to);
        const startValues = keys.map(key => parseFloat(from[key]) || 0);
        const endValues = keys.map(key => parseFloat(to[key]) || 0);
        const velocities = new Array(keys.length).fill(0);
        
        let animationId;
        const animate = () => {
            let isComplete = true;
            
            keys.forEach((key, index) => {
                const current = parseFloat(element.style[key]) || 0;
                const target = endValues[index];
                const displacement = target - current;
                
                const springForce = displacement * stiffness;
                const dampingForce = velocities[index] * damping;
                const acceleration = (springForce - dampingForce) / mass;
                
                velocities[index] += acceleration * 0.016; // 60fps
                const newValue = current + velocities[index] * 0.016;
                
                element.style[key] = newValue + (typeof to[key] === 'string' ? 
                    to[key].replace(/[\d.-]+/, '') : '');
                
                if (Math.abs(displacement) > 0.1 || Math.abs(velocities[index]) > 0.1) {
                    isComplete = false;
                }
            });
            
            if (isComplete) {
                Object.assign(element.style, to);
                if (onComplete) onComplete(element);
            } else {
                animationId = requestAnimationFrame(animate);
            }
        };
        
        animationId = requestAnimationFrame(animate);
        
        return {
            cancel: () => cancelAnimationFrame(animationId)
        };
    }
    
    // 视觉缓动函数
    easings = {
        linear: t => t,
        easeInQuad: t => t * t,
        easeOutQuad: t => t * (2 - t),
        easeInOutQuad: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
        easeInCubic: t => t * t * t,
        easeOutCubic: t => (--t) * t * t + 1,
        easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
        easeInBounce: t => 1 - this.easings.easeOutBounce(1 - t),
        easeOutBounce: t => {
            if (t < 1 / 2.75) return 7.5625 * t * t;
            if (t < 2 / 2.75) return 7.5625 * (t -= 1.5 / 2.75) * t + 0.75;
            if (t < 2.5 / 2.75) return 7.5625 * (t -= 2.25 / 2.75) * t + 0.9375;
            return 7.5625 * (t -= 2.625 / 2.75) * t + 0.984375;
        },
        elastic: t => Math.sin(13 * Math.PI / 2 * t) * Math.pow(2, 10 * (t - 1))
    };
    
    // 自定义缓动动画
    customEasing(element, options) {
        const {
            from = {},
            to = {},
            duration = 300,
            easing = 'easeOutQuad',
            onComplete = null,
            onProgress = null
        } = options;
        
        const startTime = Date.now();
        const startValues = {};
        const changeValues = {};
        
        // 计算起始值和变化量
        Object.keys(to).forEach(key => {
            startValues[key] = parseFloat(from[key]) || 0;
            changeValues[key] = parseFloat(to[key]) - startValues[key];
        });
        
        let animationId;
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const easedProgress = this.easings[easing](progress);
            
            Object.keys(to).forEach(key => {
                const currentValue = startValues[key] + changeValues[key] * easedProgress;
                element.style[key] = currentValue + (typeof to[key] === 'string' ? 
                    to[key].replace(/[\d.-]+/, '') : '');
            });
            
            if (onProgress) onProgress(progress, element);
            
            if (progress < 1) {
                animationId = requestAnimationFrame(animate);
            } else {
                Object.assign(element.style, to);
                if (onComplete) onComplete(element);
            }
        };
        
        animationId = requestAnimationFrame(animate);
        
        return {
            cancel: () => cancelAnimationFrame(animationId)
        };
    }
    
    // 交互观察器
    setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const element = entry.target;
                const animationName = element.dataset.scrollAnimation;
                
                if (entry.isIntersecting && animationName) {
                    this.triggerScrollAnimation(element, animationName);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });
        
        // 观察所有有滚动动画的元素
        document.querySelectorAll('[data-scroll-animation]').forEach(el => {
            observer.observe(el);
        });
        
        this.observers.set('intersection', observer);
    }
    
    setupMutationObserver() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1 && node.dataset.scrollAnimation) {
                        this.observers.get('intersection').observe(node);
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        this.observers.set('mutation', observer);
    }
    
    triggerScrollAnimation(element, animationName) {
        const animations = {
            fadeInUp: {
                from: { opacity: '0', transform: 'translateY(50px)' },
                to: { opacity: '1', transform: 'translateY(0)' },
                duration: 600
            },
            fadeInLeft: {
                from: { opacity: '0', transform: 'translateX(-50px)' },
                to: { opacity: '1', transform: 'translateX(0)' },
                duration: 600
            },
            scaleIn: {
                from: { opacity: '0', transform: 'scale(0.8)' },
                to: { opacity: '1', transform: 'scale(1)' },
                duration: 500
            },
            rotateIn: {
                from: { opacity: '0', transform: 'rotate(-180deg) scale(0.5)' },
                to: { opacity: '1', transform: 'rotate(0deg) scale(1)' },
                duration: 800
            }
        };
        
        const animation = animations[animationName];
        if (animation) {
            this.animateElement(element, animation);
        }
    }
    
    bindEvents() {
        // 性能监听
        window.addEventListener('resize', this.throttle(() => {
            this.handleResize();
        }, 100));
        
        // 可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAllAnimations();
            } else {
                this.resumeAllAnimations();
            }
        });
    }
    
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    pauseAllAnimations() {
        document.querySelectorAll('*').forEach(el => {
            const style = window.getComputedStyle(el);
            if (style.animationName !== 'none') {
                el.style.animationPlayState = 'paused';
            }
        });
    }
    
    resumeAllAnimations() {
        document.querySelectorAll('*').forEach(el => {
            el.style.animationPlayState = 'running';
        });
    }
    
    handleResize() {
        // 处理窗口大小变化时的动画调整
        if (window.innerWidth < 768) {
            this.disableComplexAnimations();
        } else {
            this.enableComplexAnimations();
        }
    }
    
    disableComplexAnimations() {
        document.body.classList.add('reduced-animations');
    }
    
    enableComplexAnimations() {
        document.body.classList.remove('reduced-animations');
    }
    
    // 销毁方法
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.animations.clear();
    }
}

// 粒子文字效果
class ParticleText {
    constructor(text, container, options = {}) {
        this.text = text;
        this.container = typeof container === 'string' ? 
            document.querySelector(container) : container;
        this.particles = [];
        this.mouse = { x: 0, y: 0 };
        this.animationId = null;
        
        this.options = {
            fontSize: options.fontSize || 60,
            particleSize: options.particleSize || 2,
            particleCount: options.particleCount || 100,
            color: options.color || '#6366f1',
            interactive: options.interactive !== false,
            speed: options.speed || 0.5
        };
        
        this.init();
    }
    
    init() {
        this.createCanvas();
        this.createTextParticles();
        this.bindEvents();
        this.animate();
    }
    
    createCanvas() {
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.canvas.width = this.container.offsetWidth;
        this.canvas.height = this.container.offsetHeight;
        this.container.appendChild(this.canvas);
    }
    
    createTextParticles() {
        // 创建临时canvas来获取文字像素数据
        const tempCanvas = document.createElement('canvas');
        const tempCtx = tempCanvas.getContext('2d');
        
        tempCanvas.width = this.canvas.width;
        tempCanvas.height = this.canvas.height;
        
        tempCtx.font = `${this.options.fontSize}px Arial`;
        tempCtx.fillStyle = '#000';
        tempCtx.textAlign = 'center';
        tempCtx.fillText(this.text, tempCanvas.width / 2, tempCanvas.height / 2);
        
        const imageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
        const pixels = imageData.data;
        
        // 从像素数据创建粒子
        for (let y = 0; y < tempCanvas.height; y += 4) {
            for (let x = 0; x < tempCanvas.width; x += 4) {
                const index = (y * tempCanvas.width + x) * 4;
                const alpha = pixels[index + 3];
                
                if (alpha > 128) {
                    this.particles.push({
                        originalX: x,
                        originalY: y,
                        x: Math.random() * this.canvas.width,
                        y: Math.random() * this.canvas.height,
                        vx: 0,
                        vy: 0,
                        size: Math.random() * this.options.particleSize + 1
                    });
                }
            }
        }
    }
    
    bindEvents() {
        if (this.options.interactive) {
            this.canvas.addEventListener('mousemove', (e) => {
                const rect = this.canvas.getBoundingClientRect();
                this.mouse.x = e.clientX - rect.left;
                this.mouse.y = e.clientY - rect.top;
            });
        }
    }
    
    animate() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        this.particles.forEach(particle => {
            // 计算朝向原始位置的力
            const dx = particle.originalX - particle.x;
            const dy = particle.originalY - particle.y;
            
            particle.vx += dx * 0.01;
            particle.vy += dy * 0.01;
            
            // 鼠标交互
            if (this.options.interactive) {
                const mouseX = this.mouse.x - particle.x;
                const mouseY = this.mouse.y - particle.y;
                const mouseDistance = Math.sqrt(mouseX * mouseX + mouseY * mouseY);
                
                if (mouseDistance < 100) {
                    particle.vx -= mouseX * 0.001;
                    particle.vy -= mouseY * 0.001;
                }
            }
            
            // 阻尼
            particle.vx *= 0.95;
            particle.vy *= 0.95;
            
            // 更新位置
            particle.x += particle.vx;
            particle.y += particle.vy;
            
            // 绘制粒子
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fillStyle = this.options.color;
            this.ctx.fill();
        });
        
        this.animationId = requestAnimationFrame(() => this.animate());
    }
    
    destroy() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        if (this.canvas && this.canvas.parentNode) {
            this.canvas.parentNode.removeChild(this.canvas);
        }
    }
}

// 导出类
window.AdvancedAnimationController = AdvancedAnimationController;
window.ParticleText = ParticleText;
