# Augment工作空间 (Augment Workspace)

这是基于**ACSD Protocol v1.0**的智能开发工作空间，专为Augment代码库上下文引擎和Cursor开发环境优化。

## 📁 文件结构

```
augment_workspace/
├── README.md                    # 本文档
├── ACSD_Protocol_v1.0.md       # 完整协议文档
├── project_context.md          # 项目上下文记忆
├── task_record_template.md     # 任务记录模板
├── completed_tasks/            # 已完成任务归档
└── active_tasks/               # 活跃任务记录
```

## 🚀 快速开始

### 1. 协议激活
当AI助手开始工作时，会自动：
- 读取 `project_context.md` 获取项目上下文
- 根据任务复杂度选择适当的执行流程
- 创建任务记录文件跟踪进度

### 2. 任务执行流程

#### 简单任务 (Simple Tasks)
```
RESEARCH → IMPLEMENT → REVIEW
```
- 单文件修改
- 明确需求
- 无架构影响

#### 复杂任务 (Complex Tasks)
```
RESEARCH → UNDERSTAND → CREATE → IMPLEMENT → REVIEW
```
- 多文件协调
- 模糊需求需要澄清
- 涉及架构级变更

### 3. 工具集成

#### 必用工具
- **codebase-retrieval**: 代码库上下文分析（强制使用）
- **interactive_feedback**: 用户交互和确认
- **sequential_thinking**: 复杂问题分析

#### 辅助工具
- **browser_***: Web相关任务
- **str-replace-editor**: 精确代码编辑
- **resolve-library-id**: 第三方库集成

## 📋 使用指南

### 创建新任务
1. 复制 `task_record_template.md`
2. 重命名为 `task_record_YYYYMMDD_HHMM.md`
3. AI助手会自动填写各个阶段的内容

### 任务完成后
1. 任务记录会移动到 `completed_tasks/`
2. 重要变更会更新 `project_context.md`
3. 协议会自动归档和总结

## 🎯 协议特色

### 代码库感知优先
- 任何代码修改前必须分析上下文
- 尊重现有架构和代码风格
- 充分利用Augment的实时索引

### 渐进式严格
- 简单任务快速执行
- 复杂任务严格流程
- 智能判断任务复杂度

### 持续交付导向
- 小步快跑，频繁交付
- 测试驱动开发
- 持续优化重构

## 🔧 代码规范

### Augment代码块格式
```html
<augment_code_snippet path="文件路径" mode="EXCERPT">
```javascript
// {{AUGMENT:
// Action: [Added|Modified|Removed]
// Reason: [简要说明]
// Timestamp: [YYYY-MM-DD HH:MM:SS +08:00]
// }}
// {{START MODIFICATION}}
... 修改的代码 ...
// {{END MODIFICATION}}
```
</augment_code_snippet>
```

### 质量标准
- 上下文完整，路径明确
- 变更清晰，中文注释
- 包含完整错误处理
- 遵循现有代码风格

## ⚠️ 重要规则

### 绝对禁止
❌ 不使用codebase-retrieval就修改代码  
❌ 重写整个文件而不是精确修改  
❌ 使用占位符或伪代码  
❌ 修改无关代码  
❌ 引入未验证的依赖  

### 强制要求
✅ IMPLEMENT前必须同步project_context.md  
✅ 关键节点必须调用interactive_feedback  
✅ 复杂任务必须使用sequential_thinking  
✅ 所有代码变更必须使用Augment格式  

## 📊 当前项目状态

- **项目类型**: AI面试助手 Web应用
- **技术栈**: 原生JavaScript + PWA
- **架构**: 模块化管理器系统
- **版本**: v2.0.0 (优化版本)
- **协议版本**: ACSD Protocol v1.0

## 🔄 版本历史

- **v1.0** (2025-01-27): 初始化ACSD Protocol工作空间
- **项目v2.0.0** (2024年12月): AI面试助手重大优化

---

**使用此工作空间，AI助手将以最高效率和质量完成您的开发任务！** 🚀
