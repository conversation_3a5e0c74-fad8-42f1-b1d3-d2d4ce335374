<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1200" height="900" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" 
        refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
        </marker>
    </defs>
    <!-- 背景 -->
    <rect width="1200" height="900" fill="#f9f9f9" />
    
    <!-- 标题 -->
    <text x="600" y="40" font-size="28" font-weight="bold" text-anchor="middle" fill="#333">汽车引擎智能诊断管家架构图</text>
    
    <!-- 左侧层次标签 -->
    <g>
        <!-- 展示层 -->
        <rect x="12" y="70" width="98" height="60" fill="#4CAF50" />
        <text x="61" y="106" font-size="18" font-weight="bold" text-anchor="middle" fill="white">展示层</text>
        <svg width="800" height="3000" xmlns="http://www.w3.org/2000/svg">

  
  
 

</svg>
        <!-- 前端层 -->
        <rect x="12" y="170" width="98" height="60" fill="#4CAF50" />
        <text x="61" y="206" font-size="18" font-weight="bold" text-anchor="middle" fill="white">前端层</text>
        
        <!-- 网关层 -->
        <rect x="12" y="364" width="98" height="60" fill="#4CAF50" />
        <text x="61" y="400" font-size="18" font-weight="bold" text-anchor="middle" fill="white">网关层</text>
        
        <!-- 管理层 -->
        <rect x="12" y="434" width="98" height="60" fill="#4CAF50" />
        <text x="61" y="470" font-size="18" font-weight="bold" text-anchor="middle" fill="white">业务层</text>
        
        <!-- AI层 -->
        <rect x="12" y="680" width="98" height="60" fill="#4CAF50" />
        <text x="61" y="716" font-size="18" font-weight="bold" text-anchor="middle" fill="white">AI层</text>
        
        <!-- 服务层 -->
        <rect x="12" y="750" width="98" height="60" fill="#4CAF50" />
        <text x="61" y="786" font-size="18" font-weight="bold" text-anchor="middle" fill="white">服务层</text>
        
        <!-- 基础设施层 -->
        <rect x="12" y="850" width="98" height="60" fill="#4CAF50" />
        <text x="61" y="886" font-size="18" font-weight="bold" text-anchor="middle" fill="white">基础设施层</text>
    </g>
    
    <!-- 展示层内容 -->
    <g>
        <rect x="120" y="70" width="1070" height="60" fill="#f0f0f0" stroke="#ddd" stroke-width="1" />
        
        <!-- 用户角色 -->
        <g>
            <rect x="150" y="80" width="140" height="40" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="220" y="105" font-size="16" text-anchor="middle" fill="#333">车主用户</text>
        </g>
        
        <g>
            <rect x="320" y="80" width="140" height="40" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="390" y="105" font-size="16" text-anchor="middle" fill="#333">维修技师</text>
        </g>
        
        <g>
            <rect x="490" y="80" width="140" height="40" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="560" y="105" font-size="16" text-anchor="middle" fill="#333">内容贡献者</text>
        </g>
        
        <g>
            <rect x="660" y="80" width="140" height="40" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="730" y="105" font-size="16" text-anchor="middle" fill="#333">系统管理员</text>
        </g>
        
        <g>
            <rect x="830" y="80" width="140" height="40" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="900" y="105" font-size="16" text-anchor="middle" fill="#333">数据分析师</text>
        </g>
    </g>
    
    <!-- 前端层内容 -->
    <g>
        <!-- 小程序端 -->
        <rect x="120" y="170" width="530" height="160" fill="#f0f0f0" stroke="#ddd" stroke-width="1" />
        <text x="385" y="190" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">APP前端</text>
        
        <g>
            <rect x="150" y="210" width="100" height="40" rx="5" ry="5" fill="#B2EBF2" stroke="#ddd" stroke-width="1" />
            <text x="200" y="235" font-size="14" text-anchor="middle" fill="#333">故障诊断</text>
        </g>
        
        <g>
            <rect x="270" y="210" width="100" height="40" rx="5" ry="5" fill="#B2EBF2" stroke="#ddd" stroke-width="1" />
            <text x="320" y="235" font-size="14" text-anchor="middle" fill="#333">维修建议</text>
        </g>
        
        <g>
            <rect x="390" y="210" width="100" height="40" rx="5" ry="5" fill="#B2EBF2" stroke="#ddd" stroke-width="1" />
            <text x="440" y="235" font-size="14" text-anchor="middle" fill="#333">AI问答</text>
        </g>
        
        <g>
            <rect x="510" y="210" width="100" height="40" rx="5" ry="5" fill="#B2EBF2" stroke="#ddd" stroke-width="1" />
            <text x="560" y="235" font-size="14" text-anchor="middle" fill="#333">用户中心</text>
        </g>
        
        <g>
            <rect x="150" y="270" width="100" height="40" rx="5" ry="5" fill="#B2EBF2" stroke="#ddd" stroke-width="1" />
            <text x="200" y="295" font-size="14" text-anchor="middle" fill="#333">历史记录</text>
        </g>
        
        <g>
            <rect x="270" y="270" width="100" height="40" rx="5" ry="5" fill="#B2EBF2" stroke="#ddd" stroke-width="1" />
            <text x="320" y="295" font-size="14" text-anchor="middle" fill="#333">视频详情</text>
        </g>
        
        <g>
            <rect x="390" y="270" width="100" height="40" rx="5" ry="5" fill="#B2EBF2" stroke="#ddd" stroke-width="1" />
            <text x="440" y="295" font-size="14" text-anchor="middle" fill="#333">消息中心</text>
        </g>
        
        <!-- 管理系统前端 -->
        <rect x="660" y="170" width="530" height="160" fill="#f0f0f0" stroke="#ddd" stroke-width="1" />
        <text x="925" y="190" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Vue管理系统前端</text>
        
        <g>
            <rect x="690" y="210" width="100" height="40" rx="5" ry="5" fill="#FFECB3" stroke="#ddd" stroke-width="1" />
            <text x="740" y="235" font-size="14" text-anchor="middle" fill="#333">用户管理</text>
        </g>
        
        <g>
            <rect x="810" y="210" width="100" height="40" rx="5" ry="5" fill="#FFECB3" stroke="#ddd" stroke-width="1" />
            <text x="860" y="235" font-size="14" text-anchor="middle" fill="#333">故障管理</text>
        </g>
        
        <g>
            <rect x="930" y="210" width="100" height="40" rx="5" ry="5" fill="#FFECB3" stroke="#ddd" stroke-width="1" />
            <text x="980" y="235" font-size="14" text-anchor="middle" fill="#333">建议管理</text>
        </g>
        
        <g>
            <rect x="1050" y="210" width="100" height="40" rx="5" ry="5" fill="#FFECB3" stroke="#ddd" stroke-width="1" />
            <text x="1100" y="235" font-size="14" text-anchor="middle" fill="#333">数据分析</text>
        </g>
        
        <g>
            <rect x="690" y="270" width="100" height="40" rx="5" ry="5" fill="#FFECB3" stroke="#ddd" stroke-width="1" />
            <text x="740" y="295" font-size="14" text-anchor="middle" fill="#333">内容管理</text>
        </g>
        
        <g>
            <rect x="810" y="270" width="100" height="40" rx="5" ry="5" fill="#FFECB3" stroke="#ddd" stroke-width="1" />
            <text x="860" y="295" font-size="14" text-anchor="middle" fill="#333">视频管理</text>
        </g>
        
        <g>
            <rect x="930" y="270" width="100" height="40" rx="5" ry="5" fill="#FFECB3" stroke="#ddd" stroke-width="1" />
            <text x="980" y="295" font-size="14" text-anchor="middle" fill="#333">历史记录</text>
        </g>
        
        <g>
            <rect x="1050" y="270" width="100" height="40" rx="5" ry="5" fill="#FFECB3" stroke="#ddd" stroke-width="1" />
            <text x="1100" y="295" font-size="14" text-anchor="middle" fill="#333">系统设置</text>
        </g>
    </g>
    
    <!-- 网关层内容 -->
    <g>
        <rect x="120" y="364" width="1070" height="60" fill="#f0f0f0" stroke="#ddd" stroke-width="1" />
        
        <g>
            <rect x="300" y="374" width="200" height="40" rx="5" ry="5" fill="#BBDEFB" stroke="#ddd" stroke-width="1" />
            <text x="400" y="399" font-size="16" text-anchor="middle" fill="#333">APP API网关</text>
        </g>
        
        <g>
            <rect x="600" y="374" width="200" height="40" rx="5" ry="5" fill="#BBDEFB" stroke="#ddd" stroke-width="1" />
            <text x="700" y="399" font-size="16" text-anchor="middle" fill="#333">管理系统API网关</text>
        </g>
        
        <g>
            <rect x="900" y="374" width="200" height="40" rx="5" ry="5" fill="#BBDEFB" stroke="#ddd" stroke-width="1" />
            <text x="1000" y="399" font-size="16" text-anchor="middle" fill="#333">Nginx反向代理</text>
        </g>
    </g>
    
    <!-- 管理层内容 -->
    <g>
        <rect x="120" y="434" width="1070" height="230" fill="#f0f0f0" stroke="#ddd" stroke-width="1" />
        
        <g>
            <rect x="150" y="444" width="150" height="210" rx="5" ry="5" fill="#FFCDD2" stroke="#ddd" stroke-width="1" />
            <text x="225" y="464" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">数据采集分析</text>
            
            <rect x="170" y="484" width="110" height="40" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="225" y="509" font-size="14" text-anchor="middle" fill="#333">数据采集</text>
            
            <rect x="170" y="534" width="110" height="40" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="225" y="559" font-size="14" text-anchor="middle" fill="#333">数据分析</text>
            
            <rect x="170" y="584" width="110" height="40" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="225" y="609" font-size="14" text-anchor="middle" fill="#333">神经网络</text>
        </g>
        
        <g>
            <rect x="320" y="444" width="630" height="210" rx="5" ry="5" fill="#D1C4E9" stroke="#ddd" stroke-width="1" />
            <text x="635" y="464" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">核心业务系统</text>
            
            <!-- 故障诊断系统 -->
            <rect x="340" y="484" width="180" height="160" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="430" y="504" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">故障诊断系统</text>
            
            <rect x="350" y="514" width="160" height="30" rx="5" ry="5" fill="#E1F5FE" stroke="#ddd" stroke-width="1" />
            <text x="430" y="534" font-size="12" text-anchor="middle" fill="#333">引擎参数采集</text>
            
            <rect x="350" y="554" width="160" height="30" rx="5" ry="5" fill="#E1F5FE" stroke="#ddd" stroke-width="1" />
            <text x="430" y="574" font-size="12" text-anchor="middle" fill="#333">故障诊断分析</text>
            
            <rect x="350" y="594" width="160" height="30" rx="5" ry="5" fill="#E1F5FE" stroke="#ddd" stroke-width="1" />
            <text x="430" y="614" font-size="12" text-anchor="middle" fill="#333">故障原因推断</text>
            
            <!-- 维修建议系统 -->
            <rect x="540" y="484" width="180" height="160" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="630" y="504" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">维修建议系统</text>
            
            <rect x="550" y="514" width="160" height="30" rx="5" ry="5" fill="#E1F5FE" stroke="#ddd" stroke-width="1" />
            <text x="630" y="534" font-size="12" text-anchor="middle" fill="#333">故障-建议匹配</text>
            
            <rect x="550" y="554" width="160" height="30" rx="5" ry="5" fill="#E1F5FE" stroke="#ddd" stroke-width="1" />
            <text x="630" y="574" font-size="12" text-anchor="middle" fill="#333">修复方案生成</text>
            
            <rect x="550" y="594" width="160" height="30" rx="5" ry="5" fill="#E1F5FE" stroke="#ddd" stroke-width="1" />
            <text x="630" y="614" font-size="12" text-anchor="middle" fill="#333">零配件推荐</text>
            
            <!-- 内容管理系统 -->
            <rect x="740" y="484" width="180" height="160" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="830" y="504" font-size="14" font-weight="bold" text-anchor="middle" fill="#333">内容管理系统</text>
            
            <rect x="750" y="514" width="160" height="30" rx="5" ry="5" fill="#E1F5FE" stroke="#ddd" stroke-width="1" />
            <text x="830" y="534" font-size="12" text-anchor="middle" fill="#333">用户内容管理</text>
            
            <rect x="750" y="554" width="160" height="30" rx="5" ry="5" fill="#E1F5FE" stroke="#ddd" stroke-width="1" />
            <text x="830" y="574" font-size="12" text-anchor="middle" fill="#333">视频教程管理</text>
            
            <rect x="750" y="594" width="160" height="30" rx="5" ry="5" fill="#E1F5FE" stroke="#ddd" stroke-width="1" />
            <text x="830" y="614" font-size="12" text-anchor="middle" fill="#333">标签分类管理</text>
        </g>
        
        <g>
            <rect x="970" y="444" width="200" height="210" rx="5" ry="5" fill="#DCEDC8" stroke="#ddd" stroke-width="1" />
            <text x="1070" y="464" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">系统支持</text>
            
            <rect x="990" y="484" width="160" height="30" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="1070" y="504" font-size="14" text-anchor="middle" fill="#333">用户认证授权</text>
            
            <rect x="990" y="524" width="160" height="30" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="1070" y="544" font-size="14" text-anchor="middle" fill="#333">日志监控</text>
            
            <rect x="990" y="564" width="160" height="30" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="1070" y="584" font-size="14" text-anchor="middle" fill="#333">系统配置</text>
            
            <rect x="990" y="604" width="160" height="30" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="1070" y="624" font-size="14" text-anchor="middle" fill="#333">数据缓存</text>
        </g>
    </g>
    
    <!-- AI层内容 - 新增DeepSeek大模型 -->
    <g>
        <rect x="120" y="680" width="1070" height="60" fill="#f0f0f0" stroke="#ddd" stroke-width="1" />
        
        <g>
            <rect x="150" y="690" width="250" height="40" rx="5" ry="5" fill="#E1BEE7" stroke="#ddd" stroke-width="1" />
            <text x="275" y="715" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">DeepSeek大模型</text>
        </g>
        
        <g>
            <rect x="420" y="690" width="220" height="40" rx="5" ry="5" fill="#E1BEE7" stroke="#ddd" stroke-width="1" />
            <text x="530" y="715" font-size="16" text-anchor="middle" fill="#333">自然语言处理模块</text>
        </g>
        
        <g>
            <rect x="660" y="690" width="220" height="40" rx="5" ry="5" fill="#E1BEE7" stroke="#ddd" stroke-width="1" />
            <text x="770" y="715" font-size="16" text-anchor="middle" fill="#333">知识问答模块</text>
        </g>
        
        <g>
            <rect x="900" y="690" width="250" height="40" rx="5" ry="5" fill="#E1BEE7" stroke="#ddd" stroke-width="1" />
            <text x="1025" y="715" font-size="16" text-anchor="middle" fill="#333">故障原因推理模块</text>
        </g>
    </g>
    
    <!-- 服务层内容 -->
    <g>
        <rect x="120" y="750" width="1070" height="90" fill="#f0f0f0" stroke="#ddd" stroke-width="1" />
        
        <!-- Java服务 -->
        <g>
            <rect x="140" y="760" width="350" height="70" rx="5" ry="5" fill="#FFF9C4" stroke="#ddd" stroke-width="1" />
            <text x="315" y="785" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">Car模块服务</text>
            
        </g>
        
        <!-- 管理后台服务 -->
        <g>
            <rect x="510" y="760" width="350" height="70" rx="5" ry="5" fill="#FFF9C4" stroke="#ddd" stroke-width="1" />
            <text x="685" y="785" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">平台管理端服务</text>
        </g>
        
        <!-- 小程序服务 -->
        <g>
            <rect x="880" y="760" width="290" height="70" rx="5" ry="5" fill="#FFF9C4" stroke="#ddd" stroke-width="1" />
            <text x="1025" y="785" font-size="16" font-weight="bold" text-anchor="middle" fill="#333">移动端服务</text>
        </g>
    </g>
    
    <!-- 基础设施层内容 -->
    <g>
        <rect x="120" y="850" width="1070" height="60" fill="#f0f0f0" stroke="#ddd" stroke-width="1" />
        
        <g>
            <rect x="140" y="860" width="140" height="40" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="210" y="885" font-size="16" text-anchor="middle" fill="#333">MySQL</text>
        </g>
        
        <g>
            <rect x="300" y="860" width="140" height="40" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="370" y="885" font-size="16" text-anchor="middle" fill="#333">Redis</text>
        </g>
        
        <g>
            <rect x="460" y="860" width="140" height="40" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="530" y="885" font-size="16" text-anchor="middle" fill="#333">Nginx</text>
        </g>
        
        <g>
            <rect x="620" y="860" width="140" height="40" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="690" y="885" font-size="16" text-anchor="middle" fill="#333">Maven</text>
        </g>
        
        <g>
            <rect x="780" y="860" width="140" height="40" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="850" y="885" font-size="16" text-anchor="middle" fill="#333">NPM</text>
        </g>
        
        <g>
            <rect x="940" y="860" width="230" height="40" rx="5" ry="5" fill="#fff" stroke="#ddd" stroke-width="1" />
            <text x="1055" y="885" font-size="16" text-anchor="middle" fill="#333">APP开发环境</text>
        </g>
    </g>
    
    <!-- 连接线 - 包括与DeepSeek大模型的连接 -->
    <g stroke="#999" stroke-width="1" stroke-dasharray="5,5">
        <!-- 展示层到前端层 -->
        <line x1="600" y1="130" x2="600" y2="170" />
        
        <!-- 前端层到网关层 -->
        <line x1="600" y1="330" x2="600" y2="364" />
        
        <!-- 网关层到管理层 -->
        <line x1="600" y1="424" x2="600" y2="434" />
        
        <!-- 管理层到AI层 -->
        <line x1="600" y1="664" x2="600" y2="680" />
        
        <!-- AI层到服务层 -->
        <line x1="600" y1="740" x2="600" y2="750" />
        
        <!-- 服务层到基础设施层 -->
        <line x1="600" y1="840" x2="600" y2="850" />
        
        <!-- 核心业务系统到DeepSeek模型的连接 -->
        <line x1="430" y1="644" x2="430" y2="680" />
        <line x1="630" y1="644" x2="630" y2="680" />
        <line x1="830" y1="644" x2="830" y2="680" />
    </g>
    
    <!-- 数据流箭头 -->
    <g fill="none" stroke="#333" stroke-width="3" marker-end="url(#arrowhead)">
        <!-- 小程序前端 -> 小程序API网关 -->
        <path d="M200,310 L200,348 L380,348 L380,374" />
        <path d="M320,310 L320,336 L420,336 L420,374" />

        <!-- Vue前端 -> 管理系统API网关 -->
        <path d="M860,310 L860,348 L720,348 L720,374" />

        <!-- 核心业务 -> 服务层 -->
        <path d="M430,644 L430,745" />
        <path d="M630,644 L630,748" />
        <path d="M830,644 L830,748" />
        
        <!-- 系统支持 -> 服务层 -->
        <path d="M1070,594 L1130,594 L1130,720 L970,720 L970,760" />
        <path d="M1070,624 L1150,624 L1150,735 L1020,735 L1020,760" />

        <!-- 大模型接口调用 -> AI层 -->
        <path d="M120,650 L275,690" />
    </g>

    <!-- DeepSeek模型说明 -->
    <g>
        <rect x="20" y="630" width="200" height="30" rx="5" ry="5" fill="#E1BEE7" stroke="#ddd" stroke-width="1" />
        <text x="120" y="650" font-size="14" text-anchor="middle" fill="#333">大模型接口调用</text>
    </g>
</svg>