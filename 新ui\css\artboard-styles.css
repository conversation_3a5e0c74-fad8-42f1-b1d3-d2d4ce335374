/* 增强版画板样式 - 美化用户界面 */

/* 画板容器优化 */
.artboards-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-8);
    padding: var(--space-8);
    justify-content: center;
    width: 100%;
    box-sizing: border-box;
    background: 
        radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(236, 72, 153, 0.03) 0%, transparent 50%);
}

/* 画板基础样式 */
.artboard {
    width: 100%;
    max-width: 1200px;
    min-height: 80vh;
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    padding: var(--space-8);
    position: relative;
    opacity: 0;
    transform: translateY(20px);
    transition: var(--transition-slow);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
}

.artboard::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--gradient-primary);
    opacity: 0.5;
}

.artboard.active-artboard {
    opacity: 1;
    transform: translateY(0);
}

/* 画板头部 */
.artboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-8);
    padding-bottom: var(--space-6);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.artboard-title {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
}

.artboard-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin: var(--space-2) 0 0 0;
    font-weight: var(--font-weight-regular);
}

/* 卡片系统 */
.card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: var(--space-6);
    transition: var(--transition-all);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-warm);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl), var(--shadow-colored);
}

.card:hover::before {
    opacity: 0.03;
}

.card-header {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.card-icon {
    width: 40px;
    height: 40px;
    padding: var(--space-2);
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: var(--shadow-md);
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.card-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--space-4);
}

/* 按钮系统 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-lg);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: var(--transition-all);
    position: relative;
    overflow: hidden;
    font-size: var(--font-size-md);
    line-height: 1;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), 0 0 20px rgba(99, 102, 241, 0.3);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    transform: translateY(-1px);
}

.btn-outline {
    background: transparent;
    color: var(--color-primary);
    border: 2px solid var(--color-primary);
}

.btn-outline:hover {
    background: var(--color-primary);
    color: white;
    transform: translateY(-1px);
}

/* 网格系统 */
.grid {
    display: grid;
    gap: var(--space-6);
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* 统计卡片 */
.stat-card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-all);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    margin-bottom: var(--space-2);
    background: var(--gradient-primary);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.stat-label {
    font-size: var(--font-size-md);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

/* 进度条 */
.progress {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin: var(--space-2) 0;
}

.progress-bar {
    height: 100%;
    background: var(--gradient-primary);
    transition: width var(--transition-slow);
    border-radius: var(--radius-full);
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

/* 标签和徽章 */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    line-height: 1;
}

.badge-primary {
    background: rgba(99, 102, 241, 0.1);
    color: var(--color-primary);
}

.badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--color-success);
}

.badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--color-warning);
}

.badge-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--color-error);
}

/* 输入框 */
.input {
    width: 100%;
    padding: var(--space-4);
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-md);
    transition: var(--transition-all);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

/* 选择器 */
.select {
    position: relative;
    display: inline-block;
    width: 100%;
}

.select select {
    width: 100%;
    padding: var(--space-4);
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition-all);
    appearance: none;
}

.select::after {
    content: '▼';
    position: absolute;
    right: var(--space-4);
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: var(--text-secondary);
    font-size: 12px;
}

/* 表格 */
.table {
    width: 100%;
    border-collapse: collapse;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.table th,
.table td {
    padding: var(--space-4);
    text-align: left;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table th {
    background: var(--bg-secondary);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.table tbody tr:hover {
    background: var(--bg-secondary);
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-normal);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    max-width: 500px;
    width: 90%;
    box-shadow: var(--shadow-xl);
    transform: scale(0.9);
    transition: var(--transition-elastic);
}

.modal.active .modal-content {
    transform: scale(1);
}

/* 通知 */
.toast {
    position: fixed;
    bottom: var(--space-6);
    right: var(--space-6);
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--space-4) var(--space-6);
    box-shadow: var(--shadow-lg);
    z-index: var(--z-toast);
    transform: translateX(100%);
    transition: var(--transition-elastic);
    border-left: 4px solid var(--color-primary);
}

.toast.show {
    transform: translateX(0);
}

/* 加载状态 */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(99, 102, 241, 0.2);
    border-top: 4px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: var(--space-4) auto;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: var(--space-16) var(--space-8);
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto var(--space-6);
    opacity: 0.3;
}

.empty-state-title {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.empty-state-description {
    color: var(--text-secondary);
    margin-bottom: var(--space-6);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .artboard {
        padding: var(--space-6);
        border-radius: var(--radius-lg);
    }
    
    .artboard-title {
        font-size: var(--font-size-xl);
    }
    
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
    
    .card {
        padding: var(--space-4);
    }
    
    .btn {
        padding: var(--space-3) var(--space-4);
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 480px) {
    .artboards-container {
        padding: var(--space-4);
        gap: var(--space-4);
    }
    
    .artboard {
        padding: var(--space-4);
    }
    
    .stat-number {
        font-size: var(--font-size-xl);
    }
}

/* 打印样式 */
@media print {
    .artboard {
        box-shadow: none;
        border: 1px solid #ccc;
        break-inside: avoid;
    }
    
    .btn {
        display: none;
    }
}
