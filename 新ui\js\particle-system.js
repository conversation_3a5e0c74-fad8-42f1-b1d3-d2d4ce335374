// 高级粒子背景系统 - 优化版本
class ParticleSystem {
    static instances = new Map();
    static isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    constructor(containerId, options = {}) {
        // 防止重复初始化
        if (ParticleSystem.instances.has(containerId)) {
            return ParticleSystem.instances.get(containerId);
        }
        
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            console.warn(`Container with id "${containerId}" not found`);
            return;
        }
        
        this.canvas = null;
        this.ctx = null;
        this.particles = [];
        this.mouse = { x: 0, y: 0 };
        this.animationId = null;
        this.isVisible = true;
        this.rafId = null;
        this.frameCount = 0;
        this.lastTime = 0;
        this.fps = 60;
        this.frameInterval = 1000 / this.fps;
        
        // 性能监控
        this.performanceMetrics = {
            frameTime: 0,
            particleCount: 0,
            averageFrameTime: 0
        };
        
        this.options = {
            particleCount: options.particleCount || (ParticleSystem.isReducedMotion ? 20 : 50),
            connectionDistance: options.connectionDistance || 150,
            particleSpeed: options.particleSpeed || (ParticleSystem.isReducedMotion ? 0.2 : 0.5),
            particleSize: options.particleSize || 2,
            lineOpacity: options.lineOpacity || 0.1,
            particleOpacity: options.particleOpacity || 0.6,
            interactive: options.interactive !== false && !ParticleSystem.isReducedMotion,
            color: options.color || '#6366f1',
            backgroundColor: options.backgroundColor || 'transparent',
            maxFPS: options.maxFPS || 60,
            enablePerformanceMode: options.enablePerformanceMode !== false
        };
        
        // 注册实例
        ParticleSystem.instances.set(containerId, this);
        
        this.init();
    }
      init() {
        try {
            this.createCanvas();
            this.createParticles();
            this.bindEvents();
            this.setupIntersectionObserver();
            this.animate();
        } catch (error) {
            console.error('ParticleSystem initialization failed:', error);
        }
    }
    
    createCanvas() {
        this.canvas = document.createElement('canvas');
        this.canvas.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: -1;
            will-change: auto;
        `;
        this.canvas.className = 'particle-canvas';
        
        this.container.style.position = 'relative';
        this.container.appendChild(this.canvas);
        
        this.ctx = this.canvas.getContext('2d', { alpha: true, desynchronized: true });
        
        // 设置高DPI支持
        this.setupHighDPI();
        this.resizeCanvas();
    }
    
    setupHighDPI() {
        const dpr = window.devicePixelRatio || 1;
        this.canvas.style.imageRendering = 'auto';
        this.dpr = dpr;
    }
      resizeCanvas() {
        const rect = this.container.getBoundingClientRect();
        const dpr = this.dpr || 1;
        
        // 设置实际尺寸
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        
        // 设置CSS尺寸
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        // 缩放上下文以匹配设备像素比
        this.ctx.scale(dpr, dpr);
        
        // 重新创建粒子以适应新尺寸
        if (this.particles.length > 0) {
            this.createParticles();
        }
    }
    
    setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    this.isVisible = entry.isIntersecting;
                    if (!this.isVisible && this.options.enablePerformanceMode) {
                        // 当不可见时暂停动画
                        if (this.rafId) {
                            cancelAnimationFrame(this.rafId);
                            this.rafId = null;
                        }
                    } else if (this.isVisible && !this.rafId) {
                        // 当重新可见时恢复动画
                        this.animate();
                    }
                });
            }, {
                threshold: 0.1
            });
            
            this.observer.observe(this.container);
        }
    }
      createParticles() {
        this.particles = [];
        const rect = this.container.getBoundingClientRect();
        
        for (let i = 0; i < this.options.particleCount; i++) {
            this.particles.push({
                x: Math.random() * rect.width,
                y: Math.random() * rect.height,
                vx: (Math.random() - 0.5) * this.options.particleSpeed,
                vy: (Math.random() - 0.5) * this.options.particleSpeed,
                size: Math.random() * this.options.particleSize + 1,
                life: 1.0,
                maxLife: 1.0
            });
        }
        
        this.performanceMetrics.particleCount = this.particles.length;
    }
    
    bindEvents() {
        // 鼠标事件 - 使用防抖优化
        if (this.options.interactive) {
            let mouseMoveTimeout;
            this.container.addEventListener('mousemove', (e) => {
                clearTimeout(mouseMoveTimeout);
                mouseMoveTimeout = setTimeout(() => {
                    const rect = this.container.getBoundingClientRect();
                    this.mouse.x = e.clientX - rect.left;
                    this.mouse.y = e.clientY - rect.top;
                }, 16); // ~60fps
            }, { passive: true });
            
            this.container.addEventListener('mouseleave', () => {
                this.mouse.x = -1000;
                this.mouse.y = -1000;
            }, { passive: true });
        }
        
        // 窗口大小变化 - 使用防抖
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.resizeCanvas();
            }, 250);
        }, { passive: true });
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseAnimation();
            } else {
                this.resumeAnimation();
            }
        });
    }
      updateParticles() {
        const rect = this.container.getBoundingClientRect();
        
        this.particles.forEach(particle => {
            // 更新位置
            particle.x += particle.vx;
            particle.y += particle.vy;
            
            // 边界检测 - 优化性能
            if (particle.x < 0 || particle.x > rect.width) {
                particle.vx *= -1;
                particle.x = Math.max(0, Math.min(rect.width, particle.x));
            }
            if (particle.y < 0 || particle.y > rect.height) {
                particle.vy *= -1;
                particle.y = Math.max(0, Math.min(rect.height, particle.y));
            }
            
            // 鼠标交互 - 只在可见且启用交互时计算
            if (this.options.interactive && this.isVisible && this.mouse.x > -500) {
                const dx = this.mouse.x - particle.x;
                const dy = this.mouse.y - particle.y;
                const distanceSquared = dx * dx + dy * dy;
                
                if (distanceSquared < 10000) { // 100^2 优化：避免开方运算
                    const force = 100 / distanceSquared;
                    particle.x -= dx * force * 0.001;
                    particle.y -= dy * force * 0.001;
                }
            }
        });
    }
    
    drawParticles() {
        const rect = this.container.getBoundingClientRect();
        
        // 清空画布
        this.ctx.clearRect(0, 0, rect.width, rect.height);
        
        // 设置全局混合模式以提升性能
        this.ctx.globalCompositeOperation = 'source-over';
        
        // 绘制粒子
        this.ctx.fillStyle = this.hexToRgba(this.options.color, this.options.particleOpacity);
        this.particles.forEach(particle => {
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
        });
        
        // 绘制连接线 - 使用优化算法
        this.drawConnections();
    }
    
    drawConnections() {
        const connectionDistanceSquared = this.options.connectionDistance * this.options.connectionDistance;
        
        for (let i = 0; i < this.particles.length; i++) {
            for (let j = i + 1; j < this.particles.length; j++) {
                const dx = this.particles[i].x - this.particles[j].x;
                const dy = this.particles[i].y - this.particles[j].y;
                const distanceSquared = dx * dx + dy * dy;
                
                if (distanceSquared < connectionDistanceSquared) {
                    const distance = Math.sqrt(distanceSquared);
                    const opacity = this.options.lineOpacity * (1 - distance / this.options.connectionDistance);
                    
                    this.ctx.beginPath();
                    this.ctx.moveTo(this.particles[i].x, this.particles[i].y);
                    this.ctx.lineTo(this.particles[j].x, this.particles[j].y);
                    this.ctx.strokeStyle = this.hexToRgba(this.options.color, opacity);
                    this.ctx.lineWidth = 1;
                    this.ctx.stroke();
                }
            }
        }
    }
      animate(currentTime = 0) {
        if (!this.isVisible && this.options.enablePerformanceMode) {
            return;
        }
        
        // 帧率控制
        const deltaTime = currentTime - this.lastTime;
        
        if (deltaTime >= this.frameInterval) {
            const frameStart = performance.now();
            
            this.updateParticles();
            this.drawParticles();
            
            // 性能监控
            this.performanceMetrics.frameTime = performance.now() - frameStart;
            this.performanceMetrics.averageFrameTime = 
                (this.performanceMetrics.averageFrameTime * 0.9) + 
                (this.performanceMetrics.frameTime * 0.1);
                
            // 自适应性能调整
            if (this.performanceMetrics.averageFrameTime > 16.67) { // 如果帧时间超过60fps
                this.adaptPerformance();
            }
            
            this.lastTime = currentTime - (deltaTime % this.frameInterval);
            this.frameCount++;
        }
        
        this.rafId = requestAnimationFrame((time) => this.animate(time));
    }
    
    adaptPerformance() {
        // 自适应性能优化
        if (this.particles.length > 20 && this.frameCount % 60 === 0) {
            // 每秒检查一次，如果性能不佳则减少粒子数量
            const targetCount = Math.max(20, Math.floor(this.particles.length * 0.8));
            this.particles = this.particles.slice(0, targetCount);
            this.performanceMetrics.particleCount = this.particles.length;
        }
    }
    
    pauseAnimation() {
        if (this.rafId) {
            cancelAnimationFrame(this.rafId);
            this.rafId = null;
        }
    }
    
    resumeAnimation() {
        if (!this.rafId && this.isVisible) {
            this.animate();
        }
    }
      hexToRgba(hex, alpha) {
        // 缓存颜色转换结果
        if (!this.colorCache) {
            this.colorCache = new Map();
        }
        
        const key = `${hex}-${alpha}`;
        if (this.colorCache.has(key)) {
            return this.colorCache.get(key);
        }
        
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        const result = `rgba(${r}, ${g}, ${b}, ${alpha})`;
        
        this.colorCache.set(key, result);
        return result;
    }
    
    destroy() {
        try {
            // 取消动画
            if (this.rafId) {
                cancelAnimationFrame(this.rafId);
                this.rafId = null;
            }
            
            // 移除观察器
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
            
            // 移除DOM元素
            if (this.canvas && this.canvas.parentNode) {
                this.canvas.parentNode.removeChild(this.canvas);
            }
            
            // 清理实例
            ParticleSystem.instances.delete(this.containerId);
            
            // 清理缓存
            if (this.colorCache) {
                this.colorCache.clear();
            }
            
            // 清理引用
            this.particles = null;
            this.canvas = null;
            this.ctx = null;
            this.container = null;
        } catch (error) {
            console.error('ParticleSystem destruction failed:', error);
        }
    }
    
    updateOptions(newOptions) {
        Object.assign(this.options, newOptions);
        this.createParticles();
    }
    
    getPerformanceMetrics() {
        return { ...this.performanceMetrics };
    }
    
    // 静态方法：销毁所有实例
    static destroyAll() {
        ParticleSystem.instances.forEach(instance => {
            instance.destroy();
        });
        ParticleSystem.instances.clear();
    }
    
    // 静态方法：获取实例
    static getInstance(containerId) {
        return ParticleSystem.instances.get(containerId);
    }
}

// 流星效果 - 优化版本
class MeteorShower {
    static instance = null;
    
    constructor(containerId) {
        // 单例模式
        if (MeteorShower.instance) {
            return MeteorShower.instance;
        }
        
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.meteors = [];
        this.canvas = null;
        this.ctx = null;
        this.rafId = null;
        this.isActive = !ParticleSystem.isReducedMotion;
        this.lastMeteorTime = 0;
        this.meteorInterval = 2000; // 2秒间隔
        
        // 性能优化配置
        this.maxMeteors = ParticleSystem.isReducedMotion ? 3 : 8;
        this.spawnRate = ParticleSystem.isReducedMotion ? 0.005 : 0.01;
        
        MeteorShower.instance = this;
        
        if (this.isActive) {
            this.init();
        }
    }
    
    init() {
        try {
            this.createCanvas();
            this.setupVisibilityObserver();
            this.animate();
        } catch (error) {
            console.error('MeteorShower initialization failed:', error);
        }
    }
      createCanvas() {
        this.canvas = document.createElement('canvas');
        this.canvas.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            will-change: auto;
        `;
        this.canvas.className = 'meteor-canvas';
        
        document.body.appendChild(this.canvas);
        
        this.ctx = this.canvas.getContext('2d', { alpha: true, desynchronized: true });
        this.resizeCanvas();
        
        // 响应式处理
        window.addEventListener('resize', this.debounce(() => {
            this.resizeCanvas();
        }, 250), { passive: true });
    }
    
    resizeCanvas() {
        const dpr = window.devicePixelRatio || 1;
        this.canvas.width = window.innerWidth * dpr;
        this.canvas.height = window.innerHeight * dpr;
        this.canvas.style.width = window.innerWidth + 'px';
        this.canvas.style.height = window.innerHeight + 'px';
        this.ctx.scale(dpr, dpr);
    }
    
    setupVisibilityObserver() {
        // 页面可见性检测
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pause();
            } else {
                this.resume();
            }
        });
    }
    
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
      createMeteor() {
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        
        return {
            x: Math.random() * screenWidth + 200,
            y: -50 - Math.random() * 100,
            length: Math.random() * 80 + 40,
            speed: Math.random() * 3 + 2,
            opacity: Math.random() * 0.6 + 0.3,
            angle: Math.PI / 4 + (Math.random() - 0.5) * 0.2,
            trail: [],
            maxTrailLength: 8,
            life: 1.0
        };
    }
    
    updateMeteors(currentTime) {
        // 控制流星生成频率
        if (currentTime - this.lastMeteorTime > this.meteorInterval && 
            this.meteors.length < this.maxMeteors && 
            Math.random() < this.spawnRate) {
            this.meteors.push(this.createMeteor());
            this.lastMeteorTime = currentTime;
        }
        
        // 更新现有流星
        this.meteors = this.meteors.filter(meteor => {
            // 更新位置
            const prevX = meteor.x;
            const prevY = meteor.y;
            
            meteor.x -= meteor.speed * Math.cos(meteor.angle);
            meteor.y += meteor.speed * Math.sin(meteor.angle);
            
            // 添加轨迹点
            meteor.trail.push({ x: prevX, y: prevY, opacity: meteor.opacity });
            if (meteor.trail.length > meteor.maxTrailLength) {
                meteor.trail.shift();
            }
            
            // 更新生命值
            meteor.life -= 0.008;
            meteor.opacity = Math.max(0, meteor.life * 0.8);
            
            // 检查是否应该移除
            return meteor.x > -300 && 
                   meteor.y < window.innerHeight + 200 && 
                   meteor.life > 0;
        });
    }
      drawMeteors() {
        const screenWidth = window.innerWidth;
        const screenHeight = window.innerHeight;
        
        this.ctx.clearRect(0, 0, screenWidth, screenHeight);
        
        this.meteors.forEach(meteor => {
            // 绘制轨迹
            if (meteor.trail.length > 1) {
                meteor.trail.forEach((point, index) => {
                    const trailOpacity = (point.opacity || meteor.opacity) * (index / meteor.trail.length) * 0.3;
                    if (trailOpacity > 0.01) {
                        this.ctx.beginPath();
                        this.ctx.arc(point.x, point.y, 1, 0, Math.PI * 2);
                        this.ctx.fillStyle = `rgba(255, 255, 255, ${trailOpacity})`;
                        this.ctx.fill();
                    }
                });
            }
            
            // 绘制主流星体
            if (meteor.opacity > 0.01) {
                const gradient = this.ctx.createLinearGradient(
                    meteor.x, meteor.y,
                    meteor.x + meteor.length * Math.cos(meteor.angle),
                    meteor.y - meteor.length * Math.sin(meteor.angle)
                );
                
                gradient.addColorStop(0, `rgba(255, 255, 255, ${meteor.opacity})`);
                gradient.addColorStop(0.3, `rgba(135, 206, 235, ${meteor.opacity * 0.8})`);
                gradient.addColorStop(0.7, `rgba(99, 102, 241, ${meteor.opacity * 0.6})`);
                gradient.addColorStop(1, 'rgba(99, 102, 241, 0)');
                
                this.ctx.beginPath();
                this.ctx.moveTo(meteor.x, meteor.y);
                this.ctx.lineTo(
                    meteor.x + meteor.length * Math.cos(meteor.angle),
                    meteor.y - meteor.length * Math.sin(meteor.angle)
                );
                this.ctx.strokeStyle = gradient;
                this.ctx.lineWidth = 2 + meteor.speed * 0.2;
                this.ctx.lineCap = 'round';
                this.ctx.stroke();
            }
        });
    }
    
    animate(currentTime = 0) {
        if (!this.isActive || document.hidden) {
            return;
        }
        
        this.updateMeteors(currentTime);
        this.drawMeteors();
        this.rafId = requestAnimationFrame((time) => this.animate(time));
    }
    
    pause() {
        if (this.rafId) {
            cancelAnimationFrame(this.rafId);
            this.rafId = null;
        }
    }
    
    resume() {
        if (!this.rafId && this.isActive) {
            this.animate();
        }
    }
      destroy() {
        try {
            if (this.rafId) {
                cancelAnimationFrame(this.rafId);
                this.rafId = null;
            }
            
            if (this.canvas && this.canvas.parentNode) {
                this.canvas.parentNode.removeChild(this.canvas);
            }
            
            // 清理实例
            MeteorShower.instance = null;
            
            // 清理数据
            this.meteors = null;
            this.canvas = null;
            this.ctx = null;
        } catch (error) {
            console.error('MeteorShower destruction failed:', error);
        }
    }
    
    // 静态方法
    static getInstance() {
        return MeteorShower.instance;
    }
    
    static destroyInstance() {
        if (MeteorShower.instance) {
            MeteorShower.instance.destroy();
        }
    }
}

// 工具函数：性能监控和优化
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            fps: 0,
            frameTime: 0,
            memoryUsage: 0,
            particleCount: 0
        };
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fpsArray = [];
    }
    
    update() {
        this.frameCount++;
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastTime;
        
        if (deltaTime >= 1000) {
            this.metrics.fps = Math.round((this.frameCount * 1000) / deltaTime);
            this.fpsArray.push(this.metrics.fps);
            
            // 只保留最近10个FPS值
            if (this.fpsArray.length > 10) {
                this.fpsArray.shift();
            }
            
            this.frameCount = 0;
            this.lastTime = currentTime;
            
            // 内存使用情况
            if (performance.memory) {
                this.metrics.memoryUsage = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
            }
        }
    }
    
    getAverageFPS() {
        if (this.fpsArray.length === 0) return 60;
        return Math.round(this.fpsArray.reduce((a, b) => a + b, 0) / this.fpsArray.length);
    }
    
    shouldReduceQuality() {
        return this.getAverageFPS() < 30 || this.metrics.memoryUsage > 100;
    }
    
    getRecommendedSettings() {
        const avgFPS = this.getAverageFPS();
        
        if (avgFPS >= 50) {
            return { particleCount: 50, quality: 'high' };
        } else if (avgFPS >= 30) {
            return { particleCount: 30, quality: 'medium' };
        } else {
            return { particleCount: 15, quality: 'low' };
        }
    }
}

// 导出类和工具
if (typeof window !== 'undefined') {
    window.ParticleSystem = ParticleSystem;
    window.MeteorShower = MeteorShower;
    window.PerformanceMonitor = PerformanceMonitor;
}
