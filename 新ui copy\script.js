document.addEventListener('DOMContentLoaded', () => {
    const islandContainer = document.getElementById('dynamic-island-container');
    const islands = document.querySelectorAll('.island');
    const searchIsland = document.getElementById('search-island');
    const searchInput = document.getElementById('search-input');
    const searchTriggerIcon = searchIsland.querySelector('.search-trigger-icon');
    const userIsland = document.getElementById('user-island');
    const notificationIsland = document.getElementById('notification-island');
    const notificationDot = notificationIsland.querySelector('.notification-dot');

    const logoIsland = document.getElementById('logo-island');
    const navInterview = document.getElementById('nav-interview');
    const navCoach = document.getElementById('nav-coach');
    const navCareer = document.getElementById('nav-career');
    const navCommunity = document.getElementById('nav-community');
    // Assume other nav islands might be added, e.g., navSkills, navProfile
    // const navSkills = document.getElementById('nav-skills'); 
    // const navProfile = document.getElementById('nav-profile');


    // --- Artboard and Context Management ---
    const artboards = {
        dashboard: document.getElementById('dashboard-artboard'),
        interview: document.getElementById('mock-interview-artboard'),
        coach: document.getElementById('ai-coach-artboard'),
        skills: document.getElementById('skill-practice-artboard'),
        profile: document.getElementById('profile-settings-artboard'),
        career: document.getElementById('career-dev-artboard'), // 职业发展/成长导航
        community: document.getElementById('community-artboard') // 面试伙伴社群
        // Add other artboards here if they exist
    };

    function switchContextAndArtboard(newContext) {
        // Remove all context-specific classes from body
        document.body.className = ''; // Clear all classes first
        document.body.classList.add(`context-${newContext}`);

        // Hide all artboards
        for (const key in artboards) {
            if (artboards[key]) {
                artboards[key].classList.remove('active-artboard');
            }
        }

        // Show the target artboard
        if (artboards[newContext]) {
            artboards[newContext].classList.add('active-artboard');
            // 切换后初始化手风琴
            setTimeout(initArtboardAccordions, 0);
            // 切换后初始化tab切换
            setTimeout(initArtboardTabs, 0);
            // 切换到AI教练artboard时初始化聊天区
            if (newContext === 'coach') {
                setTimeout(initAICoachChatArea, 0);
            }
            // 切换到技能练习artboard时初始化技能练习区
            if (newContext === 'skills') {
                setTimeout(initSkillPracticeArea, 0);
            }
        } else {
            console.warn(`Artboard for context "${newContext}" not found.`);
            if (artboards.dashboard) artboards.dashboard.classList.add('active-artboard'); // Fallback to dashboard
        }
        
        console.log(`Switched to context: ${newContext}`);
        
        // Close any open island menus (search, user profile) when switching context
        toggleSearch(false);
        if (userIsland.classList.contains('menu-active')) {
            userIsland.classList.remove('menu-active');
        }
    }

    // Initial context setup
    // Determine initial context (e.g., from URL hash, or default to dashboard)
    let initialContext = 'dashboard'; 
    // Example: if (window.location.hash === '#interview') initialContext = 'interview';
    switchContextAndArtboard(initialContext);


    // --- Navigation Island Event Listeners ---
    if (logoIsland) { // Assuming logo leads to dashboard
        logoIsland.addEventListener('click', () => switchContextAndArtboard('dashboard'));
    }
    if (navInterview) {
        navInterview.addEventListener('click', () => switchContextAndArtboard('interview'));
    }
    if (navCoach) {
        navCoach.addEventListener('click', () => switchContextAndArtboard('coach'));
    }
    if (navCareer) {
        navCareer.addEventListener('click', () => switchContextAndArtboard('career'));
    }
    if (navCommunity) {
        navCommunity.addEventListener('click', () => switchContextAndArtboard('community'));
    }
    // Add listeners for other nav islands if they are implemented in HTML
    // e.g., if (navSkills) navSkills.addEventListener('click', () => switchContextAndArtboard('skills'));
    // e.g., if (navProfile) userIsland.addEventListener('click', () => switchContextAndArtboard('profile')); 
    // Note: userIsland currently opens a menu, if it should also switch to profile artboard, logic needs merging or a dedicated profile button.


    // --- Search Interaction (existing code) ---
    searchTriggerIcon.addEventListener('click', (e) => {
        e.stopPropagation();
        toggleSearch();
    });
    searchInput.addEventListener('click', (e) => {
        e.stopPropagation();
    });
    searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            toggleSearch(false);
        }
    });

    function toggleSearch(forceOpen = null) {
        const isActive = searchIsland.classList.contains('active');
        if (forceOpen === true || (!isActive && forceOpen === null) ) {
            searchIsland.classList.add('active');
            searchInput.focus();
            if (userIsland.classList.contains('menu-active')) {
                 userIsland.classList.remove('menu-active');
            }
        } else if (forceOpen === false || (isActive && forceOpen === null)) {
            searchIsland.classList.remove('active');
            searchInput.blur();
        }
    }

    // --- User Menu Interaction (existing code) ---
    userIsland.addEventListener('click', (e) => {
        e.stopPropagation();
        userIsland.classList.toggle('menu-active');
        if (userIsland.classList.contains('menu-active')) {
            toggleSearch(false); // Close search if opening user menu
        }
        // If clicking user island should also navigate to profile artboard:
        // switchContextAndArtboard('profile'); // This would override menu toggle, needs careful thought.
        // For now, user island click only opens its own menu. A menu item could trigger profile artboard.
    });
    
    // Example: User menu items could also switch contexts/artboards
    const userMenuLinks = userIsland.querySelectorAll('.user-menu a');
    userMenuLinks.forEach(link => {
        if (link.textContent.includes('个人中心')) { // Or use an ID/data-attribute
            link.addEventListener('click', (e) => {
                e.preventDefault();
                switchContextAndArtboard('profile');
                userIsland.classList.remove('menu-active'); // Close menu
            });
        }
        // Add other menu item handlers if needed
    });


    // --- Click outside to close active elements (Search, User Menu) (existing code) ---
    document.addEventListener('click', (e) => {
        if (searchIsland && !searchIsland.contains(e.target) && searchIsland.classList.contains('active')) {
            toggleSearch(false);
        }
        if (userIsland && !userIsland.contains(e.target) && userIsland.classList.contains('menu-active')) {
            userIsland.classList.remove('menu-active');
        }
    });

    // --- Notification Demo (existing code, can be kept or enhanced) ---
    const triggerNotificationBtn = document.getElementById('trigger-notification'); // This button was removed from HTML
    if (triggerNotificationBtn) {
        triggerNotificationBtn.addEventListener('click', () => {
            notificationDot.classList.toggle('visible');
        });
    }
    if (notificationIsland) {
        notificationIsland.addEventListener('click', () => {
            // alert('通知中心被点击！实际应用中会显示通知列表。'); // Replace alert
            // Implement actual notification panel display here
            notificationDot.classList.remove('visible'); 
        });
    }

    console.log('Dynamic Island UI with Artboard Navigation initialized.');

    // Placeholder for Artboard-specific JavaScript:
    // The extensive script from part1.html (for accordions, chat, skill practice etc.)
    // needs to be integrated here. This might involve:
    // 1. Copying the entire script block here.
    // 2. Ensuring its event listeners are correctly scoped or re-initialized if artboards are dynamically shown/hidden.
    // 3. Resolving any conflicts with existing DOMContentLoaded or global listeners.
    // Example:
    // function initArtboardSpecificScripts() {
    //     // Code from part1.html script block
    //     // e.g., setupAccordion(); setupChat(); etc.
    // }
    // initArtboardSpecificScripts();
    // If artboards are frequently switched, some scripts might need re-initialization upon artboard activation.

    // --- 手风琴交互逻辑迁移自part1.html ---
    function setupAccordion(containerSelector, headerSelector, contentSelector, openClass, iconSelector, iconOpenHref, iconClosedHref) {
        const accordions = document.querySelectorAll(containerSelector);
        accordions.forEach(accordion => {
            const header = accordion.querySelector(headerSelector);
            const content = accordion.querySelector(contentSelector);
            const iconUse = iconSelector && header ? header.querySelector(iconSelector + ' use') : null;

            // 防止重复绑定
            if (header && !header.dataset.accordionBound) {
                header.addEventListener('click', () => {
                    const isOpen = content.style.display === 'block';
                    content.style.display = isOpen ? 'none' : 'block';
                    if (accordion.classList.contains(openClass)) {
                        accordion.classList.remove(openClass);
                    } else {
                        accordion.classList.add(openClass);
                    }
                    if (iconUse && iconOpenHref && iconClosedHref) {
                        iconUse.setAttribute('xlink:href', isOpen ? iconClosedHref : iconOpenHref);
                    }
                });
                header.dataset.accordionBound = 'true';
            }
        });
    }

    // --- 在artboard切换后初始化手风琴 ---
    function initArtboardAccordions() {
        // 技能分类手风琴
        setupAccordion('.skill-category-item', '.category-header', '.skill-point-list', 'active', '.chevron-icon', '#icon-chevron-down', '#icon-chevron-right');
        // 默认展开第一个技能分类的子列表
        const firstActiveSkillCategory = document.querySelector('.skill-category-item.active .skill-point-list');
        if (firstActiveSkillCategory) {
            firstActiveSkillCategory.style.display = 'block';
            const firstActiveChevron = document.querySelector('.skill-category-item.active .category-header .chevron-icon use');
            if(firstActiveChevron) firstActiveChevron.setAttribute('xlink:href', '#icon-chevron-down');
        }
        // FAQ手风琴
        setupAccordion('.faq-item', 'h4', 'p', 'open', '.chevron-icon', '#icon-chevron-down', '#icon-chevron-right');
    }

    // --- Tab切换交互逻辑迁移自part1.html ---
    function setupTabs(navItemSelector, contentSectionSelector, activeClass) {
        const navItems = document.querySelectorAll(navItemSelector);
        const contentSections = document.querySelectorAll(contentSectionSelector);

        navItems.forEach(item => {
            // 防止重复绑定
            if (!item.dataset.tabBound) {
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    // Deactivate all nav items and content sections
                    navItems.forEach(i => i.classList.remove(activeClass));
                    contentSections.forEach(s => {
                        if (s.id !== item.getAttribute('href')?.substring(1)) {
                            s.style.display = 'none';
                            s.classList.remove(activeClass);
                        }
                    });
                    // Activate clicked nav item
                    item.classList.add(activeClass);
                    // Activate target content section
                    const targetId = item.getAttribute('href')?.substring(1);
                    if (targetId) {
                        const targetContent = document.getElementById(targetId);
                        if (targetContent) {
                            targetContent.style.display = 'block';
                            targetContent.classList.add(activeClass);
                        }
                    }
                });
                item.dataset.tabBound = 'true';
            }
        });

        // Default active tab (first one或有activeClass的)
        let defaultActiveItem = document.querySelector(navItemSelector + '.' + activeClass);
        if (!defaultActiveItem && navItems.length > 0) {
            defaultActiveItem = navItems[0];
            defaultActiveItem.classList.add(activeClass);
        }
        if (defaultActiveItem) {
            contentSections.forEach(s => s.style.display = 'none');
            const defaultTargetId = defaultActiveItem.getAttribute('href')?.substring(1);
            if (defaultTargetId) {
                const defaultContent = document.getElementById(defaultTargetId);
                if (defaultContent) {
                    defaultContent.style.display = 'block';
                    defaultContent.classList.add(activeClass);
                }
            }
        }
    }

    // --- 在artboard切换后初始化tab切换 ---
    function initArtboardTabs() {
        // 个人中心tab
        setupTabs('.profile-tab-nav .tab-nav-item', '.profile-tab-content', 'active');
        // 成就/等级tab
        setupTabs('.achievement-tab-nav .tab-nav-item', '.achievement-tab-content', 'active');
        // 帮助中心tab
        setupTabs('.help-nav-item', '.help-content-area section', 'active');
        // 可根据实际artboard结构补充更多tab切换
    }

    // --- AI教练聊天区自动滚动与输入框自适应 ---
    function initAICoachChatArea() {
        const messageListCoach = document.getElementById('message-list-coach');
        if (messageListCoach) {
            // 自动滚动到底部
            messageListCoach.scrollTop = messageListCoach.scrollHeight;
        }
        const coachMessageInput = document.getElementById('coach-message-input');
        const coachSendButton = document.getElementById('coach-send-btn');
        if (coachMessageInput) {
            // 防止重复绑定
            if (!coachMessageInput.dataset.resizeBound) {
                coachMessageInput.addEventListener('input', () => {
                    coachMessageInput.style.height = 'auto';
                    coachMessageInput.style.height = coachMessageInput.scrollHeight + 'px';
                });
                coachMessageInput.dataset.resizeBound = 'true';
            }
            // 回车发送消息（如有发送逻辑可补充sendCoachMessage函数）
            if (!coachMessageInput.dataset.enterBound) {
                coachMessageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        if (coachSendButton) coachSendButton.click();
                        // 如无按钮可在此直接调用sendCoachMessage();
                    }
                });
                coachMessageInput.dataset.enterBound = 'true';
            }
            // 初始高度自适应
            coachMessageInput.style.height = 'auto';
            coachMessageInput.style.height = coachMessageInput.scrollHeight + 'px';
        }
    }

    // --- 技能练习区交互迁移自part1.html ---
    function initSkillPracticeArea() {
        const skillPracticeContainer = document.getElementById('skill-practice-artboard');
        if (!skillPracticeContainer) return;

        // 技能点点击切换
        const skillPoints = skillPracticeContainer.querySelectorAll('.skill-point-item');
        const skillDetailPanels = skillPracticeContainer.querySelectorAll('.skill-detail-panel');
        skillPoints.forEach(item => {
            if (!item.dataset.skillBound) {
                item.addEventListener('click', () => {
                    // 切换当前技能高亮
                    skillPoints.forEach(i => i.classList.remove('current-skill'));
                    item.classList.add('current-skill');
                    // 切换技能详情面板
                    const targetId = item.getAttribute('data-target');
                    skillDetailPanels.forEach(panel => {
                        if (panel.id === targetId) {
                            panel.style.display = 'block';
                        } else {
                            panel.style.display = 'none';
                        }
                    });
                });
                item.dataset.skillBound = 'true';
            }
        });
        // 初始高亮技能
        const initialSkill = skillPracticeContainer.querySelector('.skill-point-item.current-skill');
        if (initialSkill) {
            initialSkill.click();
        }
        // 按钮操作（查看提示、提交反馈、下一题等）
        const viewHintButton = skillPracticeContainer.querySelector('.practice-controls-bar .btn-outline');
        const submitFeedbackButton = skillPracticeContainer.querySelector('.practice-controls-bar .btn-secondary');
        const nextPracticeButton = skillPracticeContainer.querySelector('.practice-controls-bar .btn-primary');
        if (viewHintButton && !viewHintButton.dataset.bound) {
            viewHintButton.addEventListener('click', () => {
                // 显示提示内容（可根据实际结构补充）
                const hintPanel = skillPracticeContainer.querySelector('.practice-hint-panel');
                if (hintPanel) hintPanel.style.display = 'block';
            });
            viewHintButton.dataset.bound = 'true';
        }
        if (submitFeedbackButton && !submitFeedbackButton.dataset.bound) {
            submitFeedbackButton.addEventListener('click', () => {
                // 提交反馈逻辑（可根据实际需求补充）
                alert('反馈已提交！');
            });
            submitFeedbackButton.dataset.bound = 'true';
        }
        if (nextPracticeButton && !nextPracticeButton.dataset.bound) {
            nextPracticeButton.addEventListener('click', () => {
                // 跳转到下一个技能点（可根据实际结构补充）
                let current = skillPracticeContainer.querySelector('.skill-point-item.current-skill');
                if (current) {
                    let next = current.nextElementSibling;
                    while (next && !next.classList.contains('skill-point-item')) {
                        next = next.nextElementSibling;
                    }
                    if (next) next.click();
                }
            });
            nextPracticeButton.dataset.bound = 'true';
        }
    }

    // 可在此处为career和community添加导航按钮监听，例如：
    // const navCareer = document.getElementById('nav-career');
    // if (navCareer) navCareer.addEventListener('click', () => switchContextAndArtboard('career'));
    // const navCommunity = document.getElementById('nav-community');
    // if (navCommunity) navCommunity.addEventListener('click', () => switchContextAndArtboard('community'));

}); 