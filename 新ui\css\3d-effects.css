/* 3D转换和深度效果样式 */

/* 3D空间设置 */
.perspective-container {
    perspective: 1000px;
    perspective-origin: center center;
}

.transform-3d {
    transform-style: preserve-3d;
    transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1);
}

/* 深度层级系统 */
.depth-1 { transform: translateZ(10px); }
.depth-2 { transform: translateZ(20px); }
.depth-3 { transform: translateZ(30px); }
.depth-4 { transform: translateZ(40px); }
.depth-5 { transform: translateZ(50px); }

/* 3D卡片效果 */
.card-3d {
    position: relative;
    transform-style: preserve-3d;
    transition: all 0.6s cubic-bezier(0.23, 1, 0.32, 1);
    cursor: pointer;
}

.card-3d:hover {
    transform: rotateY(5deg) rotateX(5deg) translateZ(20px);
}

.card-3d::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    border-radius: inherit;
    transform: translateZ(-1px);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card-3d:hover::before {
    opacity: 1;
}

/* 翻转卡片效果 */
.flip-card {
    background-color: transparent;
    perspective: 1000px;
    width: 100%;
    height: 200px;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.8s;
    transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.flip-card-front {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.flip-card-back {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    transform: rotateY(180deg);
}

/* 立体按钮效果 */
.btn-3d {
    position: relative;
    background: linear-gradient(145deg, #667eea, #764ba2);
    border: none;
    border-radius: 12px;
    color: white;
    cursor: pointer;
    font-weight: 600;
    padding: 12px 24px;
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    transform-style: preserve-3d;
    box-shadow: 
        0 4px 8px rgba(102, 126, 234, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.btn-3d::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(145deg, #764ba2, #667eea);
    border-radius: inherit;
    transform: translateZ(-6px);
    transition: transform 0.3s ease;
}

.btn-3d:hover {
    transform: translateY(-2px) translateZ(4px);
    box-shadow: 
        0 8px 25px rgba(102, 126, 234, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset;
}

.btn-3d:active {
    transform: translateY(0) translateZ(0);
    box-shadow: 
        0 2px 5px rgba(102, 126, 234, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
}

.btn-3d:hover::before {
    transform: translateZ(-8px);
}

/* 浮动动画效果 */
.floating {
    animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% {
        transform: translateY(0px) rotateX(0deg);
    }
    50% {
        transform: translateY(-10px) rotateX(2deg);
    }
}

/* 层叠卡片效果 */
.stacked-cards {
    position: relative;
    transform-style: preserve-3d;
}

.stacked-cards .card {
    position: absolute;
    transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
}

.stacked-cards .card:nth-child(1) {
    transform: translateZ(0px) rotateX(0deg);
    z-index: 3;
}

.stacked-cards .card:nth-child(2) {
    transform: translateZ(-10px) rotateX(-2deg) translateY(5px);
    z-index: 2;
    opacity: 0.9;
}

.stacked-cards .card:nth-child(3) {
    transform: translateZ(-20px) rotateX(-4deg) translateY(10px);
    z-index: 1;
    opacity: 0.8;
}

.stacked-cards:hover .card:nth-child(1) {
    transform: translateZ(20px) rotateX(5deg) translateY(-10px);
}

.stacked-cards:hover .card:nth-child(2) {
    transform: translateZ(10px) rotateX(2deg) translateY(-5px);
}

.stacked-cards:hover .card:nth-child(3) {
    transform: translateZ(0px) rotateX(0deg) translateY(0px);
}

/* 书页翻转效果 */
.page-flip {
    position: relative;
    width: 300px;
    height: 400px;
    margin: 20px;
    perspective: 1500px;
}

.page {
    position: absolute;
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    transition: transform 1.2s cubic-bezier(0.23, 1, 0.32, 1);
    transform-origin: left center;
    cursor: pointer;
}

.page.flipped {
    transform: rotateY(-180deg);
}

.page-front, .page-back {
    position: absolute;
    width: 100%;
    height: 100%;
    backface-visibility: hidden;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.page-front {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
}

.page-back {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transform: rotateY(180deg);
}

/* 立体图标效果 */
.icon-3d {
    position: relative;
    display: inline-block;
    transform-style: preserve-3d;
    transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.icon-3d::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 100%;
    height: 100%;
    background: currentColor;
    opacity: 0.3;
    transform: translateZ(-4px);
    border-radius: inherit;
    transition: all 0.3s ease;
}

.icon-3d:hover {
    transform: translateZ(8px) rotateX(10deg) rotateY(10deg);
}

.icon-3d:hover::before {
    transform: translateZ(-8px);
    opacity: 0.5;
}

/* 玻璃形态按钮 */
.glassmorphism-btn {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    color: white;
    padding: 12px 24px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.glassmorphism-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.glassmorphism-btn:hover::before {
    left: 100%;
}

.glassmorphism-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

/* 神经形态按钮 */
.neumorphism-btn {
    background: #f0f0f3;
    border: none;
    border-radius: 20px;
    color: #555;
    cursor: pointer;
    font-weight: 600;
    padding: 15px 30px;
    transition: all 0.3s ease;
    box-shadow: 
        9px 9px 16px rgba(163,177,198,0.6), 
        -9px -9px 16px rgba(255,255,255, 0.5);
}

.neumorphism-btn:hover {
    box-shadow: 
        inset 9px 9px 16px rgba(163,177,198,0.6), 
        inset -9px -9px 16px rgba(255,255,255, 0.5);
}

.neumorphism-btn:active {
    box-shadow: 
        inset 6px 6px 12px rgba(163,177,198,0.7), 
        inset -6px -6px 12px rgba(255,255,255, 0.6);
}

/* 磁性悬浮效果 */
.magnetic-hover {
    transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    cursor: pointer;
}

.magnetic-hover:hover {
    transform: scale(1.05) translateZ(10px);
    filter: brightness(1.1);
}

/* 视差滚动效果 */
.parallax-element {
    will-change: transform;
    transition: transform 0.1s ease-out;
}

.parallax-slow {
    transform: translateZ(-100px) scale(2);
}

.parallax-medium {
    transform: translateZ(-50px) scale(1.5);
}

.parallax-fast {
    transform: translateZ(0);
}

/* 呼吸灯效果 */
.breathing {
    animation: breathing 2s ease-in-out infinite;
}

@keyframes breathing {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.02);
        opacity: 0.8;
    }
}

/* 3D文字效果 */
.text-3d {
    font-weight: 900;
    text-transform: uppercase;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 
        1px 1px 0 rgba(102, 126, 234, 0.8),
        2px 2px 0 rgba(102, 126, 234, 0.6),
        3px 3px 0 rgba(102, 126, 234, 0.4),
        4px 4px 0 rgba(102, 126, 234, 0.2),
        5px 5px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.text-3d:hover {
    transform: translateY(-2px);
    text-shadow: 
        2px 2px 0 rgba(102, 126, 234, 0.8),
        4px 4px 0 rgba(102, 126, 234, 0.6),
        6px 6px 0 rgba(102, 126, 234, 0.4),
        8px 8px 0 rgba(102, 126, 234, 0.2),
        10px 10px 20px rgba(0, 0, 0, 0.3);
}

/* 响应式3D效果 */
@media (max-width: 768px) {
    .transform-3d {
        transform: none !important;
    }
    
    .card-3d:hover {
        transform: translateY(-5px) scale(1.02);
    }
    
    .perspective-container {
        perspective: none;
    }
}

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
    .transform-3d,
    .card-3d,
    .flip-card-inner,
    .btn-3d,
    .floating,
    .stacked-cards .card,
    .page {
        transition: none !important;
        animation: none !important;
        transform: none !important;
    }
}
