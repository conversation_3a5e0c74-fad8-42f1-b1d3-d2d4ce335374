/* 优化的动态岛样式 - 性能与美观并重 */

/* CSS变量定义 */
:root {
    /* 颜色系统 */
    --island-bg-primary: rgba(30, 30, 30, 0.85);
    --island-bg-secondary: rgba(0, 0, 0, 0.25);
    --island-bg-hover: rgba(0, 0, 0, 0.35);
    --island-border: rgba(255, 255, 255, 0.15);
    --island-text: #ffffff;
    --island-text-secondary: rgba(255, 255, 255, 0.8);
    
    /* 动画时长 */
    --animation-fast: 0.2s;
    --animation-normal: 0.35s;
    --animation-slow: 0.5s;
    
    /* 缓动函数 */
    --easing-bounce: cubic-bezier(0.68, -0.6, 0.32, 1.6);
    --easing-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --easing-sharp: cubic-bezier(0.4, 0, 0.2, 1);
    
    /* 尺寸 */
    --island-size: 42px;
    --island-padding: 10px;
    --island-gap: 12px;
    --island-border-radius: 21px;
    
    /* 阴影 */
    --shadow-primary: 0 8px 32px 0 rgba(31, 38, 135, 0.25);
    --shadow-hover: 0 12px 40px 0 rgba(31, 38, 135, 0.35);
    --shadow-glow-blue: 0 0 20px 2px rgba(0, 122, 255, 0.6);
    --shadow-glow-orange: 0 0 25px 3px rgba(255, 165, 0, 0.7);
}

/* 性能优化的基础样式 */
*,
*::before,
*::after {
    box-sizing: border-box;
}

/* 针对动画偏好的适配 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 主体样式 */
body {
    margin: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
    background-color: #FAF7F5;
    color: #333;
    min-height: 100vh;
    padding-top: 80px;
    transition: background-color var(--animation-normal) var(--easing-smooth);
    font-feature-settings: 'liga' 1, 'kern' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* 动态岛容器 */
#dynamic-island-container {
    position: fixed;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    background-color: var(--island-bg-primary);
    backdrop-filter: blur(20px) saturate(180%);
    -webkit-backdrop-filter: blur(20px) saturate(180%);
    border-radius: 35px;
    z-index: 1000;
    box-shadow: var(--shadow-primary);
    border: 1px solid var(--island-border);
    transition: all var(--animation-slow) var(--easing-smooth);
    gap: var(--island-gap);
    will-change: transform;
    contain: layout;
}

/* 岛屿基础样式 */
.island {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--island-padding);
    min-width: var(--island-size);
    height: var(--island-size);
    background-color: var(--island-bg-secondary);
    border-radius: var(--island-border-radius);
    cursor: pointer;
    transition: all var(--animation-normal) var(--easing-smooth);
    color: var(--island-text);
    overflow: hidden;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    contain: layout style;
}

/* 悬停效果 */
.island:hover {
    background-color: var(--island-bg-hover);
    transform: scale(1.05) translateY(-2px);
    box-shadow: var(--shadow-glow-blue);
}

/* 激活效果 */
.island:active {
    transform: scale(0.95) translateY(0);
    transition-duration: var(--animation-fast);
}

/* 图标样式 */
.island .icon {
    width: 22px;
    height: 22px;
    fill: currentColor;
    transition: transform var(--animation-normal) var(--easing-smooth),
                opacity var(--animation-fast) ease;
    flex-shrink: 0;
}

.island:hover .icon {
    opacity: 0.9;
}

.island:active .icon {
    transform: scale(0.85);
}

/* Logo岛屿特殊样式 */
.logo-island {
    background-color: rgba(255, 136, 0, 0.75);
    position: relative;
}

.logo-island::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
        rgba(255, 165, 0, 0.3) 0%, 
        rgba(255, 136, 0, 0.3) 50%, 
        rgba(255, 69, 0, 0.3) 100%);
    border-radius: inherit;
    opacity: 0;
    transition: opacity var(--animation-normal) ease;
}

.logo-island:hover {
    box-shadow: var(--shadow-glow-orange);
}

.logo-island:hover::before {
    opacity: 1;
}

.logo-svg {
    animation: logoBreath 6s infinite ease-in-out;
    animation-play-state: running;
}

@keyframes logoBreath {
    0%, 100% { 
        opacity: 0.85; 
        transform: scale(1) rotate(0deg); 
    }
    50% { 
        opacity: 1; 
        transform: scale(1.08) rotate(2deg); 
    }
}

/* 搜索岛屿 */
.search-island {
    transition: all var(--animation-slow) var(--easing-bounce);
    overflow: hidden;
}

.search-island.active {
    min-width: 200px;
    padding-left: 15px;
    padding-right: 15px;
    background-color: rgba(0, 0, 0, 0.4);
}

#search-input {
    background: transparent;
    border: none;
    outline: none;
    color: var(--island-text);
    font-size: 14px;
    font-family: inherit;
    width: 0;
    padding: 0 8px;
    margin-left: 0;
    opacity: 0;
    transition: width var(--animation-slow) var(--easing-bounce),
                opacity var(--animation-normal) 0.1s ease,
                margin-left var(--animation-normal) ease;
}

.search-island.active #search-input {
    width: 140px;
    opacity: 1;
    margin-left: 8px;
}

#search-input::placeholder {
    color: var(--island-text-secondary);
    font-size: 13px;
}

/* 导航岛屿 */
.nav-island {
    position: relative;
    overflow: hidden;
}

.nav-island::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transition: all var(--animation-normal) ease;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.nav-island:hover::after {
    width: 60px;
    height: 60px;
}

/* 用户岛屿 */
.user-island {
    position: relative;
}

.user-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: border-color var(--animation-normal) ease;
}

.user-island:hover .user-avatar {
    border-color: rgba(255, 255, 255, 0.8);
}

/* 工具提示 */
.island[data-tooltip] {
    position: relative;
}

.island[data-tooltip]::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--animation-normal) ease;
    pointer-events: none;
    z-index: 1001;
}

.island[data-tooltip]:hover::before {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-2px);
}

/* 下拉菜单样式 */
.dropdown-menu {
    position: absolute;
    top: calc(100% + 10px);
    right: 0;
    background: rgba(20, 20, 20, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    min-width: 200px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all var(--animation-normal) var(--easing-bounce);
    z-index: 1002;
    overflow: hidden;
}

.dropdown-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: var(--island-text);
    text-decoration: none;
    transition: background-color var(--animation-fast) ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.dropdown-item .icon {
    width: 16px;
    height: 16px;
    margin-right: 10px;
    opacity: 0.7;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #dynamic-island-container {
        width: 95%;
        max-width: 400px;
        padding: 6px;
        gap: 8px;
    }
    
    .island {
        min-width: 36px;
        height: 36px;
        padding: 8px;
    }
    
    .island .icon {
        width: 20px;
        height: 20px;
    }
    
    .search-island.active {
        min-width: 150px;
    }
    
    .search-island.active #search-input {
        width: 100px;
    }
}

@media (max-width: 480px) {
    body {
        padding-top: 70px;
    }
    
    #dynamic-island-container {
        top: 10px;
        padding: 4px;
        gap: 6px;
    }
    
    .island {
        min-width: 32px;
        height: 32px;
        padding: 6px;
    }
    
    .search-island.active {
        min-width: 120px;
    }
    
    .dropdown-menu {
        min-width: 180px;
        right: -10px;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --island-bg-primary: rgba(40, 40, 40, 0.9);
        --island-bg-secondary: rgba(60, 60, 60, 0.3);
        --island-bg-hover: rgba(80, 80, 80, 0.4);
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .island {
        border: 2px solid var(--island-text);
    }
    
    .island:hover {
        background-color: var(--island-text);
        color: #000;
    }
}

/* 性能优化类 */
.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

.will-change-transform {
    will-change: transform;
}

.will-change-auto {
    will-change: auto;
}

/* 动画入场效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.fade-in {
    animation: fadeInUp var(--animation-normal) var(--easing-smooth) both;
}

.bounce-in {
    animation: bounceIn var(--animation-slow) var(--easing-bounce) both;
}

/* 延迟动画 */
.fade-in:nth-child(1) { animation-delay: 0s; }
.fade-in:nth-child(2) { animation-delay: 0.1s; }
.fade-in:nth-child(3) { animation-delay: 0.2s; }
.fade-in:nth-child(4) { animation-delay: 0.3s; }
.fade-in:nth-child(5) { animation-delay: 0.4s; }
