# 项目上下文 (Project Context)
- **创建时间**: 2025-01-27 
- **协议版本**: ACSD Protocol v1.0
- **项目类型**: Web UI 开发项目

## 项目概述 (Project Overview)

### 核心功能
- **AI面试助手**: 多模态模拟面试评测智能体
- **动态岛屿UI**: 现代化的用户界面组件
- **模拟面试系统**: 个性化面试指导和职业发展建议
- **AI教练功能**: 智能对话和训练系统
- **技能练习**: 专业技能训练模块
- **PWA支持**: 离线使用和原生应用体验

### 技术栈
- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **架构**: 模块化JavaScript架构
- **UI设计**: 响应式设计，支持明暗主题
- **PWA**: Service Worker + Web App Manifest
- **性能**: 防抖、节流、GPU加速动画
- **无障碍**: ARIA标签、语义化HTML

## 项目结构 (Project Structure)

```
./
├── 新ui/                    # 主要UI项目目录
│   ├── dynamic_island_ui.html # 动态岛屿UI主页面
│   ├── enhanced-demo.html     # 增强演示页面
│   ├── advanced-features-demo.html # 高级功能演示
│   ├── css/                   # 样式文件目录
│   ├── js/                    # JavaScript文件目录
│   ├── style.css             # 主样式文件
│   ├── script.js             # 主脚本文件
│   ├── sw.js                 # Service Worker
│   ├── manifest.json         # PWA清单文件
│   └── README.md             # 项目说明
├── 新ui copy/               # UI项目备份/变体
├── 测试/                    # 测试相关文件
├── augment_workspace/       # ACSD协议工作空间
└── 提示词.txt              # 提示词文档
```

## 代码特点与规范 (Code Characteristics & Standards)

### 编码风格
- 使用现代JavaScript ES6+语法
- CSS采用现代布局技术(Flexbox, Grid)
- 响应式设计原则
- 组件化开发思路

### 架构模式
- **模块化管理器系统**: ArtboardManager, SearchManager, UserMenuManager等
- **工具函数库**: Utils工具集，包含防抖、节流等性能优化函数
- **配置系统**: 统一的CONFIG配置管理
- **事件驱动**: 基于事件的组件通信
- **PWA渐进增强**: Service Worker + 缓存策略

### 关键设计决策
- **原生Web技术**: 无框架依赖，使用现代JavaScript ES6+
- **性能优先**: 实现防抖、节流、GPU加速等优化
- **用户体验**: 响应式设计、无障碍访问、主题系统
- **PWA完整支持**: 离线功能、推送通知、应用快捷方式
- **模块化架构**: 高内聚低耦合的代码组织

## 重要依赖与配置 (Dependencies & Configuration)

### 核心依赖
- 无外部JavaScript框架依赖
- 使用现代浏览器原生API
- Service Worker用于PWA功能

### 关键配置
- manifest.json: PWA配置
- sw.js: Service Worker配置
- 响应式断点设计

## 开发规范 (Development Standards)

### 文件命名
- HTML文件: 小写字母，连字符分隔
- CSS文件: 小写字母，下划线分隔
- JS文件: 小写字母，下划线分隔

### 代码组织
- 样式文件按功能模块组织
- JavaScript按功能模块分离
- 资源文件统一管理

## 更新日志 (Update Log)

### v1.0 - 2025-01-27
- 建立项目上下文文档
- 采用ACSD Protocol v1.0
- 初始化项目结构分析
- 完善AI面试助手项目信息
- 集成PWA功能和模块化架构信息

### 项目历史版本
- **v2.0.0** (2024年12月): AI面试助手重大优化版本
  - 模块化架构重构
  - PWA功能完整实现
  - 性能优化30%提升
  - 用户体验全面改进
