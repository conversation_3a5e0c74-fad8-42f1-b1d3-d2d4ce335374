<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UniApp跨平台应用</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #2E8B57 0%, #3CB371 50%, #20B2AA 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .slide {
            width: 1200px;
            height: 800px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 60px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
        }

        .company-logo {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px 30px;
            border-radius: 10px;
            display: inline-block;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
        }

        .title {
            font-size: 48px;
            font-weight: bold;
            color: #F0FFF0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 24px;
            color: #E0FFE0;
            opacity: 0.9;
        }

        .content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            height: calc(100% - 200px);
        }

        .section {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 30px;
            border-left: 5px solid #90EE90;
        }

        .section h3 {
            font-size: 28px;
            color: #F0FFF0;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .section h3::before {
            content: "●";
            color: #90EE90;
            font-size: 20px;
            margin-right: 10px;
        }

        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }

        .tech-item {
            background: rgba(144, 238, 144, 0.3);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            border: 1px solid rgba(144, 238, 144, 0.5);
        }

        .feature-list {
            list-style: none;
        }

        .feature-list li {
            margin-bottom: 12px;
            padding-left: 20px;
            position: relative;
            font-size: 16px;
            line-height: 1.4;
        }

        .feature-list li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #90EE90;
            font-weight: bold;
            font-size: 18px;
        }

        .highlight-box {
            background: rgba(144, 238, 144, 0.2);
            border: 2px solid rgba(144, 238, 144, 0.4);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            text-align: center;
            font-weight: bold;
            color: #F0FFF0;
        }

        .grid-4 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 20px;
            height: 100%;
        }

        .mini-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            border-left: 3px solid #90EE90;
        }

        .mini-section h4 {
            font-size: 20px;
            color: #F0FFF0;
            margin-bottom: 15px;
        }

        .mini-section ul {
            list-style: none;
            font-size: 14px;
        }

        .mini-section li {
            margin-bottom: 8px;
            padding-left: 15px;
            position: relative;
        }

        .mini-section li::before {
            content: "▶";
            position: absolute;
            left: 0;
            color: #90EE90;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="slide">
        <div class="header">
            <div class="company-logo">TEXT COMPANY NAME & LOGO</div>
            <h1 class="title"> UniApp跨平台应用</h1>
            <p class="subtitle">一次开发，多端发布的移动应用解决方案</p>
        </div>

        <div class="content">
            <div class="section">
                <h3>技术选型</h3>
                <div class="tech-stack">
                    <span class="tech-item">Vue 3</span>
                    <span class="tech-item">TypeScript</span>
                    <span class="tech-item">Pinia状态管理</span>
                    <span class="tech-item">UnoCSS</span>
                    <span class="tech-item">Wot Design Uni</span>
                </div>
                <div class="highlight-box">
                    原子化CSS + 组件库<br>
                    兼顾开发效率与性能
                </div>

                <h3 style="margin-top: 30px;">平台支持</h3>
                <ul class="feature-list">
                    <li>一键发布至iOS、Android、H5</li>
                    <li>微信小程序等多平台</li>
                    <li>支持不同平台的原生能力调用</li>
                    <li>自适应不同屏幕尺寸和分辨率</li>
                </ul>
            </div>

            <div class="section">
                <div class="grid-4">
                    <div class="mini-section">
                        <h4>用户体验优化</h4>
                        <ul>
                            <li>离线支持功能</li>
                            <li>手势操作优化</li>
                            <li>无障碍设计</li>
                            <li>视障用户支持</li>
                        </ul>
                    </div>

                    <div class="mini-section">
                        <h4>性能优化</h4>
                        <ul>
                            <li>懒加载和代码分割</li>
                            <li>图片压缩和CDN加速</li>
                            <li>内存管理优化</li>
                            <li>避免内存泄漏</li>
                        </ul>
                    </div>

                    <div class="mini-section">
                        <h4>项目概述</h4>
                        <ul>
                            <li>跨平台移动应用</li>
                            <li>统一开发体验</li>
                            <li>高性能表现</li>
                            <li>现代化技术栈</li>
                        </ul>
                    </div>

                    <div class="mini-section">
                        <h4>项目发展</h4>
                        <ul>
                            <li>持续迭代优化</li>
                            <li>功能模块扩展</li>
                            <li>用户反馈驱动</li>
                            <li>技术栈升级</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
