<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能可视化报告系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            background: linear-gradient(45deg, #00C851 0%, #007E33 25%, #00FF41 50%, #00C851 75%, #004D40 100%);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            overflow: hidden;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .ppt-container {
            width: 95vw;
            height: 95vh;
            max-width: 1400px;
            max-height: 900px;
            background: linear-gradient(135deg, rgba(0, 200, 81, 0.9) 0%, rgba(0, 126, 51, 0.8) 100%);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 50px;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.3);
            position: relative;
        }

        .ppt-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            border-radius: 25px;
            pointer-events: none;
        }

        .header {
            text-align: center;
            margin-bottom: 50px;
            position: relative;
            z-index: 2;
        }

        .company-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #00FF41, #00C851);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            font-weight: bold;
            color: white;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        .title {
            font-size: 56px;
            font-weight: 900;
            color: #FFFFFF;
            text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5);
            margin-bottom: 15px;
            letter-spacing: 2px;
        }

        .subtitle {
            font-size: 28px;
            color: #E8F5E8;
            font-weight: 400;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 40px;
            height: calc(100% - 200px);
            position: relative;
            z-index: 2;
        }

        .module {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
            border-radius: 20px;
            padding: 35px;
            border: 2px solid rgba(255, 255, 255, 0.4);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .module:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            border-color: rgba(255, 255, 255, 0.6);
        }

        .module::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #00FF41 0%, #00C851 50%, #007E33 100%);
            border-radius: 20px 20px 0 0;
        }

        .module::after {
            content: '';
            position: absolute;
            top: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, rgba(0, 255, 65, 0.3), rgba(0, 200, 81, 0.3));
            border-radius: 50%;
            opacity: 0.5;
        }

        .module-number {
            position: absolute;
            top: 25px;
            right: 25px;
            width: 50px;
            height: 50px;
            background: linear-gradient(45deg, #00FF41, #00C851);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 22px;
            font-weight: 900;
            color: white;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
            z-index: 3;
        }

        .module-title {
            font-size: 26px;
            font-weight: 800;
            color: #FFFFFF;
            margin-bottom: 20px;
            padding-right: 70px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            line-height: 1.2;
        }

        .module-content {
            font-size: 16px;
            line-height: 1.8;
            color: #F0FFF0;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        }

        .feature-list {
            list-style: none;
            margin-top: 15px;
        }

        .feature-list li {
            margin-bottom: 12px;
            padding-left: 30px;
            position: relative;
            font-size: 15px;
            line-height: 1.6;
        }

        .feature-list li::before {
            content: '●';
            position: absolute;
            left: 0;
            color: #00FF41;
            font-weight: bold;
            font-size: 20px;
            top: -2px;
        }

        .tech-highlight {
            background: linear-gradient(45deg, rgba(0, 255, 65, 0.3), rgba(0, 200, 81, 0.3));
            padding: 4px 8px;
            border-radius: 6px;
            color: #FFFFFF;
            font-weight: bold;
            border: 1px solid rgba(0, 255, 65, 0.5);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .footer {
            position: absolute;
            bottom: 30px;
            right: 50px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
            z-index: 2;
        }

        .decorative-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
            border-radius: 25px;
        }

        .circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(0, 255, 65, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .circle:nth-child(1) {
            width: 100px;
            height: 100px;
            top: 10%;
            left: 5%;
            animation-delay: 0s;
        }

        .circle:nth-child(2) {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .circle:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 20%;
            left: 15%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="ppt-container">
        <div class="decorative-elements">
            <div class="circle"></div>
            <div class="circle"></div>
            <div class="circle"></div>
        </div>

        <div class="header">
            <div class="company-logo">AI</div>
            <h1 class="title">模块六：智能可视化报告系统</h1>
            <p class="subtitle">多维度智能分析 · 可视化数据洞察 · 个性化报告生成</p>
        </div>

        <div class="content-grid">
            <div class="module">
                <div class="module-number">01</div>
                <h3 class="module-title">个人能力报告</h3>
                <div class="module-content">
                    <ul class="feature-list">
                        <li>多维度能力雷达图，直观展示优势和短板</li>
                        <li>历史成长曲线，追踪能力提升轨迹</li>
                        <li>个性化改进建议，提供具体提升方案</li>
                    </ul>
                </div>
            </div>

            <div class="module">
                <div class="module-number">02</div>
                <h3 class="module-title">企业招聘分析</h3>
                <div class="module-content">
                    <ul class="feature-list">
                        <li>候选人对比分析，支持多人横向比较</li>
                        <li>岗位匹配度排序，智能推荐最佳人选</li>
                        <li>招聘效果统计，分析招聘ROI和成功率</li>
                    </ul>
                </div>
            </div>

            <div class="module">
                <div class="module-number">03</div>
                <h3 class="module-title">行业趋势洞察</h3>
                <div class="module-content">
                    <ul class="feature-list">
                        <li>人才市场趋势分析，预测未来需求</li>
                        <li>薪资水平对标，提供市场参考</li>
                        <li>技能热度排行，指导培训方向</li>
                    </ul>
                </div>
            </div>

            <div class="module">
                <div class="module-number">04</div>
                <h3 class="module-title">可视化技术</h3>
                <div class="module-content">
                    <ul class="feature-list">
                        <li>基于<span class="tech-highlight">ECharts</span>的丰富图表类型</li>
                        <li>交互式数据探索，支持钻取和筛选</li>
                        <li>响应式设计，适配不同设备</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="footer">
            智能可视化报告系统 - 数据驱动决策
        </div>
    </div>
</body>
</html>
