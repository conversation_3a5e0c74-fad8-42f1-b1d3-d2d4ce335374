<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能可视化报告系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #2E8B57 0%, #3CB371 50%, #20B2AA 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .ppt-container {
            width: 1200px;
            height: 800px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .title {
            font-size: 48px;
            font-weight: bold;
            color: #F0FFF0;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 24px;
            color: #E0FFE0;
            font-weight: 300;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
            gap: 30px;
            height: 600px;
        }

        .module {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .module:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }

        .module::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #00FF7F, #32CD32, #7FFF00);
        }

        .module-number {
            position: absolute;
            top: 15px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: bold;
            color: #F0FFF0;
        }

        .module-title {
            font-size: 22px;
            font-weight: bold;
            color: #F0FFF0;
            margin-bottom: 15px;
            padding-right: 50px;
        }

        .module-content {
            font-size: 14px;
            line-height: 1.6;
            color: #E0FFE0;
        }

        .feature-list {
            list-style: none;
            margin-top: 10px;
        }

        .feature-list li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .feature-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #00FF7F;
            font-weight: bold;
            font-size: 16px;
        }

        .tech-highlight {
            background: rgba(0, 255, 127, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            color: #F0FFF0;
            font-weight: bold;
        }

        .footer {
            position: absolute;
            bottom: 20px;
            right: 40px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }
    </style>
</head>
<body>
    <div class="ppt-container">
        <div class="header">
            <h1 class="title">模块六：智能可视化报告系统</h1>
            <p class="subtitle">多维度智能分析 · 可视化数据洞察 · 个性化报告生成</p>
        </div>

        <div class="content-grid">
            <div class="module">
                <div class="module-number">01</div>
                <h3 class="module-title">个人能力报告</h3>
                <div class="module-content">
                    <ul class="feature-list">
                        <li>多维度能力雷达图，直观展示优势和短板</li>
                        <li>历史成长曲线，追踪能力提升轨迹</li>
                        <li>个性化改进建议，提供具体提升方案</li>
                    </ul>
                </div>
            </div>

            <div class="module">
                <div class="module-number">02</div>
                <h3 class="module-title">企业招聘分析</h3>
                <div class="module-content">
                    <ul class="feature-list">
                        <li>候选人对比分析，支持多人横向比较</li>
                        <li>岗位匹配度排序，智能推荐最佳人选</li>
                        <li>招聘效果统计，分析招聘ROI和成功率</li>
                    </ul>
                </div>
            </div>

            <div class="module">
                <div class="module-number">03</div>
                <h3 class="module-title">行业趋势洞察</h3>
                <div class="module-content">
                    <ul class="feature-list">
                        <li>人才市场趋势分析，预测未来需求</li>
                        <li>薪资水平对标，提供市场参考</li>
                        <li>技能热度排行，指导培训方向</li>
                    </ul>
                </div>
            </div>

            <div class="module">
                <div class="module-number">04</div>
                <h3 class="module-title">可视化技术</h3>
                <div class="module-content">
                    <ul class="feature-list">
                        <li>基于<span class="tech-highlight">ECharts</span>的丰富图表类型</li>
                        <li>交互式数据探索，支持钻取和筛选</li>
                        <li>响应式设计，适配不同设备</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="footer">
            智能可视化报告系统 - 数据驱动决策
        </div>
    </div>
</body>
</html>
