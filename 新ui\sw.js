/**
 * Service Worker for AI面试助手
 * 提供离线支持和缓存管理
 * @version 1.0
 */

const CACHE_NAME = 'ai-interview-assistant-v1';
const STATIC_CACHE_NAME = 'ai-interview-static-v1';
const DYNAMIC_CACHE_NAME = 'ai-interview-dynamic-v1';

// 需要缓存的静态资源
const STATIC_ASSETS = [
    '/',
    '/dynamic_island_ui.html',
    '/css/enhanced-styles.css',
    '/script.js',
    '/manifest.json'
];

// 需要缓存的动态资源模式
const DYNAMIC_CACHE_PATTERNS = [
    /\.(?:png|jpg|jpeg|svg|gif|webp)$/,
    /\.(?:css|js)$/,
    /fonts\.googleapis\.com/,
    /fonts\.gstatic\.com/
];

// Service Worker 安装事件
self.addEventListener('install', event => {
    console.log('🔧 Service Worker 安装中...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE_NAME)
            .then(cache => {
                console.log('📦 缓存静态资源...');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('✅ 静态资源缓存完成');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('❌ 静态资源缓存失败:', error);
            })
    );
});

// Service Worker 激活事件
self.addEventListener('activate', event => {
    console.log('🚀 Service Worker 激活中...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        // 删除旧版本缓存
                        if (cacheName !== STATIC_CACHE_NAME && 
                            cacheName !== DYNAMIC_CACHE_NAME) {
                            console.log('🗑️ 删除旧缓存:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('✅ Service Worker 激活完成');
                return self.clients.claim();
            })
    );
});

// 网络请求拦截
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // 只处理同源请求和允许的外部资源
    if (url.origin === location.origin || shouldCacheExternalResource(url)) {
        event.respondWith(handleFetch(request));
    }
});

// 处理网络请求的策略
async function handleFetch(request) {
    const url = new URL(request.url);
    
    try {
        // 对于静态资源，优先使用缓存
        if (isStaticAsset(url.pathname)) {
            return await cacheFirst(request);
        }
        
        // 对于动态内容，优先使用网络
        if (isDynamicContent(url.pathname)) {
            return await networkFirst(request);
        }
        
        // 对于API请求，使用网络优先策略
        if (url.pathname.startsWith('/api/')) {
            return await networkFirst(request);
        }
        
        // 默认策略：缓存优先
        return await cacheFirst(request);
        
    } catch (error) {
        console.error('❌ 请求处理失败:', error);
        
        // 返回离线页面或默认响应
        if (request.destination === 'document') {
            return await caches.match('/offline.html') || 
                   new Response('离线模式 - 请检查网络连接', {
                       status: 503,
                       statusText: 'Service Unavailable'
                   });
        }
        
        throw error;
    }
}

// 缓存优先策略
async function cacheFirst(request) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
        // 后台更新缓存
        updateCacheInBackground(request);
        return cachedResponse;
    }
    
    return await fetchAndCache(request);
}

// 网络优先策略
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            await cacheResponse(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        const cachedResponse = await caches.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        throw error;
    }
}

// 获取并缓存资源
async function fetchAndCache(request) {
    const response = await fetch(request);
    
    if (response.ok) {
        await cacheResponse(request, response.clone());
    }
    
    return response;
}

// 缓存响应
async function cacheResponse(request, response) {
    const url = new URL(request.url);
    const cacheName = isStaticAsset(url.pathname) ? 
                     STATIC_CACHE_NAME : DYNAMIC_CACHE_NAME;
    
    const cache = await caches.open(cacheName);
    await cache.put(request, response);
}

// 后台更新缓存
function updateCacheInBackground(request) {
    fetch(request)
        .then(response => {
            if (response.ok) {
                return cacheResponse(request, response);
            }
        })
        .catch(error => {
            console.warn('⚠️ 后台缓存更新失败:', error);
        });
}

// 判断是否为静态资源
function isStaticAsset(pathname) {
    return STATIC_ASSETS.some(asset => pathname.endsWith(asset)) ||
           /\.(css|js|png|jpg|jpeg|gif|svg|webp|woff|woff2|ttf|eot)$/.test(pathname);
}

// 判断是否为动态内容
function isDynamicContent(pathname) {
    return pathname.includes('/api/') || 
           pathname.includes('/dynamic/') ||
           pathname.endsWith('.json');
}

// 判断是否应该缓存外部资源
function shouldCacheExternalResource(url) {
    return DYNAMIC_CACHE_PATTERNS.some(pattern => pattern.test(url.href));
}

// 消息处理
self.addEventListener('message', event => {
    const { type, payload } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'CLEAR_CACHE':
            clearAllCaches()
                .then(() => {
                    event.ports[0].postMessage({ success: true });
                })
                .catch(error => {
                    event.ports[0].postMessage({ success: false, error });
                });
            break;
            
        case 'GET_CACHE_SIZE':
            getCacheSize()
                .then(size => {
                    event.ports[0].postMessage({ size });
                })
                .catch(error => {
                    event.ports[0].postMessage({ error });
                });
            break;
    }
});

// 清除所有缓存
async function clearAllCaches() {
    const cacheNames = await caches.keys();
    await Promise.all(cacheNames.map(name => caches.delete(name)));
    console.log('🗑️ 所有缓存已清除');
}

// 获取缓存大小
async function getCacheSize() {
    const cacheNames = await caches.keys();
    let totalSize = 0;
    
    for (const name of cacheNames) {
        const cache = await caches.open(name);
        const requests = await cache.keys();
        
        for (const request of requests) {
            const response = await cache.match(request);
            if (response) {
                const blob = await response.blob();
                totalSize += blob.size;
            }
        }
    }
    
    return totalSize;
}

console.log('🎯 AI面试助手 Service Worker 已加载');
