# 任务: [任务标题]
- **文件**: `task_record.md`
- **路径**: `./augment_workspace/task_record.md`
- **创建时间**: [YYYY-MM-DD HH:MM:SS]
- **协议版本**: ACSD Protocol v1.0

## 任务描述
[用户原始需求]

## 复杂度评估
- **级别**: [Simple|Complex]
- **流程**: [简化|完整RUCIR-5]
- **预估工作量**: [小时]

## 1. 深度研究 (RESEARCH)
### 代码库分析
[codebase-retrieval结果摘要]

### 关键发现
[架构、约束、风险等]

## 2. 需求理解 (UNDERSTAND)
[需求澄清和技术可行性分析]

## 3. 方案创造 (CREATE)
### 候选方案
[不同实现路径的分析]

### 推荐方案
[最终选择及理由]

## 4. 精确实现 (IMPLEMENT)
### 实施清单
- [ ] 步骤1: [具体操作]
- [ ] 步骤2: [具体操作]

### 执行日志
[实时更新的详细执行记录]

## 5. 质量审查 (REVIEW)
### 交付物检查
[代码质量、功能完整性等]

### 项目上下文更新
[是否需要更新project_context.md]

---

## 使用说明
1. 每个新任务开始时复制此模板
2. 重命名为 `task_record_YYYYMMDD_HHMM.md`
3. 按照ACSD Protocol流程填写各部分
4. 任务完成后归档到 `./augment_workspace/completed_tasks/`
